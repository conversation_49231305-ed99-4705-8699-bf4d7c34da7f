<?php get_header(); ?>

<!-- Start Hero Area -->
<section class="hero-area style3">
    <div class="hero-inner">
        <div class="hero-overlay"></div>
        <div class="container">
            <div class="inner-content">
                <div class="row">
                    <div class="col-lg-12 col-12">
                        <div class="hero-text">
                            <h5 class="fade-in-down delay-1"><?php echo esc_html(get_theme_mod('zajel_hero_subtitle', 'Welcome to Zajel Arabic Academy')); ?></h5>
                            <h1 class="fade-in delay-2"><?php echo wp_kses_post(get_theme_mod('zajel_hero_title', 'Learn <span>Quran</span> & <span>Arabic</span> with Expert Teachers')); ?></h1>
                            <p class="fade-in-up delay-3"><?php echo esc_html(get_theme_mod('zajel_hero_description', 'Join our online academy to learn Quran memorization, Arabic language, and Islamic studies with experienced teachers from the comfort of your home.')); ?></p>
                            <div class="button style2 fade-in-up delay-4">
                                <a href="<?php echo esc_url(get_theme_mod('zajel_hero_button_url', '#')); ?>" class="btn hover-lift"><?php echo esc_html(get_theme_mod('zajel_hero_button_text', 'Start Free Trial Class')); ?></a>
                                <a href="<?php echo esc_url(get_theme_mod('zajel_hero_video_url', 'https://www.youtube.com/watch?v=r44RKWyfcFw')); ?>" class="glightbox video-button hover-grow"><i class="lni lni-play"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--/ End Hero Area -->

<!-- Start Why Choose Us Area -->
<section class="why-choose-us-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title text-center">
                    <span class="fade-in delay-1"><?php echo esc_html(get_theme_mod('zajel_why_subtitle', 'Why Choose Us')); ?></span>
                    <h2 class="fade-in delay-2"><?php echo esc_html(get_theme_mod('zajel_why_title', 'Why Zajel Arabic Institute?')); ?></h2>
                    <p class="fade-in-up delay-3"><?php echo esc_html(get_theme_mod('zajel_why_description', 'Discover what makes Zajel Arabic Institute the preferred choice for students seeking quality Arabic and Islamic education worldwide.')); ?></p>
                </div>
            </div>
        </div>

        <div class="row align-items-center">
            <!-- Left Side Image -->
            <div class="col-lg-6 col-md-12 col-12">
                <div class="why-choose-image fade-in-left delay-2">
                    <?php
                    $why_image = get_theme_mod('zajel_why_image');
                    if ($why_image) :
                    ?>
                        <img src="<?php echo esc_url($why_image); ?>" alt="Why Choose Zajel" class="img-fluid">
                    <?php else : ?>
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/why-choose-zajel-blue.svg" alt="Why Choose Zajel" class="img-fluid">
                    <?php endif; ?>

                    <!-- Floating Badge -->
                    <div class="experience-badge">
                        <span class="years"><?php echo esc_html(get_theme_mod('zajel_experience_years', '10')); ?>+</span>
                        <span class="text">Years of<br>Experience</span>
                    </div>
                </div>
            </div>

            <!-- Right Side Features -->
            <div class="col-lg-6 col-md-12 col-12">
                <div class="why-choose-content fade-in-right delay-3">
                    <div class="row features-grid">
                        <?php
                        $features = [
                            ['lni lni-users', 'Expert Teachers', 'Learn from qualified native Arabic speakers with years of teaching experience.'],
                            ['lni lni-laptop', 'Flexible Learning', 'Study anytime, anywhere with our flexible online learning platform.'],
                            ['lni lni-book', 'Comprehensive Curriculum', 'Our curriculum covers all aspects of Arabic language and Islamic studies.'],
                            ['lni lni-certificate', 'Recognized Certification', 'Receive internationally recognized certificates upon course completion.'],
                            ['lni lni-headphone-alt', 'Dedicated Support', 'Get personalized support from our dedicated student success team.'],
                            ['lni lni-world', 'Global Community', 'Join a diverse community of learners from around the world.'],
                        ];

                        for ($i = 0; $i < 6; $i++) :
                            $icon = get_theme_mod('zajel_feature_' . ($i+1) . '_icon', $features[$i][0]);
                            $title = get_theme_mod('zajel_feature_' . ($i+1) . '_title', $features[$i][1]);
                            $description = get_theme_mod('zajel_feature_' . ($i+1) . '_description', $features[$i][2]);
                            $delay = ($i % 3) + 1;
                            $animation = ($i < 3) ? 'fade-in-up' : 'fade-in-down';
                        ?>
                            <div class="col-md-6 col-12">
                                <div class="single-feature <?php echo $animation; ?> delay-<?php echo $delay; ?>">
                                    <div class="icon">
                                        <i class="<?php echo esc_attr($icon); ?>"></i>
                                    </div>
                                    <h4><?php echo esc_html($title); ?></h4>
                                    <p><?php echo esc_html($description); ?></p>
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Why Choose Us Area -->

<!-- Start Courses Area -->
<section class="courses style2 section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title">
                    <span class="fade-in delay-1"><?php echo esc_html(get_theme_mod('zajel_courses_subtitle', 'Featured Courses')); ?></span>
                    <h2 class="fade-in delay-2"><?php echo esc_html(get_theme_mod('zajel_courses_title', 'Our Popular Courses')); ?></h2>
                    <p class="fade-in-up delay-3"><?php echo esc_html(get_theme_mod('zajel_courses_description', 'Explore our most popular courses designed to help you master Arabic language, Quran recitation, and Islamic studies with expert guidance.')); ?></p>
                </div>
            </div>
        </div>
        <div class="single-head">
            <div class="row courses-slider" style="position: relative; overflow: hidden;">
                <?php
                $courses_query = new WP_Query(array(
                    'post_type' => 'course',
                    'posts_per_page' => 6,
                ));
                $delay = 2;
                if ($courses_query->have_posts()) :
                    while ($courses_query->have_posts()) : $courses_query->the_post();
                        // Get course details
                        $course_image = get_post_meta(get_the_ID(), 'course_image', true);
                        if (empty($course_image)) {
                            $course_image = get_template_directory_uri() . '/assets/images/default-course.svg';
                        }

                        // Get teacher information
                        $teacher_id = get_post_meta(get_the_ID(), 'course_teacher_id', true);
                        $teacher_name = '';
                        $teacher_image = '';

                        if ($teacher_id) {
                            $teacher = get_post($teacher_id);
                            if ($teacher) {
                                $teacher_name = $teacher->post_title;
                                $teacher_image = get_the_post_thumbnail_url($teacher_id, 'thumbnail');
                                if (!$teacher_image) {
                                    $teacher_image = get_template_directory_uri() . '/assets/images/default-avatar.svg';
                                }
                            }
                        }
                ?>
                        <div class="col-lg-4 col-md-6 col-12 course-slide">
                            <div class="single-course fade-in-up delay-<?php echo ($delay/2); ?> hover-lift">
                                <div class="course-image">
                                    <a href="<?php echo esc_url(str_replace('/courses/', '/course/', get_permalink())); ?>" style="display: block; width: 100%;">
                                        <img src="<?php echo esc_url($course_image); ?>" alt="<?php the_title(); ?>">
                                    </a>
                                    <?php if ($teacher_name) : ?>
                                    <div class="teacher-badge">
                                        <div class="teacher-image">
                                            <img src="<?php echo esc_url($teacher_image); ?>" alt="<?php echo esc_attr($teacher_name); ?>">
                                        </div>
                                        <span><?php echo esc_html($teacher_name); ?></span>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="content">
                                    <h3>
                                        <a href="<?php echo esc_url(str_replace('/courses/', '/course/', get_permalink())); ?>">
                                            <?php the_title(); ?>
                                        </a>
                                    </h3>
                                    <div class="excerpt">
                                        <?php echo wp_trim_words(get_the_excerpt(), 15); ?>
                                    </div>
                                    <div class="button">
                                        <a href="<?php echo esc_url(str_replace('/courses/', '/course/', get_permalink())); ?>" class="btn">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                <?php
                        $delay += 2;
                    endwhile;
                    wp_reset_postdata();
                else :
                ?>
                    <div class="col-12">
                        <p><?php esc_html_e('No courses found.', 'zajel'); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Controles de navegación para cursos -->
            <div class="courses-navigation">
                <div class="courses-nav-buttons">
                    <button id="courses-prev" class="courses-prev" aria-label="Previous courses">
                        <i class="lni lni-chevron-left"></i>
                    </button>
                    <button id="courses-next" class="courses-next" aria-label="Next courses">
                        <i class="lni lni-chevron-right"></i>
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="button" style="text-align: center; margin-top: 20px;">
                        <a href="<?php echo esc_url(home_url('/courses/')); ?>" class="btn hover-lift" style="background: linear-gradient(135deg, #1a5f8d 0%, #03355c 100%); color: #fff; padding: 12px 30px; border-radius: 30px; display: inline-block; font-weight: 600; transition: all 0.3s ease; border: none; text-decoration: none; box-shadow: 0 4px 15px rgba(3, 53, 92, 0.2);">
                            <?php echo esc_html(get_theme_mod('zajel_courses_button_text', 'Browse All Courses')); ?>
                            <i class="lni lni-arrow-right" style="margin-left: 8px;"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Courses Area -->

<!-- Start Mission & Vision Area -->
<section class="mission-vision-section">
    <!-- Decorative Elements -->
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/decoration-arabic.svg" alt="Decoration" class="decoration-image top-right">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/decoration-quran.svg" alt="Decoration" class="decoration-image bottom-left">

    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title text-center">
                    <span class="fade-in delay-1"><?php echo esc_html(get_theme_mod('zajel_mission_subtitle', 'Our Purpose')); ?></span>
                    <h2 class="fade-in delay-2"><?php echo esc_html(get_theme_mod('zajel_mission_title', 'Our Mission & Vision')); ?></h2>
                    <p class="fade-in-up delay-3"><?php echo esc_html(get_theme_mod('zajel_mission_quote', 'At Zajel Arabic Academy, we are committed to excellence in Arabic and Islamic education, guided by our core mission and vision.')); ?></p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Mission Card -->
            <div class="col-lg-6 col-md-6 col-12">
                <div class="mission-vision-card fade-in-left delay-2">
                    <div class="icon-box">
                        <i class="lni lni-book"></i>
                    </div>
                    <h3>Our Mission</h3>
                    <p><?php echo esc_html(get_theme_mod('zajel_mission_description', 'Our mission is to make Quranic education accessible to everyone around the world, regardless of their background or location. We strive to provide high-quality Arabic and Islamic education in a supportive and engaging online environment.')); ?></p>

                    <ul class="values-list">
                        <li><i class="lni lni-checkmark-circle"></i> <?php echo esc_html(get_theme_mod('zajel_mission_point_1', 'Provide accessible Arabic education worldwide')); ?></li>
                        <li><i class="lni lni-checkmark-circle"></i> <?php echo esc_html(get_theme_mod('zajel_mission_point_2', 'Deliver high-quality Quranic learning experiences')); ?></li>
                        <li><i class="lni lni-checkmark-circle"></i> <?php echo esc_html(get_theme_mod('zajel_mission_point_3', 'Create a supportive and engaging learning community')); ?></li>
                    </ul>
                </div>
            </div>

            <!-- Vision Card -->
            <div class="col-lg-6 col-md-6 col-12">
                <div class="mission-vision-card fade-in-right delay-3">
                    <div class="icon-box">
                        <i class="lni lni-eye"></i>
                    </div>
                    <h3>Our Vision</h3>
                    <p><?php echo esc_html(get_theme_mod('zajel_vision_description', 'Our vision is to become the leading global platform for Arabic and Islamic education, recognized for excellence, innovation, and cultural authenticity. We aim to empower students to connect with their faith and heritage through language.')); ?></p>

                    <ul class="values-list">
                        <li><i class="lni lni-checkmark-circle"></i> <?php echo esc_html(get_theme_mod('zajel_vision_point_1', 'Become a global leader in Arabic education')); ?></li>
                        <li><i class="lni lni-checkmark-circle"></i> <?php echo esc_html(get_theme_mod('zajel_vision_point_2', 'Innovate in teaching methods and technology')); ?></li>
                        <li><i class="lni lni-checkmark-circle"></i> <?php echo esc_html(get_theme_mod('zajel_vision_point_3', 'Preserve and promote authentic Islamic knowledge')); ?></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Values Section -->
        <div class="row mt-5">
            <div class="col-12 text-center">
                <h3 class="fade-in delay-4 gold-gradient-text" style="margin-bottom: 30px; font-size: 32px; font-weight: 700;"><?php echo esc_html(get_theme_mod('zajel_values_title', 'Our Core Values')); ?></h3>
                <p class="fade-in-up delay-4" style="max-width: 700px; margin: 0 auto 40px; color: #666;"><?php echo esc_html(get_theme_mod('zajel_values_description', 'These core values guide everything we do at Zajel Arabic Academy, from curriculum development to student interactions.')); ?></p>
            </div>
        </div>

        <!-- Value Items in a separate row for better mobile layout -->
        <div class="row values-grid">
            <?php
            $value_icons = ['lni lni-heart', 'lni lni-users', 'lni lni-graduation', 'lni lni-star'];
            $value_titles = ['Excellence', 'Community', 'Knowledge', 'Innovation'];
            $value_descriptions = [
                'Commitment to the highest standards in education and service.',
                'Building a supportive global community of learners.',
                'Preserving and sharing authentic Islamic knowledge.',
                'Embracing innovative teaching methods and technology.'
            ];

            for ($i = 1; $i <= 4; $i++) :
                $icon = get_theme_mod('zajel_value_' . $i . '_icon', $value_icons[$i-1]);
                $title = get_theme_mod('zajel_value_' . $i . '_title', $value_titles[$i-1]);
                $description = get_theme_mod('zajel_value_' . $i . '_description', $value_descriptions[$i-1]);
                $animation = ($i % 2 == 0) ? 'fade-in-right' : 'fade-in-left';
            ?>
                <div class="col-lg-3 col-md-6 col-sm-6 col-12">
                    <div class="core-value-card text-center <?php echo $animation; ?> delay-<?php echo $i; ?>">
                        <div class="icon-box mx-auto">
                            <i class="<?php echo esc_attr($icon); ?>"></i>
                        </div>
                        <h4><?php echo esc_html($title); ?></h4>
                        <p><?php echo esc_html($description); ?></p>
                    </div>
                </div>
            <?php endfor; ?>
        </div>
    </div>
</section>
<!-- End Mission & Vision Area -->



<!-- Start Achievement Area -->
<section class="our-achievement style3 section overlay" style="<?php
    $bg_color = get_theme_mod('zajel_achievement_bg_color', '#206B94'); // Updated to blue color
    $bg_image = get_theme_mod('zajel_achievement_bg_image', '');
    $styles = [];
    if ($bg_color) {
        $styles[] = 'background-color: ' . esc_attr($bg_color) . ';';
    }
    if ($bg_image) {
        $styles[] = 'background-image: url(' . esc_url($bg_image) . ');';
    }
    echo implode(' ', $styles);
?>">
    <div class="container">
        <div class="row">
            <?php for ($i = 1; $i <= 4; $i++) : ?>
                <div class="col-lg-3 col-md-3 col-12">
                    <div class="single-achievement fade-in-up delay-<?php echo $i; ?>">
                        <h3 class="counter"><span id="achieve<?php echo $i; ?>" class="countup" cup-end="<?php echo esc_attr(get_theme_mod('zajel_achievement_' . $i . '_number', '500')); ?>"><?php echo esc_html(get_theme_mod('zajel_achievement_' . $i . '_number', '500')); ?></span>+</h3>
                        <h4><?php echo esc_html(get_theme_mod('zajel_achievement_' . $i . '_title', 'Happy Clients')); ?></h4>
                    </div>
                </div>
            <?php endfor; ?>
        </div>
    </div>
</section>
<!-- End Achievement Area -->







<!-- Start Testimonials Area -->
<section class="testimonials section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title align-center">
                    <div class="section-icon" style="background-color: transparent;">
                        <i class="lni lni-quotation" style="color: #03355c;"></i>
                    </div>
                    <h2 style="color: #03355c;"><?php echo esc_html(get_theme_mod('zajel_testimonials_title', 'What Our Students Say')); ?></h2>
                    <p style="color: #555;"><?php echo esc_html(get_theme_mod('zajel_testimonials_description', 'Hear from our students about their experiences learning with Zajel Arabic Academy and how our courses have helped them achieve their goals.')); ?></p>
                </div>
            </div>
        </div>
        <div class="row testimonial-container" style="position: relative; justify-content: center;">
            <?php
            $args = array(
                'post_type' => 'testimonial',
                'posts_per_page' => 6,
            );
            $testimonials = new WP_Query($args);
            if ($testimonials->have_posts()) :
                while ($testimonials->have_posts()) : $testimonials->the_post();
                    $designation = get_post_meta(get_the_ID(), 'testimonial_designation', true) ?: 'Student';
                    ?>
                    <div class="col-lg-4 col-md-6 col-12" style="margin-bottom: 30px; padding: 0 15px;">
                        <div class="single-testimonial" style="background-color: #fff; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); overflow: hidden; height: 100%; display: flex; flex-direction: column;">
                            <div class="quote-icon" style="text-align: center; padding: 20px 0 0;">
                                <i class="lni lni-quotation" style="color: #1a5f8d; font-size: 30px;"></i>
                            </div>
                            <div class="text" style="flex: 1; padding: 20px 25px; background-color: #fff; position: relative; border-radius: 0; margin-bottom: 0;">
                                <p style="font-size: 14px; line-height: 1.6; color: #666;"><?php the_excerpt(); ?></p>
                            </div>
                            <div class="author" style="padding: 15px 25px 25px; background-color: #fff; display: flex; align-items: center;">
                                <div class="author-image" style="width: 60px; height: 60px; border-radius: 50%; overflow: hidden; margin-right: 15px; border: 3px solid rgba(3, 53, 92, 0.2); position: relative;">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <?php the_post_thumbnail('thumbnail', ['alt' => get_the_title(), 'style' => 'width: 100%; height: 100%; object-fit: cover;']); ?>
                                    <?php else : ?>
                                        <img src="https://via.placeholder.com/300x300" alt="<?php the_title(); ?>" style="width: 100%; height: 100%; object-fit: cover;">
                                    <?php endif; ?>
                                    <div class="quote-mark" style="position: absolute; bottom: 0; right: 0; width: 25px; height: 25px; background: #1a5f8d; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="lni lni-star-filled" style="font-size: 12px; color: #fff;"></i>
                                    </div>
                                </div>
                                <div class="author-info" style="text-align: left;">
                                    <h4 class="name" style="font-size: 16px; font-weight: 600; margin-bottom: 5px; color: #333;">
                                        <?php the_title(); ?>
                                        <span class="deg" style="font-size: 13px; display: block; font-weight: 400; color: #1a5f8d; margin-top: 3px;"><i class="lni lni-user"></i> <?php echo esc_html($designation); ?></span>
                                    </h4>
                                    <div class="rating" style="margin-top: 5px;">
                                        <i class="lni lni-star-filled" style="color: #1a5f8d; font-size: 12px; margin-right: 2px;"></i>
                                        <i class="lni lni-star-filled" style="color: #1a5f8d; font-size: 12px; margin-right: 2px;"></i>
                                        <i class="lni lni-star-filled" style="color: #1a5f8d; font-size: 12px; margin-right: 2px;"></i>
                                        <i class="lni lni-star-filled" style="color: #1a5f8d; font-size: 12px; margin-right: 2px;"></i>
                                        <i class="lni lni-star-filled" style="color: #1a5f8d; font-size: 12px; margin-right: 2px;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                endwhile;
                wp_reset_postdata();
                ?>
                <!-- Navigation will be added dynamically by JavaScript -->
            <?php else : ?>
                <div class="col-12">
                    <p style="text-align: center; padding: 30px; background: #fff; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">No testimonials available at the moment. Stay tuned for student reviews!</p>
                </div>
            <?php endif; ?>
        </div>
        </div>
    </div>
</section>
<!-- End Testimonials Area -->




<!-- Start Latest News Area -->
<section class="latest-news-area style2 section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title">
                    <span class="fade-in delay-1"><?php echo esc_html(get_theme_mod('zajel_news_subtitle', 'Our Blog')); ?></span>
                    <h2 class="fade-in delay-2"><?php echo esc_html(get_theme_mod('zajel_news_title', 'Latest Articles & News')); ?></h2>
                    <p class="fade-in-up delay-3"><?php echo esc_html(get_theme_mod('zajel_news_description', 'Stay updated with our latest articles, tips, and resources on Arabic language learning, Quran memorization, and Islamic education.')); ?></p>
                </div>
            </div>
        </div>

        <div class="row blog-posts-container">
            <?php
            // Query para obtener los artículos más recientes
            $news_query = new WP_Query(array(
                'post_type' => 'post',
                'posts_per_page' => 3,
                'ignore_sticky_posts' => 0, // Incluir sticky posts primero
                'orderby' => 'date',
                'order' => 'DESC' // Orden descendente (más recientes primero)
            ));

            if ($news_query->have_posts()) :
                $delay = 1;
                while ($news_query->have_posts()) : $news_query->the_post();
                    // Usar SVG como imagen de marcador de posición
                    $news_image = has_post_thumbnail() ? get_the_post_thumbnail_url() : get_template_directory_uri() . '/assets/images/blog-placeholder.svg';
                    $categories = get_the_category();
                    $category = !empty($categories) ? $categories[0]->name : 'Uncategorized';
                    $delay++;
                    if ($delay > 4) $delay = 2;

                    // Contar comentarios
                    $comment_count = get_comments_number();
            ?>
                    <div class="col-lg-4 col-md-6 col-12 blog-post-item">
                        <div class="single-news fade-in-up delay-<?php echo esc_attr($delay); ?>">
                            <div class="image">
                                <a href="<?php the_permalink(); ?>">
                                    <img class="thumb" src="<?php echo esc_url($news_image); ?>" alt="<?php the_title_attribute(); ?>">
                                </a>
                                <?php if (is_sticky()): ?>
                                <div class="featured-badge">
                                    <i class="lni lni-star-filled"></i> <?php esc_html_e('Featured', 'zajel'); ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="content-body">
                                <div class="meta-data">
                                    <ul>
                                        <li>
                                            <i class="lni lni-calendar"></i>
                                            <a href="<?php echo esc_url(get_day_link(get_post_time('Y'), get_post_time('m'), get_post_time('j'))); ?>"><?php echo get_the_date('F j, Y'); ?></a>
                                        </li>
                                        <li>
                                            <i class="lni lni-tag"></i>
                                            <a href="<?php echo esc_url(get_category_link($categories[0]->term_id)); ?>"><?php echo esc_html($category); ?></a>
                                        </li>
                                        <li>
                                            <i class="lni lni-comments"></i>
                                            <a href="<?php comments_link(); ?>">
                                                <?php
                                                if ($comment_count == 0) {
                                                    esc_html_e('No Comments', 'zajel');
                                                } elseif ($comment_count == 1) {
                                                    esc_html_e('1 Comment', 'zajel');
                                                } else {
                                                    echo sprintf(esc_html__('%d Comments', 'zajel'), $comment_count);
                                                }
                                                ?>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <h4 class="title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                                <p><?php echo wp_trim_words(get_the_excerpt(), 15); ?></p>
                                <div class="button">
                                    <a href="<?php the_permalink(); ?>" class="btn hover-lift"><?php esc_html_e('Read More', 'zajel'); ?> <i class="lni lni-arrow-right"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
            <?php
                endwhile;
                wp_reset_postdata();
            else :
            ?>
                <div class="col-12">
                    <div class="no-posts-found text-center">
                        <i class="lni lni-empty-file"></i>
                        <h4><?php esc_html_e('No Posts Found', 'zajel'); ?></h4>
                        <p><?php esc_html_e('Sorry, no blog posts have been published yet.', 'zajel'); ?></p>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Blog Navigation -->
        <div class="row">
            <div class="col-12">
                <div class="blog-navigation text-center fade-in-up delay-4">
                    <a href="<?php echo esc_url(home_url('/blog/')); ?>" class="btn">
                        <?php esc_html_e('View All Articles', 'zajel'); ?> <i class="lni lni-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Latest News Area -->

<!-- Start Clients Area -->
<!-- <div class="client-logo-section">
    <div class="container">
        <div class="client-logo-wrapper">
            <div class="client-logo-carousel d-flex align-items-center justify-content-between">
                <?php for ($i = 1; $i <= 6; $i++) : ?>
                    <div class="client-logo fade-in delay-<?php echo $i; ?>">
                        <?php
                        $client_image = get_theme_mod('zajel_client_' . $i . '_image');
                        if ($client_image) :
                        ?>
                            <img src="<?php echo esc_url($client_image); ?>" alt="Client <?php echo $i; ?>">
                        <?php else : ?>
                            <img src="https://via.placeholder.com/230x95" alt="Placeholder">
                        <?php endif; ?>
                    </div>
                <?php endfor; ?>
            </div>
        </div>
    </div>
</div> -->
<!-- End Clients Area -->

<?php get_footer(); ?>
