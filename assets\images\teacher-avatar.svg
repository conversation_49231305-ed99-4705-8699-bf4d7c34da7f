<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="400" height="500" viewBox="0 0 400 500" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1a5f8d" />
      <stop offset="100%" stop-color="#03355c" />
    </linearGradient>
    <pattern id="pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="rgba(255, 255, 255, 0.2)" />
    </pattern>
  </defs>

  <!-- Background -->
  <rect width="400" height="500" fill="url(#bg-gradient)" />

  <!-- Pattern overlay -->
  <rect width="400" height="500" fill="url(#pattern)" opacity="0.5" />

  <!-- Decorative elements -->
  <circle cx="50" cy="50" r="20" fill="rgba(255, 255, 255, 0.1)" />
  <circle cx="350" cy="450" r="30" fill="rgba(255, 255, 255, 0.1)" />

  <!-- Book icon - centered -->
  <g transform="translate(200, 250) scale(1.2)">
    <!-- Book cover -->
    <path d="M-60,-40 L60,-40 L60,40 L-60,40 Z" fill="#f5f5f5" />
    <path d="M-60,-40 L-60,40 L-50,30 L-50,-30 Z" fill="#ddd" />
    <path d="M-50,-30 L50,-30 L50,30 L-50,30 Z" fill="#fff" />

    <!-- Book pages/lines -->
    <path d="M-40,-20 L40,-20 M-40,-10 L30,-10 M-40,0 L40,0 M-40,10 L35,10 M-40,20 L25,20" stroke="#1a5f8d" stroke-width="3" stroke-linecap="round" />

    <!-- Arabic calligraphy-inspired decoration -->
    <path d="M-30,-25 C-20,-35 20,-35 30,-25" stroke="#03355c" stroke-width="2" fill="none" />
    <path d="M-25,25 C-15,35 15,35 25,25" stroke="#03355c" stroke-width="2" fill="none" />

    <!-- Small decorative elements -->
    <circle cx="-35" cy="-25" r="3" fill="#1a5f8d" />
    <circle cx="35" cy="-25" r="3" fill="#1a5f8d" />
    <circle cx="-35" cy="25" r="3" fill="#1a5f8d" />
    <circle cx="35" cy="25" r="3" fill="#1a5f8d" />
  </g>
</svg>
