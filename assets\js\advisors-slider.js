jQuery(document).ready(function($) {
    // Initialize variables
    var advisorContainer = $('#teachers .row:not(:first-child)');
    var advisorItems = advisorContainer.find('.col-lg-6');
    var totalItems = advisorItems.length;
    var currentIndex = 0;

    // Function to determine items per view based on screen size
    function getItemsPerView() {
        if (window.innerWidth < 768) {
            return 1; // Show 1 item on mobile
        } else {
            return 2; // Show 2 items on larger screens
        }
    }

    // Get initial items per view
    var itemsPerView = getItemsPerView();

    // Only setup if we have more than 1 advisor
    if (totalItems > 1) {
        // Create navigation dots
        var dotsHtml = '';
        for (var i = 0; i <= totalItems - itemsPerView; i++) {
            var activeClass = (i === 0) ? 'active' : '';
            dotsHtml += '<span class="advisor-dot ' + activeClass + '" data-index="' + i + '" style="width: ' + (i === 0 ? '12px' : '10px') + '; height: ' + (i === 0 ? '12px' : '10px') + '; background-color: ' + (i === 0 ? '#1a5f8d' : 'rgba(3, 53, 92, 0.3)') + '; border-radius: 50%; cursor: pointer; transition: all 0.3s ease; margin: 0 4px;"></span>';
        }

        // Create navigation HTML
        var navigationHtml = '<div class="advisor-navigation" style="width: 100%; display: flex; justify-content: center; align-items: center; margin-top: 30px;">';
        navigationHtml += '<div class="advisor-arrow prev" style="width: 40px; height: 40px; background-color: #fff; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; cursor: pointer; box-shadow: 0 3px 10px rgba(0,0,0,0.1); transition: all 0.3s ease;"><i class="lni lni-chevron-left" style="color: #1a5f8d;"></i></div>';
        navigationHtml += '<div class="advisor-dots" style="display: flex; gap: 8px; align-items: center;">' + dotsHtml + '</div>';
        navigationHtml += '<div class="advisor-arrow next" style="width: 40px; height: 40px; background-color: #fff; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 15px; cursor: pointer; box-shadow: 0 3px 10px rgba(0,0,0,0.1); transition: all 0.3s ease;"><i class="lni lni-chevron-right" style="color: #1a5f8d;"></i></div>';
        navigationHtml += '</div>';

        // Append navigation to container
        advisorContainer.after(navigationHtml);

        // Initial setup - show first set of advisors
        updateVisibleAdvisors();

        // Add touch swipe functionality
        var touchStartX = 0;
        var touchEndX = 0;
        var isSwiping = false;

        // Add a container wrapper for better touch handling
        advisorContainer.wrap('<div class="advisor-touch-container" style="overflow: hidden; position: relative;"></div>');
        var touchContainer = $('.advisor-touch-container');

        touchContainer.on('touchstart', function(e) {
            touchStartX = e.originalEvent.touches[0].clientX;
            isSwiping = true;
        });

        touchContainer.on('touchmove', function(e) {
            if (!isSwiping) return;
            // Optional: add visual feedback during swipe
        });

        touchContainer.on('touchend', function(e) {
            if (!isSwiping) return;
            touchEndX = e.originalEvent.changedTouches[0].clientX;
            handleSwipe();
            isSwiping = false;
        });

        // Also handle mouse events for desktop testing
        var isMouseDown = false;
        var mouseStartX = 0;
        var mouseEndX = 0;

        touchContainer.on('mousedown', function(e) {
            isMouseDown = true;
            mouseStartX = e.pageX;
            // Prevent text selection during swipe
            e.preventDefault();
        });

        touchContainer.on('mousemove', function(e) {
            if (!isMouseDown) return;
            // Optional: add visual feedback during swipe
        });

        touchContainer.on('mouseup', function(e) {
            if (!isMouseDown) return;
            mouseEndX = e.pageX;
            handleMouseSwipe();
            isMouseDown = false;
        });

        touchContainer.on('mouseleave', function(e) {
            if (!isMouseDown) return;
            mouseEndX = e.pageX;
            handleMouseSwipe();
            isMouseDown = false;
        });

        function handleMouseSwipe() {
            var swipeThreshold = 50; // Minimum distance to be considered a swipe

            if (mouseEndX < mouseStartX - swipeThreshold) {
                // Swipe left - next
                if (currentIndex < totalItems - itemsPerView) {
                    currentIndex++;
                    updateVisibleAdvisors();
                }
            } else if (mouseEndX > mouseStartX + swipeThreshold) {
                // Swipe right - previous
                if (currentIndex > 0) {
                    currentIndex--;
                    updateVisibleAdvisors();
                }
            }
        }

        function handleSwipe() {
            var swipeThreshold = 50; // Minimum distance to be considered a swipe

            if (touchEndX < touchStartX - swipeThreshold) {
                // Swipe left - next
                if (currentIndex < totalItems - itemsPerView) {
                    currentIndex++;
                    updateVisibleAdvisors();
                }
            } else if (touchEndX > touchStartX + swipeThreshold) {
                // Swipe right - previous
                if (currentIndex > 0) {
                    currentIndex--;
                    updateVisibleAdvisors();
                }
            }
        }

        // No swipe hint needed

        // Function to update which advisors are visible
        function updateVisibleAdvisors() {
            // Hide all advisors with animation
            advisorItems.fadeOut(100).promise().done(function() {
                // Show the current set of advisors with animation
                for (var i = 0; i < itemsPerView; i++) {
                    if (currentIndex + i < totalItems) {
                        advisorItems.eq(currentIndex + i).fadeIn(200);
                    }
                }
            });

            // Update dots
            $('.advisor-dot').removeClass('active').css({
                'width': '10px',
                'height': '10px',
                'background-color': 'rgba(3, 53, 92, 0.3)'
            });

            $('.advisor-dot[data-index="' + currentIndex + '"]').addClass('active').css({
                'width': '12px',
                'height': '12px',
                'background-color': '#1a5f8d'
            });

            // Update arrow states
            if (currentIndex === 0) {
                $('.advisor-arrow.prev').css('opacity', '0.5').addClass('disabled');
            } else {
                $('.advisor-arrow.prev').css('opacity', '1').removeClass('disabled');
            }

            if (currentIndex >= totalItems - itemsPerView) {
                $('.advisor-arrow.next').css('opacity', '0.5').addClass('disabled');
            } else {
                $('.advisor-arrow.next').css('opacity', '1').removeClass('disabled');
            }
        }

        // Handle dot click
        $(document).on('click', '.advisor-dot', function() {
            currentIndex = $(this).data('index');
            updateVisibleAdvisors();
        });

        // Handle arrow clicks - move one advisor at a time
        $(document).on('click', '.advisor-arrow.prev', function() {
            if (currentIndex > 0) {
                currentIndex--;
                updateVisibleAdvisors();
            }
        });

        $(document).on('click', '.advisor-arrow.next', function() {
            if (currentIndex < totalItems - itemsPerView) {
                currentIndex++;
                updateVisibleAdvisors();
            }
        });

        // Handle window resize
        $(window).on('resize', function() {
            var newItemsPerView = getItemsPerView();

            // Only update if items per view has changed
            if (newItemsPerView !== itemsPerView) {
                // Remove existing navigation
                $('.advisor-navigation').remove();

                // Update variables
                itemsPerView = newItemsPerView;
                currentIndex = 0; // Reset to first item

                // Recreate dots
                dotsHtml = '';
                for (var i = 0; i <= totalItems - itemsPerView; i++) {
                    var activeClass = (i === 0) ? 'active' : '';
                    dotsHtml += '<span class="advisor-dot ' + activeClass + '" data-index="' + i + '" style="width: ' + (i === 0 ? '12px' : '10px') + '; height: ' + (i === 0 ? '12px' : '10px') + '; background-color: ' + (i === 0 ? '#1a5f8d' : 'rgba(3, 53, 92, 0.3)') + '; border-radius: 50%; cursor: pointer; transition: all 0.3s ease; margin: 0 4px;"></span>';
                }

                // Recreate navigation
                navigationHtml = '<div class="advisor-navigation" style="width: 100%; display: flex; justify-content: center; align-items: center; margin-top: 30px;">';
                navigationHtml += '<div class="advisor-arrow prev" style="width: 40px; height: 40px; background-color: #fff; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; cursor: pointer; box-shadow: 0 3px 10px rgba(0,0,0,0.1); transition: all 0.3s ease;"><i class="lni lni-chevron-left" style="color: #1a5f8d;"></i></div>';
                navigationHtml += '<div class="advisor-dots" style="display: flex; gap: 8px; align-items: center;">' + dotsHtml + '</div>';
                navigationHtml += '<div class="advisor-arrow next" style="width: 40px; height: 40px; background-color: #fff; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 15px; cursor: pointer; box-shadow: 0 3px 10px rgba(0,0,0,0.1); transition: all 0.3s ease;"><i class="lni lni-chevron-right" style="color: #1a5f8d;"></i></div>';
                navigationHtml += '</div>';

                // Append navigation to container
                advisorContainer.after(navigationHtml);

                // Update visible advisors
                updateVisibleAdvisors();
            }
        });
    }
});
