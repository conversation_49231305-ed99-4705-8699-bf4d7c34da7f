/*======================================
	Footer CSS
========================================*/
.footer {
    background-color: $white;
    z-index: 2;
    position: relative;
    border-top: 1px solid #eee;

    &.style2 {
        .f-about {
            padding-right: 80px;
        }
    }


    .logo {
        margin-bottom: 26px;

        img {
            width: 170px;
        }
    }


    .f-about {
        p {
            font-size: 14px;
        }
    }

    .footer-social {
        margin-top: 35px;

        ul li {
            display: inline-block;
            margin-right: 4px;

            &:last-child {
                margin-right: 0;
            }

            a {
                font-size: 15px;
                display: block;
                background: transparent;
                border: none;
                color: #fff;
                position: relative;
                z-index: 3;
                height: 40px;
                width: 40px;
                line-height: 40px;
                text-align: center;
                border: 1px solid #eee;
                border-radius: 50%;
                color: $black;
                font-size: 13px;
                background-color: $gray;

                &:hover {
                    background-color: $theme-color;
                    color: $white;
                    border-color: transparent;
                }
            }

        }
    }

    .footer-middle {
        padding-bottom: 120px;
        padding-top: 90px;
    }

    .single-footer {
        margin-top: 30px;

        h3 {
            color: $black;
            font-size: 18px;
            font-weight: 600;
            position: relative;
            text-transform: capitalize;
            margin-bottom: 30px;
        }
    }

    .recent-blog {
        ul {
            li {
                margin-bottom: 20px;
                padding-bottom: 20px;
                border-bottom: 1px solid #eee;
                min-height: 95px;

                &:last-child {
                    margin: 0;
                    padding-bottom: 0;
                    border: none;
                }

                a {
                    position: relative;
                    padding-left: 90px;
                    color: #888;
                    font-weight: 500;
                    font-size: 14px;

                    &:hover {
                        color: $theme-color;
                    }

                    img {
                        width: 75px;
                        height: 75px;
                        position: absolute;
                        left: 0;
                        top: 0;
                        border-radius: 0;
                    }
                }

                .date {
                    padding-left: 90px;
                    color: #888;
                    font-weight: 500;
                    font-size: 12px;
                    display: block;
                    margin-top: 10px;

                    i {
                        color: $theme-color;
                        font-size: 13px;
                        display: inline-block;
                        margin-right: 6px;
                    }
                }
            }
        }
    }

    .f-link ul li {
        margin-bottom: 18px;
        position: relative;

        &:last-child {
            margin: 0;
        }

        a {
            display: inline-block;
            color: #888;
            font-size: 14px;
            font-weight: 400;
            position: relative;
            transition: all 0.3s ease;

            &:hover {
                color: $theme-color;
            }
        }
    }

    .footer-newsletter {
        .newsletter-form {
            margin-top: 30px;
        }

        input {
            height: 55px;
            width: 100%;
            background: $white;
            border: 1px solid #eee;
            border-radius: 0;
            padding: 0px 20px;
        }

        .button {
            margin-top: 10px;

            .btn {}
        }
    }

    .footer-bottom {
        padding: 30px 0;
        background-color: $theme-color;

        .inner {
            text-align: center;

            p {
                color: $white;
                font-size: 14px;

                a {
                    font-weight: 400;
                    display: inline-block;
                    margin-left: 6px;
                    color: $white;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }

}