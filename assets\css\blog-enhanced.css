/* Enhanced Blog Section Styles for Zajel Arabic Theme */
:root {
    --blue-light: #1a5f8d;
    --blue-medium: #0c4a77;
    --blue-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Blog Section Styles */
.latest-news-area {
    padding: 100px 0;
    background-color: #f9f9f9;
    position: relative;
    overflow: hidden;
}

.latest-news-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a5f8d' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 0.5;
    z-index: 0;
}

.latest-news-area .container {
    position: relative;
    z-index: 1;
}

/* Section Title */
.latest-news-area .section-title {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
}

.latest-news-area .section-title span {
    color: var(--blue-light);
    font-size: 16px;
    font-weight: 600;
    display: block;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.latest-news-area .section-title h2 {
    font-size: 36px;
    font-weight: 800;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
    color: #333;
}

.latest-news-area .section-title h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background: linear-gradient(to right, var(--blue-dark), var(--blue-light));
}

.latest-news-area .section-title p {
    max-width: 650px;
    margin: 0 auto;
    color: #666;
    font-size: 16px;
    line-height: 1.7;
}

/* Blog Card Styles */
.single-news {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
    height: 100%;
    border: none !important;
    display: flex;
    flex-direction: column;
}

.single-news:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.single-news .image {
    position: relative;
    overflow: hidden;
}

.single-news .image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,0.3));
    z-index: 1;
    opacity: 0;
    transition: var(--transition-medium);
}

.single-news:hover .image::before {
    opacity: 1;
}

.single-news .image img.thumb {
    width: 100%;
    height: 240px;
    object-fit: cover;
    transition: var(--transition-medium);
}

.single-news:hover .image img.thumb {
    transform: scale(1.05);
}

.single-news .content-body {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

/* Meta Data */
.single-news .meta-data {
    margin-bottom: 15px;
}

.single-news .meta-data ul {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    list-style: none;
}

.single-news .meta-data ul li {
    margin-right: 15px;
    font-size: 13px;
    display: flex;
    align-items: center;
    position: relative;
}

.single-news .meta-data ul li:not(:last-child):after {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 12px;
    background-color: #ddd;
}

.single-news .meta-data ul li:last-child {
    margin-right: 0;
}

.single-news .meta-data ul li i {
    color: var(--blue-light);
    margin-right: 5px;
    font-size: 16px;
}

.single-news .meta-data ul li a {
    color: #666;
    transition: var(--transition-fast);
}

.single-news .meta-data ul li a:hover {
    color: var(--blue-light);
}

/* Featured Badge */
.single-news .image {
    position: relative;
}

.single-news .featured-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    color: #fff;
    padding: 5px 12px;
    border-radius: 30px;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    z-index: 2;
}

.single-news .featured-badge i {
    margin-right: 5px;
    font-size: 14px;
    color: #fff;
}

/* Post Title */
.single-news .title {
    margin-bottom: 15px;
    line-height: 1.4;
}

.single-news .title a {
    color: #333;
    font-size: 20px;
    font-weight: 700;
    transition: var(--transition-fast);
    text-decoration: none;
}

.single-news .title a:hover {
    color: var(--blue-light);
}

.single-news.big .title a {
    font-size: 24px;
}

/* Post Excerpt */
.single-news p {
    color: #666;
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* Read More Button */
.single-news .button {
    margin-top: auto;
}

.single-news .button .btn {
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    color: #fff;
    padding: 10px 25px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    transition: var(--transition-fast);
    border: none;
    display: inline-block;
    text-decoration: none;
    box-shadow: 0 4px 10px rgba(3, 53, 92, 0.2);
}

.single-news .button .btn:hover {
    background: linear-gradient(135deg, var(--blue-dark), var(--blue-light));
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(3, 53, 92, 0.3);
}

/* Featured Post */
.single-news.big {
    position: relative;
}

.single-news.big .image img.thumb {
    height: 350px;
}

/* Blog Navigation */
.blog-navigation {
    margin-top: 40px;
    text-align: center;
}

.blog-navigation .btn {
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    color: #fff;
    padding: 12px 30px;
    border-radius: 30px;
    font-size: 16px;
    font-weight: 600;
    transition: var(--transition-fast);
    border: none;
    display: inline-block;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(3, 53, 92, 0.2);
}

.blog-navigation .btn i {
    margin-left: 8px;
    transition: var(--transition-fast);
}

.blog-navigation .btn:hover {
    background: linear-gradient(135deg, var(--blue-dark), var(--blue-light));
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(3, 53, 92, 0.3);
}

.blog-navigation .btn:hover i {
    transform: translateX(5px);
}

/* Blog Slider Navigation */
.blog-slider-nav {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    gap: 15px;
}

.blog-slider-nav button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    color: #fff;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: 0 3px 10px rgba(3, 53, 92, 0.2);
}

.blog-slider-nav button:hover {
    transform: scale(1.1);
    background: linear-gradient(135deg, var(--blue-dark), var(--blue-light));
    box-shadow: 0 5px 15px rgba(3, 53, 92, 0.3);
}

.blog-slider-nav button i {
    font-size: 16px;
}

/* No Posts Found */
.no-posts-found {
    background-color: #fff;
    border-radius: 10px;
    padding: 50px 30px;
    box-shadow: var(--shadow-small);
    margin: 20px 0;
    transition: var(--transition-medium);
}

.no-posts-found i {
    font-size: 60px;
    color: var(--blue-light);
    margin-bottom: 20px;
    display: block;
}

.no-posts-found h4 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
}

.no-posts-found p {
    color: #666;
    font-size: 16px;
    max-width: 500px;
    margin: 0 auto;
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .latest-news-area {
        padding: 80px 0;
    }

    .latest-news-area .section-title h2 {
        font-size: 30px;
    }

    .single-news.big .image img.thumb {
        height: 300px;
    }

    .blog-post-item {
        margin-bottom: 30px;
    }
}

@media only screen and (max-width: 767px) {
    .latest-news-area {
        padding: 60px 0;
    }

    .latest-news-area .section-title {
        margin-bottom: 40px;
    }

    .latest-news-area .section-title h2 {
        font-size: 26px;
    }

    .single-news {
        margin-bottom: 30px;
    }

    .single-news .title a {
        font-size: 18px;
    }

    .single-news.big .title a {
        font-size: 22px;
    }

    .single-news .meta-data ul {
        justify-content: center;
    }

    .single-news .meta-data ul li {
        margin-bottom: 10px;
    }

    .blog-navigation .btn {
        padding: 10px 20px;
        font-size: 14px;
    }
}

@media only screen and (max-width: 575px) {
    .latest-news-area {
        padding: 50px 0;
    }

    .latest-news-area .section-title h2 {
        font-size: 24px;
    }

    .single-news .image img.thumb {
        height: 200px;
    }

    .single-news.big .image img.thumb {
        height: 220px;
    }

    .single-news .meta-data ul li {
        margin-right: 10px;
    }

    .single-news .meta-data ul li:not(:last-child):after {
        display: none;
    }

    .no-posts-found {
        padding: 30px 20px;
    }

    .no-posts-found i {
        font-size: 50px;
    }

    .no-posts-found h4 {
        font-size: 20px;
    }
}
