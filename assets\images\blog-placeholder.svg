<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="450" viewBox="0 0 800 450" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <rect width="800" height="450" fill="url(#bg-gradient)"/>

  <!-- Decorative pattern -->
  <path opacity="0.05" d="M0 0H800V450H0V0Z" fill="url(#pattern)"/>

  <!-- Center logo/icon -->
  <g transform="translate(400, 225) scale(1.2)">
    <!-- Outer circle -->
    <circle cx="0" cy="0" r="80" fill="url(#circle-gradient)" filter="url(#shadow)"/>

    <!-- Arabic book icon -->
    <g transform="translate(-40, -40) scale(0.8)">
      <!-- Book base -->
      <path d="M30 20H110C115.523 20 120 24.4772 120 30V110C120 115.523 115.523 120 110 120H30C24.4772 120 20 115.523 20 110V30C20 24.4772 24.4772 20 30 20Z" fill="#FFFFFF" filter="url(#inner-shadow)"/>

      <!-- Book pages -->
      <path d="M35 30H105C107.761 30 110 32.2386 110 35V105C110 107.761 107.761 110 105 110H35C32.2386 110 30 107.761 30 105V35C30 32.2386 32.2386 30 35 30Z" fill="#F5F5F5"/>

      <!-- Arabic text lines -->
      <path d="M90 50H50C48.8954 50 48 49.1046 48 48C48 46.8954 48.8954 46 50 46H90C91.1046 46 92 46.8954 92 48C92 49.1046 91.1046 50 90 50Z" fill="#03355c"/>
      <path d="M85 65H55C53.8954 65 53 64.1046 53 63C53 61.8954 53.8954 61 55 61H85C86.1046 61 87 61.8954 87 63C87 64.1046 86.1046 65 85 65Z" fill="#03355c"/>
      <path d="M80 80H60C58.8954 80 58 79.1046 58 78C58 76.8954 58.8954 76 60 76H80C81.1046 76 82 76.8954 82 78C82 79.1046 81.1046 80 80 80Z" fill="#03355c"/>
      <path d="M75 95H65C63.8954 95 63 94.1046 63 93C63 91.8954 63.8954 91 65 91H75C76.1046 91 77 91.8954 77 93C77 94.1046 76.1046 95 75 95Z" fill="#03355c"/>
    </g>
  </g>

  <!-- Zajel text -->
  <text x="400" y="340" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF" text-anchor="middle">Zajel Arabic Academy</text>

  <!-- Filters and gradients definitions -->
  <defs>
    <!-- Background gradient -->
    <linearGradient id="bg-gradient" x1="0" y1="0" x2="800" y2="450" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1a5f8d"/>
      <stop offset="1" stop-color="#03355c"/>
    </linearGradient>

    <!-- Circle gradient -->
    <linearGradient id="circle-gradient" x1="-80" y1="-80" x2="80" y2="80" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0c4a77"/>
      <stop offset="1" stop-color="#03355c"/>
    </linearGradient>

    <!-- Pattern for background -->
    <pattern id="pattern" patternUnits="userSpaceOnUse" width="60" height="60" patternTransform="scale(2) rotate(0)">
      <path d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z" fill="#FFFFFF"/>
    </pattern>

    <!-- Shadow for main circle -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="10" flood-opacity="0.3"/>
    </filter>

    <!-- Inner shadow for book -->
    <filter id="inner-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-opacity="0.1"/>
    </filter>
  </defs>
</svg>
