<?php
/**
 * The template for displaying blog posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Zajel_Arabic
 */

get_header();
?>

<!-- Start Hero Area -->
<section class="hero-area style2">
    <div class="hero-inner">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-12">
                    <div class="breadcrumbs-content text-center">
                        <div class="page-icon wow zoomIn" data-wow-delay=".2s">
                            <i class="lni lni-book"></i>
                        </div>
                        <h1 class="page-title wow fadeInUp" data-wow-delay=".4s">
                            <?php
                            if (is_home() && !is_front_page()) :
                                single_post_title();
                            else :
                                echo esc_html__('Our Blog', 'zajel');
                            endif;
                            ?>
                        </h1>
                        <p class="wow fadeInUp" data-wow-delay=".6s"><?php echo esc_html__('Explore our latest articles and insights on Arabic language, Islamic studies, and more', 'zajel'); ?></p>
                        <ul class="breadcrumb-nav wow fadeInUp" data-wow-delay=".8s">
                            <li><a href="<?php echo esc_url(home_url('/')); ?>"><i class="lni lni-home"></i> <?php echo esc_html__('Home', 'zajel'); ?></a></li>
                            <li><i class="lni lni-chevron-right"></i></li>
                            <li><?php echo esc_html__('Blog', 'zajel'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Hero Area -->

<!-- Start Blog Area -->
<section class="blog-area section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-md-12 col-12">
                <div class="blog-posts">
                    <?php if (have_posts()) : ?>
                        <?php
                        // Check if we have a sticky post to feature
                        $sticky_posts = get_option('sticky_posts');
                        if (!empty($sticky_posts)) :
                            $sticky_args = array(
                                'post__in' => $sticky_posts,
                                'posts_per_page' => 1,
                                'ignore_sticky_posts' => 1
                            );
                            $sticky_query = new WP_Query($sticky_args);

                            if ($sticky_query->have_posts()) :
                                while ($sticky_query->have_posts()) : $sticky_query->the_post();
                        ?>
                                <div class="featured-post wow fadeInUp" data-wow-delay=".2s">
                                    <?php get_template_part('template-parts/content', 'featured'); ?>
                                </div>
                        <?php
                                endwhile;
                                wp_reset_postdata();
                            endif;
                        endif;
                        ?>

                        <div class="row">
                            <?php
                            /* Start the Loop */
                            $delay = 0.2;
                            $post_counter = 0;

                            // If we displayed a sticky post, exclude it from the main query
                            if (!empty($sticky_posts) && isset($sticky_query) && $sticky_query->have_posts()) {
                                // Skip the sticky post if it's in the main loop
                                while (have_posts()) :
                                    the_post();
                                    if (in_array(get_the_ID(), $sticky_posts)) {
                                        continue;
                                    }
                                    $post_counter++;
                            ?>
                                    <div class="col-12 wow fadeInUp" data-wow-delay="<?php echo esc_attr($delay); ?>s">
                                        <?php get_template_part('template-parts/content', get_post_type()); ?>
                                    </div>
                            <?php
                                    $delay += 0.2;
                                    if ($delay > 0.8) $delay = 0.2;
                                endwhile;
                            } else {
                                // No sticky post was displayed, show all posts
                                while (have_posts()) :
                                    the_post();
                                    $post_counter++;
                            ?>
                                    <div class="col-12 wow fadeInUp" data-wow-delay="<?php echo esc_attr($delay); ?>s">
                                        <?php get_template_part('template-parts/content', get_post_type()); ?>
                                    </div>
                            <?php
                                    $delay += 0.2;
                                    if ($delay > 0.8) $delay = 0.2;
                                endwhile;
                            }
                            ?>
                        </div>

                        <?php if ($post_counter > 0) : ?>
                            <div class="pagination-area wow fadeInUp" data-wow-delay=".2s">
                                <?php
                                if (function_exists('zajel_pagination')) :
                                    zajel_pagination();
                                else :
                                    the_posts_navigation();
                                endif;
                                ?>
                            </div>
                        <?php endif; ?>

                    <?php else : ?>

                        <?php get_template_part('template-parts/content', 'none'); ?>

                    <?php endif; ?>
                </div>
            </div>

            <div class="col-lg-4 col-md-12 col-12">
                <div class="blog-sidebar wow fadeInUp" data-wow-delay=".4s">
                    <?php get_sidebar(); ?>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Blog Area -->

<?php
get_footer();
