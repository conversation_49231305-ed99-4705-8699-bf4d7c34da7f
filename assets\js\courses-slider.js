document.addEventListener('DOMContentLoaded', function() {
    // Configuración para el slider de cursos
    initCoursesSlider();

    // Inicializar el slider de cursos
    function initCoursesSlider() {
        const coursesSlider = document.querySelector('.courses-slider');
        if (!coursesSlider) return;

        const courseSlides = document.querySelectorAll('.course-slide');
        if (courseSlides.length <= 1) return;

        const prevBtn = document.getElementById('courses-prev');
        const nextBtn = document.getElementById('courses-next');

        let currentIndex = 0;
        let slidesToShow = getSlidesToShow();

        // Configuración inicial
        updateSlides();

        // Botones de navegación
        if (prevBtn) {
            prevBtn.addEventListener('click', function() {
                currentIndex = Math.max(currentIndex - 1, 0);
                updateSlides();
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', function() {
                currentIndex = Math.min(currentIndex + 1, courseSlides.length - slidesToShow);
                updateSlides();
            });
        }

        // Actualizar en cambio de tamaño de ventana
        window.addEventListener('resize', function() {
            slidesToShow = getSlidesToShow();
            currentIndex = Math.min(currentIndex, courseSlides.length - slidesToShow);
            updateSlides();
        });

        // Soporte para deslizamiento táctil
        let touchStartX = 0;
        let touchEndX = 0;

        coursesSlider.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        }, { passive: true });

        coursesSlider.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        }, { passive: true });

        function handleSwipe() {
            const swipeThreshold = 50;
            if (touchEndX < touchStartX - swipeThreshold) {
                // Deslizar a la izquierda
                if (currentIndex < courseSlides.length - slidesToShow) {
                    currentIndex++;
                    updateSlides();
                }
            }
            if (touchEndX > touchStartX + swipeThreshold) {
                // Deslizar a la derecha
                if (currentIndex > 0) {
                    currentIndex--;
                    updateSlides();
                }
            }
        }

        // Determinar cuántos slides mostrar según el ancho de la pantalla
        function getSlidesToShow() {
            if (window.innerWidth >= 992) {
                return 3; // Pantallas grandes
            } else if (window.innerWidth >= 768) {
                return 2; // Pantallas medianas
            } else {
                return 1; // Pantallas pequeñas
            }
        }

        // Actualizar la visibilidad de los slides
        function updateSlides() {
            // Aplicar transición suave
            courseSlides.forEach(slide => {
                slide.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            });

            // Actualizar visibilidad y posición de los slides
            courseSlides.forEach((slide, index) => {
                if (index >= currentIndex && index < currentIndex + slidesToShow) {
                    // Slides visibles
                    slide.style.display = 'block';
                    slide.style.opacity = '1';
                    slide.style.transform = 'translateX(0)';
                } else {
                    // Slides ocultos
                    slide.style.display = 'none';
                    slide.style.opacity = '0';
                }
            });

            // Actualizar estado de los botones
            if (prevBtn) {
                prevBtn.disabled = currentIndex === 0;
                prevBtn.classList.toggle('disabled', currentIndex === 0);
            }

            if (nextBtn) {
                const isLastSlide = currentIndex >= courseSlides.length - slidesToShow;
                nextBtn.disabled = isLastSlide;
                nextBtn.classList.toggle('disabled', isLastSlide);
            }
        }
    }
});
