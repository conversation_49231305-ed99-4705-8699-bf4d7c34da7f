<?php
/*
Template Name: About Us
*/
get_header();
?>

<!-- Start Breadcrumbs -->
<div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <div class="page-icon wow zoomIn" data-wow-delay=".2s">
                        <i class="lni lni-information"></i>
                    </div>
                    <h1 class="page-title wow fadeInUp" data-wow-delay=".4s"><?php the_title(); ?></h1>
                    <p class="wow fadeInUp" data-wow-delay=".6s">Learn about Zajel Arabic's mission to empower non-Arabic speakers with Arabic language, Islamic studies, and Quran memorization courses.</p>
                </div>
                <ul class="breadcrumb-nav wow fadeInUp" data-wow-delay=".8s">
                    <li><a href="<?php echo esc_url(home_url('/')); ?>"><i class="lni lni-home"></i> Home</a></li>
                    <li><i class="lni lni-chevron-right"></i></li>
                    <li><?php the_title(); ?></li>
                </ul>
            </div>
        </div>

        <!-- Animated Elements -->
        <div class="animated-circle circle-1 wow fadeIn" data-wow-delay=".3s"></div>
        <div class="animated-circle circle-2 wow fadeIn" data-wow-delay=".5s"></div>
        <div class="animated-square square-1 wow fadeIn" data-wow-delay=".7s"></div>
        <div class="animated-square square-2 wow fadeIn" data-wow-delay=".9s"></div>
        <div class="animated-circle circle-3 pulse" data-wow-delay="1.1s"></div>
    </div>
</div>
<!-- End Breadcrumbs -->

<!-- Start About Us Area -->
<section class="about-us section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 col-12">
                <div class="about-left">
                    <div class="about-title align-left">
                        <div class="icon-box wow zoomIn" data-wow-delay=".2s">
                            <i class="lni lni-book"></i>
                        </div>
                        <span class="wow fadeInDown" data-wow-delay=".3s">About Zajel Arabic</span>
                        <h2 class="wow fadeInUp" data-wow-delay=".4s">Welcome to Our Learning Community</h2>
                        <p class="wow fadeInUp" data-wow-delay=".6s">Zajel Arabic is dedicated to teaching non-Arabic speakers the beauty of the Arabic language, Islamic studies, and Quran memorization. Our mission is to provide accessible, high-quality education with experienced instructors worldwide.</p>

                        <div class="about-features">
                            <div class="feature-item wow fadeInUp" data-wow-delay=".7s">
                                <div class="feature-icon">
                                    <i class="lni lni-graduation"></i>
                                </div>
                                <div class="feature-content">
                                    <h5>Expert Teachers</h5>
                                    <p>Learn from qualified native Arabic speakers with years of teaching experience.</p>
                                </div>
                            </div>

                            <div class="feature-item wow fadeInUp" data-wow-delay=".8s">
                                <div class="feature-icon">
                                    <i class="lni lni-laptop"></i>
                                </div>
                                <div class="feature-content">
                                    <h5>Flexible Learning</h5>
                                    <p>Study anytime, anywhere with our flexible online learning platform.</p>
                                </div>
                            </div>

                            <div class="feature-item wow fadeInUp" data-wow-delay=".9s">
                                <div class="feature-icon">
                                    <i class="lni lni-graduation"></i>
                                </div>
                                <div class="feature-content">
                                    <h5>Comprehensive Curriculum</h5>
                                    <p>Learn Arabic with our structured, comprehensive curriculum designed for all levels.</p>
                                </div>
                            </div>
                        </div>

                        <p class="qote wow fadeInUp" data-wow-delay=".9s"><i class="lni lni-quotation"></i> Empowering minds through knowledge and faith.</p>
                        <div class="button wow fadeInUp" data-wow-delay="1s">
                            <a href="<?php echo esc_url(get_permalink(get_page_by_path('courses'))); ?>" class="btn">Explore Courses <i class="lni lni-arrow-right"></i></a>
                            <a href="https://youtu.be/MftMN-vQgZw" class="glightbox video btn">Play Video<i class="lni lni-play"></i></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-12">
                <div class="about-right">
                    <div class="about-img-wrapper wow fadeInRight" data-wow-delay=".4s">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/homepage.jpg" alt="Zajel Arabic Learning">
                        <div class="img-shape-1"></div>
                        <div class="img-shape-2"></div>

                        <!-- Floating Elements -->
                        <div class="floating-element element-1 wow fadeInUp" data-wow-delay=".6s">
                            <div class="icon-box">
                                <i class="lni lni-users"></i>
                            </div>
                            <h5>5000+ Students</h5>
                        </div>

                        <div class="floating-element element-2 wow fadeInUp" data-wow-delay=".8s">
                            <div class="icon-box">
                                <i class="lni lni-certificate"></i>
                            </div>
                            <h5>Certified Courses</h5>
                        </div>

                        <div class="floating-element element-3 wow fadeInUp" data-wow-delay="1s">
                            <div class="icon-box">
                                <i class="lni lni-world"></i>
                            </div>
                            <h5>Global Community</h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- /End About Us Area -->

<!-- Start Our Journey Area -->
<section class="our-journey section gray-bg">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title align-center">
                    <div class="section-icon wow zoomIn" data-wow-delay=".4s">
                        <i class="lni lni-road"></i>
                    </div>
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">Our Journey</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">Discover how Zajel Arabic Institute has grown over the years.</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="timeline-wrapper">
                    <div class="timeline-line"></div>

                    <!-- Timeline Item 1 - Right -->
                    <div class="timeline-item timeline-right wow fadeInRight" data-wow-delay=".4s">
                        <div class="timeline-dot">
                            <i class="lni lni-flag"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-year">2013</div>
                            <h4>Foundation</h4>
                            <p>Zajel Arabic Institute was founded with a vision to make Arabic language accessible to everyone.</p>
                        </div>
                    </div>

                    <!-- Timeline Item 2 - Left -->
                    <div class="timeline-item timeline-left wow fadeInLeft" data-wow-delay=".6s">
                        <div class="timeline-dot">
                            <i class="lni lni-laptop"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-year">2015</div>
                            <h4>Online Expansion</h4>
                            <p>Launched our first online courses, reaching students beyond local communities.</p>
                        </div>
                    </div>

                    <!-- Timeline Item 3 - Right -->
                    <div class="timeline-item timeline-right wow fadeInRight" data-wow-delay=".8s">
                        <div class="timeline-dot">
                            <i class="lni lni-book"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-year">2017</div>
                            <h4>Curriculum Development</h4>
                            <p>Developed comprehensive curriculum covering all aspects of Arabic language and Islamic studies.</p>
                        </div>
                    </div>

                    <!-- Timeline Item 4 - Left -->
                    <div class="timeline-item timeline-left wow fadeInLeft" data-wow-delay="1s">
                        <div class="timeline-dot">
                            <i class="lni lni-world"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-year">2019</div>
                            <h4>Global Reach</h4>
                            <p>Expanded to serve students from over 50 countries worldwide.</p>
                        </div>
                    </div>

                    <!-- Timeline Item 5 - Right -->
                    <div class="timeline-item timeline-right wow fadeInRight" data-wow-delay="1.2s">
                        <div class="timeline-dot">
                            <i class="lni lni-cog"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-year">2021</div>
                            <h4>Technology Integration</h4>
                            <p>Implemented advanced learning technologies to enhance the student experience.</p>
                        </div>
                    </div>

                    <!-- Timeline Item 6 - Left -->
                    <div class="timeline-item timeline-left wow fadeInLeft" data-wow-delay="1.4s">
                        <div class="timeline-dot">
                            <i class="lni lni-users"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-year">2023</div>
                            <h4>Community Building</h4>
                            <p>Created a vibrant global community of Arabic language learners and enthusiasts.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Counter -->
        <div class="row counter-area mt-5">
            <div class="col-lg-3 col-md-6 col-6">
                <div class="single-counter wow fadeInUp" data-wow-delay=".2s">
                    <div class="icon">
                        <i class="lni lni-users"></i>
                    </div>
                    <h3><span class="counter">5000</span>+</h3>
                    <p>Students Worldwide</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-6">
                <div class="single-counter wow fadeInUp" data-wow-delay=".4s">
                    <div class="icon">
                        <i class="lni lni-graduation"></i>
                    </div>
                    <h3><span class="counter">20</span>+</h3>
                    <p>Expert Teachers</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-6">
                <div class="single-counter wow fadeInUp" data-wow-delay=".6s">
                    <div class="icon">
                        <i class="lni lni-book"></i>
                    </div>
                    <h3><span class="counter">50</span>+</h3>
                    <p>Courses Offered</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-6">
                <div class="single-counter wow fadeInUp" data-wow-delay=".8s">
                    <div class="icon">
                        <i class="lni lni-world"></i>
                    </div>
                    <h3><span class="counter">50</span>+</h3>
                    <p>Countries Reached</p>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Our Journey Area -->

<!-- Start Testimonials Area -->
<section class="testimonials section" style="background-color: #f9f9f9; padding: 80px 0;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title align-center">
                    <div class="section-icon" style="background-color: transparent;">
                        <i class="lni lni-quotation" style="color: #03355c;"></i>
                    </div>
                    <h2 style="color: #03355c;">What Our Students Say</h2>
                    <p style="color: #555;">Hear from students who have transformed their learning journey with Zajel Arabic.</p>
                </div>
            </div>
        </div>

        <div class="row testimonial-container" style="position: relative; justify-content: center;">
            <?php
            $args = array(
                'post_type' => 'testimonial',
                'posts_per_page' => 6,
            );
            $testimonials = new WP_Query($args);
            if ($testimonials->have_posts()) :
                while ($testimonials->have_posts()) : $testimonials->the_post();
                    $designation = get_post_meta(get_the_ID(), 'testimonial_designation', true) ?: 'Student';
                    ?>
                    <div class="col-lg-4 col-md-6 col-12" style="margin-bottom: 30px; padding: 0 15px;">
                        <div class="single-testimonial" style="background-color: #fff; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); overflow: hidden; height: 100%; display: flex; flex-direction: column;">
                            <div class="quote-icon" style="text-align: center; padding: 20px 0 0;">
                                <i class="lni lni-quotation" style="color: #1a5f8d; font-size: 30px;"></i>
                            </div>
                            <div class="text" style="flex: 1; padding: 20px 25px; background-color: #fff; position: relative; border-radius: 0; margin-bottom: 0;">
                                <p style="font-size: 14px; line-height: 1.6; color: #666;"><?php the_excerpt(); ?></p>
                            </div>
                            <div class="author" style="padding: 15px 25px 25px; background-color: #fff; display: flex; align-items: center;">
                                <div class="author-image" style="width: 60px; height: 60px; border-radius: 50%; overflow: hidden; margin-right: 15px; border: 3px solid rgba(3, 53, 92, 0.2); position: relative;">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <?php the_post_thumbnail('thumbnail', ['alt' => get_the_title(), 'style' => 'width: 100%; height: 100%; object-fit: cover;']); ?>
                                    <?php else : ?>
                                        <img src="https://via.placeholder.com/300x300" alt="<?php the_title(); ?>" style="width: 100%; height: 100%; object-fit: cover;">
                                    <?php endif; ?>
                                    <div class="quote-mark" style="position: absolute; bottom: 0; right: 0; width: 25px; height: 25px; background: #1a5f8d; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="lni lni-star-filled" style="font-size: 12px; color: #fff;"></i>
                                    </div>
                                </div>
                                <div class="author-info" style="text-align: left;">
                                    <h4 class="name" style="font-size: 16px; font-weight: 600; margin-bottom: 5px; color: #333;">
                                        <?php the_title(); ?>
                                        <span class="deg" style="font-size: 13px; display: block; font-weight: 400; color: #1a5f8d; margin-top: 3px;"><i class="lni lni-user"></i> <?php echo esc_html($designation); ?></span>
                                    </h4>
                                    <div class="rating" style="margin-top: 5px;">
                                        <i class="lni lni-star-filled" style="color: #1a5f8d; font-size: 12px; margin-right: 2px;"></i>
                                        <i class="lni lni-star-filled" style="color: #1a5f8d; font-size: 12px; margin-right: 2px;"></i>
                                        <i class="lni lni-star-filled" style="color: #1a5f8d; font-size: 12px; margin-right: 2px;"></i>
                                        <i class="lni lni-star-filled" style="color: #1a5f8d; font-size: 12px; margin-right: 2px;"></i>
                                        <i class="lni lni-star-filled" style="color: #1a5f8d; font-size: 12px; margin-right: 2px;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                endwhile;
                wp_reset_postdata();
                ?>
                <!-- Navigation will be added dynamically by JavaScript -->
            <?php else : ?>
                <div class="col-12">
                    <p style="text-align: center; padding: 30px; background: #fff; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">No testimonials available at the moment. Stay tuned for student reviews!</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>
<!-- End Testimonial Area -->

<!-- Start Teachers -->
<section id="teachers" class="teachers section" style="background-color: #f9f9f9; padding: 80px 0;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title align-center">
                    <div class="section-icon" style="background-color: transparent;">
                        <i class="lni lni-users" style="color: #03355c;"></i>
                    </div>
                    <h2 style="color: #03355c;">Our Experienced Advisors</h2>
                    <p style="color: #555;">Meet the dedicated instructors at Zajel Arabic guiding our students.</p>
                </div>
            </div>
        </div>
        <style>
            .single-team {
                background-color: #fff;
                border-radius: 10px;
                box-shadow: 0 5px 25px rgba(0,0,0,0.08);
                overflow: hidden;
                transition: all 0.3s ease;
            }
            .single-team:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            }
            .teacher-img-wrapper {
                transition: all 0.5s ease;
            }
            .single-team:hover .teacher-img-wrapper img {
                transform: scale(1.05);
            }
            .experience-badge {
                transition: all 0.3s ease;
            }
            .single-team:hover .experience-badge {
                background: #03355c !important;
            }
            .skill-tag {
                transition: all 0.3s ease;
            }
            .skill-tag:hover {
                background: rgba(26, 95, 141, 0.2) !important;
                transform: translateY(-2px);
            }

            /* Swipe styles for advisors section */
            .advisor-touch-container {
                overflow: hidden;
                position: relative;
                cursor: grab;
            }
            .advisor-touch-container:active {
                cursor: grabbing;
            }
            #teachers .row:not(:first-child) {
                user-select: none;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                transition: transform 0.3s ease;
            }
            .advisor-navigation {
                margin-top: 30px !important;
            }
            .advisor-arrow {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
                z-index: 10;
            }
            .advisor-arrow:hover:not(.disabled) {
                transform: scale(1.1);
                box-shadow: 0 5px 15px rgba(26, 95, 141, 0.2) !important;
            }
            .advisor-arrow.disabled {
                opacity: 0.5;
                cursor: default;
            }
            .advisor-dot {
                transition: all 0.3s ease;
                margin: 0 5px;
            }
            .advisor-dot:hover {
                transform: scale(1.2);
            }
        </style>
        <div class="row">
            <?php
            $args = array(
                'post_type' => 'teacher',
                'posts_per_page' => 6,
            );
            $teachers = new WP_Query($args);
            if ($teachers->have_posts()) :
                while ($teachers->have_posts()) : $teachers->the_post();
                    $designation = get_post_meta(get_the_ID(), 'teacher_designation', true) ?: 'Instructor';
                    ?>
                    <div class="col-lg-6 col-md-6 col-12" style="margin-bottom: 30px; padding: 0 15px;">
                        <div class="single-team">
                            <div class="row" style="margin: 0;">
                                <div class="col-lg-5 col-md-5 col-12" style="padding: 0;">
                                    <div class="image" style="height: 250px; position: relative;">
                                        <div class="teacher-img-wrapper" style="height: 100%; overflow: hidden; border-radius: 10px 0 0 10px;">
                                            <?php if (has_post_thumbnail()) : ?>
                                                <?php the_post_thumbnail('medium_large', ['alt' => get_the_title(), 'style' => 'width: 100%; height: 100%; object-fit: cover;']); ?>
                                            <?php else : ?>
                                                <img src="https://via.placeholder.com/800x1020" alt="<?php the_title(); ?>" style="width: 100%; height: 100%; object-fit: cover;">
                                            <?php endif; ?>
                                            <?php
                                            // Get experience years dynamically
                                            $experience_years = get_post_meta(get_the_ID(), 'teacher_experience', true);
                                            if (!empty($experience_years)) :
                                            ?>
                                            <div class="experience-badge" style="position: absolute; top: 15px; left: 15px; background: #1a5f8d; color: #fff; border-radius: 50%; font-weight: 600; box-shadow: 0 3px 10px rgba(0,0,0,0.2); z-index: 1; font-size: 12px; width: 70px; height: 70px; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                                                <span style="font-size: 22px; font-weight: bold; line-height: 1;"><?php echo esc_html($experience_years); ?>+</span>
                                                <small style="font-size: 10px; text-transform: uppercase; margin-top: 2px;">YEARS</small>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-7 col-md-7 col-12" style="padding: 25px 20px;">
                                    <div class="info-head">
                                        <div class="info-box">
                                            <div class="teacher-icon" style="display: inline-block; width: 40px; height: 40px; line-height: 40px; text-align: center; background: rgba(26, 95, 141, 0.1); border-radius: 50%; margin-bottom: 15px;">
                                                <i class="lni lni-graduation" style="color: #1a5f8d; font-size: 20px;"></i>
                                            </div>
                                            <span class="designation" style="display: inline-block; background: rgba(26, 95, 141, 0.1); color: #1a5f8d; padding: 5px 15px; border-radius: 30px; font-size: 13px; margin-left: 10px;">
                                                <i class="lni lni-briefcase"></i> <?php echo esc_html($designation); ?>
                                            </span>
                                            <h4 class="name" style="font-size: 22px; margin: 15px 0; color: #03355c;">
                                                <a href="<?php the_permalink(); ?>" style="color: inherit;"><?php the_title(); ?></a>
                                            </h4>
                                            <div class="teacher-skills" style="margin-bottom: 15px;">
                                                <?php
                                                // Get teacher courses dynamically
                                                $courses = get_post_meta(get_the_ID(), 'teacher_courses', true);
                                                if (!empty($courses)) {
                                                    $courses_array = explode(',', $courses);
                                                    foreach ($courses_array as $course) {
                                                        $course = trim($course);
                                                        $icon = 'lni-book'; // Default icon

                                                        // Set icon based on course
                                                        if (stripos($course, 'arabic') !== false) {
                                                            $icon = 'lni-book';
                                                        } elseif (stripos($course, 'tajweed') !== false) {
                                                            $icon = 'lni-mic';
                                                        } elseif (stripos($course, 'certified') !== false || stripos($course, 'certificate') !== false) {
                                                            $icon = 'lni-certificate';
                                                        } elseif (stripos($course, 'quran') !== false) {
                                                            $icon = 'lni-book-mark';
                                                        } elseif (stripos($course, 'islamic') !== false) {
                                                            $icon = 'lni-graduation';
                                                        }

                                                        echo '<span class="skill-tag" style="display: inline-block; background: rgba(26, 95, 141, 0.1); color: #1a5f8d; padding: 5px 12px; border-radius: 20px; font-size: 12px; margin-right: 5px; margin-bottom: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05); cursor: pointer;"><i class="lni ' . esc_attr($icon) . '"></i> ' . esc_html($course) . '</span>';
                                                    }
                                                } else {
                                                    // Default courses if none are set
                                                    echo '<span class="skill-tag" style="display: inline-block; background: rgba(26, 95, 141, 0.1); color: #1a5f8d; padding: 5px 12px; border-radius: 20px; font-size: 12px; margin-right: 5px; margin-bottom: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05); cursor: pointer;"><i class="lni lni-book"></i> Arabic</span>';
                                                    echo '<span class="skill-tag" style="display: inline-block; background: rgba(26, 95, 141, 0.1); color: #1a5f8d; padding: 5px 12px; border-radius: 20px; font-size: 12px; margin-right: 5px; margin-bottom: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.05); cursor: pointer;"><i class="lni lni-mic"></i> Tajweed</span>';
                                                }
                                                ?>
                                            </div>
                                            <?php
                                            // Get teacher bio/summary from custom field, excerpt, or content
                                            $teacher_bio = '';
                                            $teacher_summary = get_post_meta(get_the_ID(), 'teacher_summary', true);

                                            if (!empty($teacher_summary)) {
                                                // Use custom summary field if available
                                                $teacher_bio = $teacher_summary;
                                            } elseif (has_excerpt()) {
                                                // Use excerpt if available
                                                $teacher_bio = get_the_excerpt();
                                            } else {
                                                // Use trimmed content as fallback
                                                $teacher_bio = wp_trim_words(get_the_content(), 25);
                                            }
                                            ?>
                                            <p style="font-size: 14px; line-height: 1.6; color: #666; margin-bottom: 20px;"><?php echo $teacher_bio; ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                endwhile;
                wp_reset_postdata();
            else :
                ?>
                <p>No teachers available at the moment. Stay tuned for updates!</p>
            <?php endif; ?>
        </div>
    </div>
</section>
<!--/ End Teachers Area -->

<!-- Start Clients Area -->
<div class="client-logo-section section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title align-center">
                    <div class="section-icon wow zoomIn" data-wow-delay=".4s">
                        <i class="lni lni-handshake"></i>
                    </div>
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">Our Partners & Collaborations</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">We're proud to work with these organizations to provide quality Arabic education.</p>
                </div>
            </div>
        </div>

        <div class="client-logo-wrapper">
            <div class="client-decoration">
                <span class="client-dot dot-1 pulse"></span>
                <span class="client-dot dot-2 pulse"></span>
                <span class="client-dot dot-3 pulse"></span>
            </div>
            <div class="row">
                <?php
                // Placeholder for client logos (could use a custom post type or media library)
                $logos = array(
                    'https://via.placeholder.com/230x95',
                    'https://via.placeholder.com/230x95',
                    'https://via.placeholder.com/230x95',
                    'https://via.placeholder.com/230x95',
                    'https://via.placeholder.com/230x95',
                    'https://via.placeholder.com/230x95',
                );
                $delay = 0.2;
                foreach ($logos as $index => $logo_url) :
                    ?>
                    <div class="col-lg-4 col-md-4 col-6">
                        <div class="client-logo wow fadeInUp" data-wow-delay="<?php echo esc_attr($delay); ?>s">
                            <img src="<?php echo esc_url($logo_url); ?>" alt="Partner Logo">
                            <div class="client-overlay">
                                <i class="lni lni-plus"></i>
                            </div>
                        </div>
                    </div>
                <?php
                $delay += 0.2;
                endforeach;
                ?>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="row mt-5">
            <div class="col-12 text-center">
                <div class="partner-cta wow fadeInUp" data-wow-delay=".8s">
                    <h4>Interested in partnering with us?</h4>
                    <p>Join our mission to spread Arabic language education worldwide.</p>
                    <a href="<?php echo esc_url(get_permalink(get_page_by_path('contact'))); ?>" class="btn">Contact Us <i class="lni lni-arrow-right"></i></a>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Clients Area -->

<?php get_footer(); ?>