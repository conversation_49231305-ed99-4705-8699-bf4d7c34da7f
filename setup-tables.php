<?php
// إنشاء جداول قاعدة البيانات للفورمز
require_once('../../../../wp-config.php');

global $wpdb;

// جدول الـ contact form
$contact_table = $wpdb->prefix . 'contact';
$contact_charset_collate = $wpdb->get_charset_collate();

$contact_sql = "CREATE TABLE $contact_table (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    name tinytext NOT NULL,
    subject varchar(200) DEFAULT '',
    email varchar(100) NOT NULL,
    phone varchar(20) DEFAULT '',
    message text NOT NULL,
    ip_address varchar(45) DEFAULT '',
    user_agent text DEFAULT '',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY  (id)
) $contact_charset_collate;";

// جدول الـ trial class
$trial_table = $wpdb->prefix . 'trial_bookings';

$trial_sql = "CREATE TABLE $trial_table (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    full_name tinytext NOT NULL,
    email varchar(100) NOT NULL,
    phone varchar(20) NOT NULL,
    course varchar(50) NOT NULL,
    additional_notes text DEFAULT '',
    ip_address varchar(45) DEFAULT '',
    user_agent text DEFAULT '',
    status varchar(20) DEFAULT 'pending',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY  (id)
) $contact_charset_collate;";

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
dbDelta($contact_sql);
dbDelta($trial_sql);

echo "Tables created successfully!";
?>