<?php
// فحص الصفحات في WordPress
require_once('../../../wp-load.php');

echo "<h1>WordPress Pages Check</h1>";

// فحص جميع الصفحات
$pages = get_pages();

echo "<h2>All Pages:</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Title</th><th>Slug</th><th>Template</th><th>Status</th><th>URL</th></tr>";

foreach ($pages as $page) {
    $template = get_page_template_slug($page->ID);
    $url = get_permalink($page->ID);
    
    echo "<tr>";
    echo "<td>" . $page->ID . "</td>";
    echo "<td>" . $page->post_title . "</td>";
    echo "<td>" . $page->post_name . "</td>";
    echo "<td>" . ($template ? $template : 'default') . "</td>";
    echo "<td>" . $page->post_status . "</td>";
    echo "<td><a href='" . $url . "' target='_blank'>" . $url . "</a></td>";
    echo "</tr>";
}

echo "</table>";

// فحص الصفحات المحددة
echo "<h2>Specific Pages Check:</h2>";

$contact_page = get_page_by_path('contact');
$trial_page = get_page_by_path('trial-class');

echo "<h3>Contact Page:</h3>";
if ($contact_page) {
    echo "<p>✅ Found: ID = " . $contact_page->ID . "</p>";
    echo "<p>Title: " . $contact_page->post_title . "</p>";
    echo "<p>Slug: " . $contact_page->post_name . "</p>";
    echo "<p>Status: " . $contact_page->post_status . "</p>";
    echo "<p>Template: " . get_page_template_slug($contact_page->ID) . "</p>";
    echo "<p>URL: <a href='" . get_permalink($contact_page->ID) . "' target='_blank'>" . get_permalink($contact_page->ID) . "</a></p>";
} else {
    echo "<p>❌ Contact page not found!</p>";
}

echo "<h3>Trial Class Page:</h3>";
if ($trial_page) {
    echo "<p>✅ Found: ID = " . $trial_page->ID . "</p>";
    echo "<p>Title: " . $trial_page->post_title . "</p>";
    echo "<p>Slug: " . $trial_page->post_name . "</p>";
    echo "<p>Status: " . $trial_page->post_status . "</p>";
    echo "<p>Template: " . get_page_template_slug($trial_page->ID) . "</p>";
    echo "<p>URL: <a href='" . get_permalink($trial_page->ID) . "' target='_blank'>" . get_permalink($trial_page->ID) . "</a></p>";
} else {
    echo "<p>❌ Trial Class page not found!</p>";
}

// فحص الـ permalink structure
echo "<h2>Permalink Structure:</h2>";
echo "<p>Current structure: " . get_option('permalink_structure') . "</p>";

// فحص الـ rewrite rules
echo "<h2>Rewrite Rules (sample):</h2>";
global $wp_rewrite;
$rules = $wp_rewrite->wp_rewrite_rules();
echo "<p>Total rules: " . count($rules) . "</p>";

// عرض أول 10 rules
$count = 0;
echo "<ul>";
foreach ($rules as $pattern => $replacement) {
    if ($count >= 10) break;
    echo "<li><strong>$pattern</strong> → $replacement</li>";
    $count++;
}
echo "</ul>";

// فحص الـ templates
echo "<h2>Available Templates:</h2>";
$templates = wp_get_theme()->get_page_templates();
echo "<ul>";
foreach ($templates as $file => $name) {
    echo "<li><strong>$file</strong> → $name</li>";
}
echo "</ul>";

// إضافة أزرار للإصلاح
echo "<h2>Quick Fixes:</h2>";
echo "<p><a href='?flush_rewrite=1' style='background: #007cba; color: white; padding: 10px; text-decoration: none;'>Flush Rewrite Rules</a></p>";

if (isset($_GET['flush_rewrite'])) {
    flush_rewrite_rules();
    echo "<div style='background: #d4edda; color: #155724; padding: 10px; margin: 10px 0; border: 1px solid #c3e6cb;'>";
    echo "✅ Rewrite rules flushed! Try accessing the pages now.";
    echo "</div>";
}

// فحص الـ .htaccess
echo "<h2>.htaccess Check:</h2>";
$htaccess_file = ABSPATH . '.htaccess';
if (file_exists($htaccess_file)) {
    echo "<p>✅ .htaccess file exists</p>";
    $htaccess_content = file_get_contents($htaccess_file);
    echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 200px; overflow-y: scroll;'>";
    echo htmlspecialchars($htaccess_content);
    echo "</pre>";
} else {
    echo "<p>❌ .htaccess file not found!</p>";
}
?>
