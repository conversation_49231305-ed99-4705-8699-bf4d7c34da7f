// Animations JavaScript for Zajel Arabic Theme

document.addEventListener('DOMContentLoaded', function() {
    // Animate hero section spans
    const heroSpans = document.querySelectorAll('.hero-area .hero-text h1 span');
    if (heroSpans.length > 0) {
        setTimeout(() => {
            heroSpans.forEach(span => {
                span.classList.add('animated');
            });
        }, 1000);
    }

    // Initialize Animate on Scroll
    initAnimateOnScroll();

    // Initialize Counter Animation
    initCounterAnimation();

    // Add floating animation to specific elements
    addFloatingAnimation();

    // Initialize Testimonial Slider if exists
    initTestimonialSlider();

    // Make images responsive
    makeImagesResponsive();
});

// Function to initialize animations on scroll
function initAnimateOnScroll() {
    const animatedElements = document.querySelectorAll('.animate-on-scroll');

    // Initial check for elements in viewport
    checkElementsInViewport();

    // Check on scroll
    window.addEventListener('scroll', checkElementsInViewport);

    function checkElementsInViewport() {
        animatedElements.forEach(element => {
            const elementPosition = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;

            if (elementPosition < windowHeight - 100) {
                element.classList.add('visible');
            }
        });
    }
}

// Function to initialize counter animation
function initCounterAnimation() {
    const counters = document.querySelectorAll('.countup');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('cup-end'), 10);
        const duration = 2000; // 2 seconds
        const step = Math.ceil(target / (duration / 20)); // Update every 20ms
        let current = 0;

        const observer = new IntersectionObserver(entries => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const timer = setInterval(() => {
                        current += step;
                        if (current >= target) {
                            counter.textContent = target;
                            clearInterval(timer);
                        } else {
                            counter.textContent = current;
                        }
                    }, 20);

                    // Unobserve after animation starts
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        observer.observe(counter);
    });
}

// Function to add floating animation to specific elements
function addFloatingAnimation() {
    // Add floating animation to hero image, icons, etc.
    const floatingElements = [
        '.hero-area .video-button',
        '.single-service .icon',
        '.newsletter-area .lni-envelope',
        '.mission-vision-card .icon-box',
        '.mission-vision-section .decoration-image'
    ];

    floatingElements.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.classList.add('float');
        });
    });

    // Add special animations to mission & vision cards
    const missionVisionCards = document.querySelectorAll('.mission-vision-card');
    missionVisionCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const iconBox = this.querySelector('.icon-box');
            if (iconBox) {
                iconBox.style.transform = 'rotateY(180deg)';
                setTimeout(() => {
                    iconBox.style.transform = 'rotateY(0deg)';
                }, 500);
            }
        });
    });

    // Add hover effect to values list items
    const valuesListItems = document.querySelectorAll('.mission-vision-card .values-list li');
    valuesListItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.add('pulse');
                setTimeout(() => {
                    icon.classList.remove('pulse');
                }, 1000);
            }
        });
    });

    // Add animations to Why Choose Us section
    const singleFeatures = document.querySelectorAll('.single-feature');
    singleFeatures.forEach(feature => {
        feature.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.icon i');
            if (icon) {
                icon.style.transform = 'rotateY(360deg)';
            }
        });
    });

    // Add counter animation to experience badge
    const experienceBadge = document.querySelector('.experience-badge .years');
    if (experienceBadge) {
        const targetYears = parseInt(experienceBadge.textContent, 10);
        if (!isNaN(targetYears)) {
            experienceBadge.textContent = '0';

            const observer = new IntersectionObserver(entries => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        let count = 0;
                        const interval = setInterval(() => {
                            count++;
                            experienceBadge.textContent = count;
                            if (count >= targetYears) {
                                experienceBadge.textContent = targetYears + '+';
                                clearInterval(interval);
                            }
                        }, 100);

                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });

            observer.observe(experienceBadge);
        }
    }
}

// Function to initialize testimonial slider
function initTestimonialSlider() {
    const testimonialSlider = document.querySelector('.testimonial-slider');
    if (!testimonialSlider) return;

    // Simple responsive slider functionality
    const testimonials = testimonialSlider.querySelectorAll('.single-testimonial');
    if (testimonials.length <= 1) return;

    // Add navigation dots
    const dotsContainer = document.createElement('div');
    dotsContainer.className = 'slider-dots';
    testimonialSlider.appendChild(dotsContainer);

    testimonials.forEach((_, index) => {
        const dot = document.createElement('span');
        dot.className = index === 0 ? 'dot active' : 'dot';
        dot.addEventListener('click', () => showTestimonial(index));
        dotsContainer.appendChild(dot);
    });

    // Show only the first testimonial initially on mobile
    if (window.innerWidth < 768) {
        testimonials.forEach((testimonial, index) => {
            testimonial.style.display = index === 0 ? 'block' : 'none';
        });
    }

    function showTestimonial(index) {
        if (window.innerWidth < 768) {
            testimonials.forEach((testimonial, i) => {
                testimonial.style.display = i === index ? 'block' : 'none';
            });

            // Update active dot
            const dots = dotsContainer.querySelectorAll('.dot');
            dots.forEach((dot, i) => {
                dot.className = i === index ? 'dot active' : 'dot';
            });
        }
    }

    // Add swipe functionality for mobile
    let touchStartX = 0;
    let touchEndX = 0;
    let currentIndex = 0;

    testimonialSlider.addEventListener('touchstart', e => {
        touchStartX = e.changedTouches[0].screenX;
    }, false);

    testimonialSlider.addEventListener('touchend', e => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    }, false);

    function handleSwipe() {
        if (window.innerWidth >= 768) return;

        if (touchEndX < touchStartX) {
            // Swipe left - next
            currentIndex = (currentIndex + 1) % testimonials.length;
        } else if (touchEndX > touchStartX) {
            // Swipe right - previous
            currentIndex = (currentIndex - 1 + testimonials.length) % testimonials.length;
        }

        showTestimonial(currentIndex);
    }
}

// Function to make images responsive
function makeImagesResponsive() {
    const images = document.querySelectorAll('img:not(.responsive-img)');
    images.forEach(img => {
        img.classList.add('responsive-img');
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
    });
}

// Add scroll reveal animation
window.addEventListener('load', function() {
    // Add animation classes to elements as they scroll into view
    const sections = document.querySelectorAll('section');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('section-visible');

                // Animate children with delay
                const animatedChildren = entry.target.querySelectorAll('.fade-in, .fade-in-up, .fade-in-down, .fade-in-left, .fade-in-right, .zoom-in');
                animatedChildren.forEach((child, index) => {
                    setTimeout(() => {
                        child.style.opacity = '1';
                        child.style.transform = 'translateY(0) translateX(0) scale(1)';
                    }, 100 * index);
                });

                // Unobserve after animation
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    sections.forEach(section => {
        observer.observe(section);
    });

    // Add gold gradient text effect to headings
    const headings = document.querySelectorAll('h1, h2');
    headings.forEach(heading => {
        const spans = heading.querySelectorAll('span');
        spans.forEach(span => {
            span.classList.add('gold-gradient-text');
        });
    });

    // Add pulse animation to CTA buttons
    const ctaButtons = document.querySelectorAll('.call-action .btn, .hero-area .btn');
    ctaButtons.forEach(button => {
        button.classList.add('pulse');
    });
});
