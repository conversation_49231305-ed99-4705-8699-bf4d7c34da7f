/* Blog Page Styles for Zajel Arabic Theme */
:root {
    --blue-light: #1a5f8d;
    --blue-medium: #0c4a77;
    --blue-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Blog Hero Section */
.hero-area.style2 {
    position: relative;
    background: linear-gradient(135deg, #03355c, #1a5f8d);
    padding: 100px 0;
    overflow: hidden;
}

.hero-area.style2::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 1;
}

.hero-area.style2 .hero-inner {
    position: relative;
    z-index: 2;
}

.hero-area.style2 .page-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.hero-area.style2 .page-icon i {
    font-size: 36px;
    color: #fff;
}

.hero-area.style2 .hero-text {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-area.style2 .hero-text h1 {
    color: #fff;
    font-size: 48px;
    font-weight: 800;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-area.style2 .hero-text h1 span {
    color: #fff;
    position: relative;
    display: inline-block;
}

.hero-area.style2 .hero-text h1 span::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 0;
    width: 100%;
    height: 8px;
    background-color: rgba(255, 255, 255, 0.2);
    z-index: -1;
}

.hero-area.style2 .hero-text p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 30px;
}

.hero-area.style2 .button .btn {
    margin: 0 10px;
    padding: 15px 30px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 16px;
    transition: var(--transition-fast);
}

.hero-area.style2 .button .btn:first-child {
    background-color: #fff;
    color: var(--blue-dark);
}

.hero-area.style2 .button .btn:first-child:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-3px);
}

.hero-area.style2 .button .btn.mouse-dir {
    background-color: transparent;
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.hero-area.style2 .button .btn.mouse-dir:hover {
    border-color: #fff;
    transform: translateY(-3px);
}

/* Blog Area Styles */
.blog-area {
    background-color: #f9f9f9;
    padding: 100px 0;
    position: relative;
}

.blog-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a5f8d' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 0;
}

.blog-area .container {
    position: relative;
    z-index: 1;
}

/* Blog Posts Container */
.blog-posts {
    margin-bottom: 50px;
}

/* Archive Post Item */
.archive-post {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    transition: all 0.5s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.archive-post:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.archive-post .post-thumbnail {
    position: relative;
    overflow: hidden;
}

.archive-post .post-thumbnail img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.archive-post:hover .post-thumbnail img {
    transform: scale(1.05);
}

.archive-post .post-thumbnail::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
    opacity: 0;
    transition: all 0.5s ease;
}

.archive-post:hover .post-thumbnail::after {
    opacity: 1;
}

.archive-post .post-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.archive-post .post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 14px;
}

.archive-post .post-meta > div {
    display: flex;
    align-items: center;
    position: relative;
}

.archive-post .post-meta > div:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 12px;
    background-color: #ddd;
}

.archive-post .post-meta i {
    color: #03777d;
    margin-right: 5px;
}

.archive-post .post-meta a {
    color: #666;
    text-decoration: none;
    transition: all 0.3s ease;
}

.archive-post .post-meta a:hover {
    color: #03777d;
}

.archive-post .post-title {
    font-size: 22px;
    margin-bottom: 15px;
    font-weight: 700;
    line-height: 1.4;
}

.archive-post .post-title a {
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.archive-post .post-title a:hover {
    color: #03777d;
}

.archive-post .post-excerpt {
    color: #666;
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 20px;
}

.archive-post .read-more {
    margin-top: auto;
    align-self: flex-start;
}

.archive-post .read-more a {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #00989f, #03777d);
    color: #fff;
    padding: 10px 20px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(3, 119, 125, 0.2);
}

.archive-post .read-more a i {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.archive-post .read-more a:hover {
    background: linear-gradient(135deg, #03777d, #03355c);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(3, 119, 125, 0.3);
}

.archive-post .read-more a:hover i {
    transform: translateX(3px);
}

/* Featured Post */
.featured-post {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 40px;
    transition: all 0.5s ease;
}

.featured-post:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.featured-post-content {
    padding: 0;
}

.featured-post .post-thumbnail {
    position: relative;
    height: 100%;
    overflow: hidden;
}

.featured-post .post-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.featured-post:hover .post-thumbnail img {
    transform: scale(1.05);
}

.featured-post .featured-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, #03777d, #00989f);
    color: #fff;
    padding: 5px 15px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.featured-post .featured-badge i {
    margin-right: 5px;
    color: #fff;
}

.featured-post .post-content {
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.featured-post .post-meta {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.featured-post .post-meta > div {
    margin-right: 20px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #777;
}

.featured-post .post-meta i {
    margin-right: 5px;
    color: #03777d;
}

.featured-post .post-meta a {
    color: #777;
    text-decoration: none;
    transition: all 0.3s ease;
}

.featured-post .post-meta a:hover {
    color: #03777d;
}

.featured-post .post-title {
    font-size: 24px;
    font-weight: 700;
    line-height: 1.4;
    margin-bottom: 15px;
}

.featured-post .post-title a {
    color: #222;
    text-decoration: none;
    transition: all 0.3s ease;
}

.featured-post .post-title a:hover {
    color: #03777d;
}

.featured-post .post-excerpt {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

.featured-post .read-more a {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #00989f, #03777d);
    color: #fff;
    padding: 10px 20px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(3, 119, 125, 0.2);
}

.featured-post .read-more a i {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.featured-post .read-more a:hover {
    background: linear-gradient(135deg, #03777d, #03355c);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(3, 119, 125, 0.3);
}

.featured-post .read-more a:hover i {
    transform: translateX(3px);
}

/* Pagination */
.pagination-area {
    margin-top: 50px;
    text-align: center;
}

.pagination {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0;
    padding: 0;
}

.page-numbers {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #fff;
    color: #333;
    border-radius: 50%;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.page-numbers:hover {
    background-color: #1a5f8d;
    color: #fff;
}

.page-numbers.current {
    background: linear-gradient(135deg, #03777d, #00989f);
    color: #fff;
    box-shadow: 0 4px 10px rgba(3, 119, 125, 0.2);
}

.page-numbers.prev,
.page-numbers.next {
    width: auto;
    padding: 0 15px;
}

.page-numbers.prev:hover i {
    transform: translateX(-3px);
}

.page-numbers.next:hover i {
    transform: translateX(3px);
}

.page-numbers i {
    transition: all 0.3s ease;
}

/* Blog Sidebar */
.blog-sidebar {
    position: sticky;
    top: 30px;
}

.widget {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.widget-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(3, 119, 125, 0.1);
    position: relative;
    color: #333;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #03777d, #00989f);
}

/* Search Widget */
.widget_search .search-form {
    position: relative;
}

.widget_search .search-field {
    width: 100%;
    padding: 12px 20px;
    padding-right: 50px;
    border: 1px solid #eee;
    border-radius: 30px;
    font-size: 15px;
    transition: all 0.3s ease;
}

.widget_search .search-field:focus {
    border-color: #03777d;
    outline: none;
    box-shadow: 0 0 0 3px rgba(3, 119, 125, 0.1);
}

.widget_search .search-submit {
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
    height: 100%;
    background-color: transparent;
    border: none;
    color: #666;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.widget_search .search-submit:hover {
    color: #03777d;
}

/* Categories Widget */
.widget_categories ul,
.widget_recent_entries ul,
.widget_archive ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.widget_categories li,
.widget_recent_entries li,
.widget_archive li {
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.widget_categories li:last-child,
.widget_recent_entries li:last-child,
.widget_archive li:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.widget_categories li:first-child,
.widget_recent_entries li:first-child,
.widget_archive li:first-child {
    padding-top: 0;
}

.widget_categories a,
.widget_archive a {
    color: #666;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
}

.widget_categories a::before,
.widget_archive a::before {
    content: '\f105';
    font-family: 'Line Awesome Free';
    font-weight: 900;
    margin-right: 10px;
    color: #03777d;
    transition: all 0.3s ease;
}

.widget_categories a:hover,
.widget_archive a:hover {
    color: #03777d;
    padding-left: 5px;
}

.widget_categories a:hover::before,
.widget_archive a:hover::before {
    margin-right: 15px;
}

.widget_categories a span {
    background-color: #f0f0f0;
    color: #666;
    padding: 2px 8px;
    border-radius: 20px;
    font-size: 12px;
    transition: all 0.3s ease;
}

.widget_categories a:hover span {
    background-color: #03777d;
    color: #fff;
}

/* Recent Posts Widget */
.widget_recent_entries li {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.widget_recent_entries .post-image {
    width: 70px;
    height: 70px;
    border-radius: 5px;
    overflow: hidden;
    flex-shrink: 0;
}

.widget_recent_entries .post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.widget_recent_entries li:hover .post-image img {
    transform: scale(1.05);
}

.widget_recent_entries .post-content {
    flex-grow: 1;
}

.widget_recent_entries .post-title {
    font-size: 15px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 5px;
}

.widget_recent_entries .post-title a {
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.widget_recent_entries .post-title a:hover {
    color: #03777d;
}

.widget_recent_entries .post-date {
    font-size: 12px;
    color: #888;
    display: flex;
    align-items: center;
}

.widget_recent_entries .post-date i {
    color: #03777d;
    margin-right: 5px;
}

/* Tag Cloud Widget */
.widget_tag_cloud .tagcloud {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.widget_tag_cloud .tag-cloud-link {
    display: inline-block;
    padding: 5px 15px;
    background-color: #f0f0f0;
    color: #666;
    border-radius: 20px;
    font-size: 13px !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.widget_tag_cloud .tag-cloud-link:hover {
    background: linear-gradient(135deg, #00989f, #03777d);
    color: #fff;
    transform: translateY(-2px);
}

/* No Results */
.no-results {
    text-align: center;
    padding: 50px 0;
}

.no-results .page-title {
    font-size: 28px;
    margin-bottom: 15px;
    color: #333;
}

.no-results p {
    margin-bottom: 20px;
    color: #666;
}

.no-results .search-form {
    max-width: 500px;
    margin: 0 auto 30px;
}

.no-results .icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: rgba(3, 119, 125, 0.1);
    border-radius: 50%;
    margin-bottom: 20px;
}

.no-results .icon i {
    font-size: 36px;
    color: #03777d;
}

.no-results .suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

.no-results .suggestion-tag {
    display: inline-block;
    padding: 5px 15px;
    background-color: #f0f0f0;
    color: #666;
    border-radius: 20px;
    font-size: 13px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.no-results .suggestion-tag:hover {
    background: linear-gradient(135deg, #00989f, #03777d);
    color: #fff;
    transform: translateY(-2px);
}

.no-results .category-list {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    text-align: left;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.no-results .category-list li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.no-results .category-list li:last-child {
    border-bottom: none;
}

.no-results .category-list a {
    color: #666;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
}

.no-results .category-list a:hover {
    color: #03777d;
}

.no-results .category-list a i {
    margin-right: 5px;
    color: #03777d;
}

.no-results .category-list .count {
    color: #999;
    font-size: 12px;
}

.no-results .back-to-home {
    margin-top: 30px;
}

.no-results .back-to-home .btn {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #00989f, #03777d);
    color: #fff;
    padding: 10px 20px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(3, 119, 125, 0.2);
}

.no-results .back-to-home .btn i {
    margin-right: 5px;
}

.no-results .back-to-home .btn:hover {
    background: linear-gradient(135deg, #03777d, #03355c);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(3, 119, 125, 0.3);
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .hero-area.style2 {
        padding: 80px 0;
    }

    .hero-area.style2 .page-title {
        font-size: 36px;
    }

    .blog-area {
        padding: 80px 0;
    }

    .blog-sidebar {
        margin-top: 50px;
        position: static;
    }

    .featured-post .post-content {
        padding: 20px;
    }

    .featured-post .post-title {
        font-size: 22px;
    }
}

@media only screen and (max-width: 767px) {
    .hero-area.style2 {
        padding: 60px 0;
    }

    .hero-area.style2 .page-title {
        font-size: 30px;
    }

    .hero-area.style2 p {
        font-size: 16px;
    }

    .blog-area {
        padding: 60px 0;
    }

    .featured-post .row {
        flex-direction: column;
    }

    .featured-post .post-thumbnail {
        height: 250px;
    }

    .featured-post .post-content {
        padding: 25px;
    }

    .featured-post .post-title {
        font-size: 20px;
    }

    .archive-post .post-title {
        font-size: 20px;
    }

    .archive-post .post-meta {
        font-size: 12px;
    }

    .page-numbers {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .widget {
        padding: 20px;
    }

    .widget-title {
        font-size: 18px;
    }

    .blog-sidebar {
        margin-top: 40px;
    }
}

@media only screen and (max-width: 575px) {
    .hero-area.style2 {
        padding: 50px 0;
    }

    .hero-area.style2 .page-icon {
        width: 60px;
        height: 60px;
    }

    .hero-area.style2 .page-icon i {
        font-size: 28px;
    }

    .hero-area.style2 .page-title {
        font-size: 26px;
    }

    .hero-area.style2 p {
        font-size: 15px;
    }

    .blog-area {
        padding: 50px 0;
    }

    .featured-post .post-title {
        font-size: 18px;
    }

    .archive-post .post-thumbnail img {
        height: 200px;
    }

    .archive-post .post-content {
        padding: 20px;
    }

    .archive-post .post-meta > div:not(:last-child)::after {
        display: none;
    }

    .archive-post .post-title {
        font-size: 18px;
    }

    .archive-post .read-more a {
        padding: 8px 15px;
        font-size: 13px;
    }
}
