<?php
// ملف اختبار للفورمز
require_once('functions.php');

echo "<h1>اختبار الفورمز</h1>";

// اختبار وجود الدوال
if (function_exists('zajel_handle_contact_form')) {
    echo "<p>✅ دالة zajel_handle_contact_form موجودة</p>";
} else {
    echo "<p>❌ دالة zajel_handle_contact_form غير موجودة</p>";
}

if (function_exists('zajel_handle_trial_form')) {
    echo "<p>✅ دالة zajel_handle_trial_form موجودة</p>";
} else {
    echo "<p>❌ دالة zajel_handle_trial_form غير موجودة</p>";
}

if (function_exists('zajel_validate_form_security')) {
    echo "<p>✅ دالة zajel_validate_form_security موجودة</p>";
} else {
    echo "<p>❌ دالة zajel_validate_form_security غير موجودة</p>";
}

// اختبار قاعدة البيانات
global $wpdb;

$contact_table = $wpdb->prefix . 'contact';
$trial_table = $wpdb->prefix . 'trial_bookings';

if ($wpdb->get_var("SHOW TABLES LIKE '$contact_table'") == $contact_table) {
    echo "<p>✅ جدول Contact موجود: $contact_table</p>";
} else {
    echo "<p>❌ جدول Contact غير موجود: $contact_table</p>";
}

if ($wpdb->get_var("SHOW TABLES LIKE '$trial_table'") == $trial_table) {
    echo "<p>✅ جدول Trial موجود: $trial_table</p>";
} else {
    echo "<p>❌ جدول Trial غير موجود: $trial_table</p>";
}

// اختبار بسيط للفورم
echo "<h2>اختبار Contact Form</h2>";
echo '<form method="post" action="">
    <input type="hidden" name="contact_nonce" value="' . wp_create_nonce('contact_form_action') . '">
    <input type="text" name="name" placeholder="Name" required><br><br>
    <input type="email" name="email" placeholder="Email" required><br><br>
    <input type="text" name="subject" placeholder="Subject"><br><br>
    <textarea name="message" placeholder="Message" required></textarea><br><br>
    <input type="text" name="honeypot" style="display:none;">
    <button type="submit">Test Contact Form</button>
</form>';

echo "<h2>اختبار Trial Form</h2>";
echo '<form method="post" action="">
    <input type="hidden" name="trial_form_nonce" value="' . wp_create_nonce('trial_form_action') . '">
    <input type="text" name="full_name" placeholder="Full Name" required><br><br>
    <input type="email" name="email" placeholder="Email" required><br><br>
    <input type="text" name="phone" placeholder="Phone" required><br><br>
    <select name="course" required>
        <option value="">Select Course</option>
        <option value="quran">Quran</option>
        <option value="tajweed">Tajweed</option>
        <option value="arabic">Arabic</option>
        <option value="islamic">Islamic</option>
    </select><br><br>
    <textarea name="additional_notes" placeholder="Notes"></textarea><br><br>
    <input type="text" name="honeypot" style="display:none;">
    <button type="submit">Test Trial Form</button>
</form>';

// معالجة الفورمز
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    session_start();
    
    echo "<h2>نتائج الاختبار:</h2>";
    
    if (isset($_POST['contact_nonce'])) {
        echo "<h3>Contact Form Result:</h3>";
        $result = zajel_handle_contact_form();
        echo "<p>Result: " . $result . "</p>";
    }
    
    if (isset($_POST['trial_form_nonce'])) {
        echo "<h3>Trial Form Result:</h3>";
        $result = zajel_handle_trial_form();
        echo "<p>Result: " . $result . "</p>";
    }
}
?>
