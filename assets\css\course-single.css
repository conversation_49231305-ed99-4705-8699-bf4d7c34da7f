/* Course Single Page Styles */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --transition-slow: all 0.8s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 15px 60px rgba(0, 0, 0, 0.2);
}

/* Breadcrumbs Section */
.breadcrumbs.overlay {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/teachers-bg.svg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
    z-index: 1;
    padding: 110px 0;
}

.breadcrumbs.overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a5f8d' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 0.2;
    z-index: -1;
}

.breadcrumbs-content h1 {
    color: #fff;
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 15px;
    text-transform: uppercase;
}

.breadcrumbs-content p {
    color: #fff;
    font-size: 16px;
    max-width: 600px;
    margin: 0 auto;
}

.breadcrumb-nav {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.breadcrumb-nav li {
    color: #fff;
    font-size: 14px;
    margin: 0 5px;
}

.breadcrumb-nav li a {
    color: rgba(255, 255, 255, 0.7);
    transition: var(--transition-fast);
}

.breadcrumb-nav li a:hover {
    color: #fff;
}

/* Course Featured Image */
.course-featured-image {
    margin-bottom: 30px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-small);
}

.course-featured-image img {
    width: 100%;
    height: auto;
    transition: var(--transition-medium);
}

/* Tabs Styling */
.course-details {
    margin-top: 50px;
    margin-bottom: 50px;
}

.course-details .nav-tabs {
    border: none;
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 5px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-small);
    display: flex;
    justify-content: space-between;
    overflow: hidden;
}

.course-details .nav-tabs .nav-item {
    flex: 1;
    text-align: center;
    margin: 0;
    position: relative;
    z-index: 1;
}

.course-details .nav-tabs .nav-link {
    border: none;
    border-radius: 8px;
    padding: 15px 20px;
    font-weight: 600;
    color: #555;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-details .nav-tabs .nav-link i {
    margin-right: 8px;
    font-size: 18px;
    transition: var(--transition-fast);
}

.course-details .nav-tabs .nav-link:hover {
    color: white;
    background-color: var(--gold-dark);
}

.course-details .nav-tabs .nav-link.active {
    color: white;
    background: var(--gold-dark);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.2);
}

.course-details .nav-tabs .nav-link.active i {
    color: white;
}

.course-details .nav-tabs .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
    z-index: -1;
}

.course-details .nav-tabs .nav-link:hover::before {
    left: 100%;
    transition: 0.7s;
}

/* Tab Content Styling */
.course-details .tab-content {
    padding: 30px 0;
    position: relative;
}

.course-details .tab-content .title {
    font-size: 24px;
    margin-bottom: 25px;
    color: var(--gold-dark);
    position: relative;
    padding-bottom: 15px;
    font-weight: 700;
}

.course-details .tab-content .title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(to right, var(--gold-light), var(--gold-dark));
    border-radius: 2px;
    transition: var(--transition-medium);
}

.course-details .tab-content .title:hover:after {
    width: 100px;
}

.course-details .tab-pane {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Overview Tab */
.course-content {
    margin-bottom: 30px;
    line-height: 1.8;
}

.course-highlights {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
}

.course-highlights h4 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--gold-dark);
}

.highlights-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.highlights-list li {
    padding: 8px 0;
    display: flex;
    align-items: flex-start;
}

.highlights-list li i {
    color: var(--gold-medium);
    margin-right: 10px;
    margin-top: 5px;
}

.overview-course-video {
    margin: 30px 0;
}

.overview-course-video h4 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--gold-dark);
}

.video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: var(--shadow-medium);
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

/* Curriculum Tab */
.curriculum-description {
    margin-bottom: 30px;
    background-color: rgba(26, 95, 141, 0.05);
    padding: 25px;
    border-radius: 10px;
    border-left: 4px solid var(--gold-medium);
}

.curriculum-sections {
    list-style: none;
    padding: 0;
    margin: 0;
}

.single-curriculum-section {
    margin-bottom: 30px;
    border: 1px solid #eee;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
}

.single-curriculum-section:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-5px);
}

.section-header {
    background: linear-gradient(45deg, rgba(26, 95, 141, 0.05), rgba(3, 53, 92, 0.1));
    padding: 20px;
    border-bottom: 1px solid #eee;
    transition: var(--transition-fast);
}

.single-curriculum-section:hover .section-header {
    background: linear-gradient(45deg, rgba(26, 95, 141, 0.1), rgba(3, 53, 92, 0.15));
}

.section-left {
    display: flex;
    align-items: center;
}

.section-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: white;
    border-radius: 50%;
    margin-right: 15px;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(26, 95, 141, 0.2);
    transition: var(--transition-fast);
}

.single-curriculum-section:hover .section-number {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.3);
}

.section-left .title {
    margin: 0;
    padding: 0;
    font-size: 18px;
    color: var(--gold-dark);
    font-weight: 600;
}

.section-left .title:after {
    display: none;
}

.section-desc {
    margin: 10px 0 0 51px;
    color: #777;
    font-size: 14px;
    line-height: 1.6;
}

.section-content {
    list-style: none;
    padding: 0;
    margin: 0;
}

.course-item {
    border-bottom: 1px solid #eee;
    transition: var(--transition-fast);
}

.course-item:last-child {
    border-bottom: none;
}

.section-item-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #333;
    transition: var(--transition-fast);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.section-item-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(26, 95, 141, 0.05), transparent);
    transition: var(--transition-fast);
    z-index: 0;
}

.section-item-link:hover::before {
    left: 100%;
    transition: 0.7s;
}

.section-item-link:hover {
    background-color: rgba(26, 95, 141, 0.03);
    color: var(--gold-dark);
}

.lesson-number {
    color: #777;
    margin-right: 15px;
    font-size: 14px;
    min-width: 30px;
    font-weight: 600;
    color: var(--gold-medium);
    transition: var(--transition-fast);
}

.section-item-link:hover .lesson-number {
    color: var(--gold-dark);
}

.item-name {
    flex: 1;
    position: relative;
    z-index: 1;
}

.course-item-meta {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
}

.item-meta {
    margin-left: 15px;
    font-size: 13px;
    color: #777;
    display: flex;
    align-items: center;
    transition: var(--transition-fast);
}

.item-meta i {
    margin-right: 5px;
    transition: var(--transition-fast);
}

.section-item-link:hover .item-meta {
    color: var(--gold-medium);
}

.item-meta-icon i {
    color: var(--gold-medium);
}

.section-item-link:hover .item-meta-icon i {
    color: var(--gold-dark);
}

.empty-lessons {
    padding: 20px;
    text-align: center;
    color: #777;
    font-style: italic;
}

.no-curriculum-message {
    text-align: center;
    padding: 40px 20px;
    color: #777;
    background-color: rgba(26, 95, 141, 0.05);
    border-radius: 10px;
    margin-top: 20px;
}

.no-curriculum-message i {
    font-size: 48px;
    color: rgba(26, 95, 141, 0.2);
    margin-bottom: 15px;
    display: block;
}

/* Instructor Tab */
.instructor-profile {
    background: linear-gradient(45deg, rgba(26, 95, 141, 0.03), rgba(3, 53, 92, 0.05));
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-small);
    border: 1px solid rgba(26, 95, 141, 0.1);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.instructor-profile:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-5px);
}

.instructor-profile::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a5f8d' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 1;
    z-index: 0;
}

.instructor-profile-inner {
    position: relative;
    z-index: 1;
    display: flex;
    flex-wrap: wrap;
}

.profile-image-col {
    flex: 0 0 200px;
    margin-right: 30px;
}

.profile-info-col {
    flex: 1;
}

.profile-image {
    position: relative;
    margin-bottom: 20px;
}

.profile-image img {
    width: 100%;
    border-radius: 10px;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
}

.instructor-profile:hover .profile-image img {
    transform: scale(1.03);
    box-shadow: var(--shadow-medium);
}

.experience-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 11px;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.experience-badge span {
    font-size: 16px;
    font-weight: bold;
    line-height: 1;
}

.experience-badge small {
    font-size: 9px;
    text-transform: uppercase;
    margin-top: 2px;
}

.instructor-profile:hover .experience-badge {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.instructor-name {
    font-size: 24px;
    margin-bottom: 5px;
    font-weight: 700;
    color: var(--gold-dark);
}

.instructor-name a {
    color: var(--gold-dark);
    text-decoration: none;
    transition: var(--transition-fast);
}

.instructor-name a:hover {
    color: var(--gold-medium);
}

.instructor-designation {
    color: #777;
    margin-bottom: 15px;
    font-size: 16px;
    display: flex;
    align-items: center;
}

.instructor-designation i {
    color: var(--gold-medium);
    margin-right: 8px;
}

.instructor-bio {
    margin-bottom: 20px;
    line-height: 1.7;
    color: #555;
}

.instructor-expertise {
    margin-bottom: 20px;
}

.instructor-expertise h5 {
    font-size: 16px;
    margin-bottom: 10px;
    color: var(--gold-dark);
    font-weight: 600;
    display: flex;
    align-items: center;
}

.instructor-expertise h5 i {
    margin-right: 8px;
    color: var(--gold-medium);
}

.expertise-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.expertise-tag {
    background-color: rgba(26, 95, 141, 0.1);
    color: var(--gold-dark);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
    transition: var(--transition-fast);
    display: inline-flex;
    align-items: center;
}

.expertise-tag i {
    margin-right: 5px;
    font-size: 12px;
}

.expertise-tag:hover {
    background-color: var(--gold-dark);
    color: white;
    transform: translateY(-2px);
}

.instructor-social-networks {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
}

.instructor-social-networks .item {
    margin-right: 10px;
}

.instructor-social-networks .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: rgba(26, 95, 141, 0.1);
    color: var(--gold-dark);
    border-radius: 50%;
    transition: var(--transition-fast);
}

.instructor-social-networks .social-link:hover {
    background-color: var(--gold-dark);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.2);
}

.instructor-cta {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

.instructor-cta .btn {
    padding: 10px 20px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 14px;
    transition: var(--transition-fast);
    display: inline-flex;
    align-items: center;
}

.instructor-cta .btn i {
    margin-right: 8px;
}

.instructor-cta .btn-primary {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: white;
    border: none;
}

.instructor-cta .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.3);
}

.instructor-cta .btn-outline {
    border: 1px solid var(--gold-medium);
    color: var(--gold-medium);
    background: transparent;
}

.instructor-cta .btn-outline:hover {
    background-color: var(--gold-medium);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.2);
}

.no-instructor-message {
    text-align: center;
    padding: 40px 20px;
    color: #777;
    background-color: rgba(26, 95, 141, 0.05);
    border-radius: 10px;
    margin-top: 20px;
}

.no-instructor-message i {
    font-size: 48px;
    color: rgba(26, 95, 141, 0.2);
    margin-bottom: 15px;
    display: block;
}

@media (max-width: 767px) {
    .instructor-profile-inner {
        flex-direction: column;
    }

    .profile-image-col {
        flex: 0 0 100%;
        margin-right: 0;
        margin-bottom: 20px;
    }

    .profile-info-col {
        flex: 0 0 100%;
    }
}

/* Reviews Tab */
.rating-summary {
    background: linear-gradient(45deg, rgba(26, 95, 141, 0.03), rgba(3, 53, 92, 0.05));
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 30px;
    text-align: center;
    box-shadow: var(--shadow-small);
    border: 1px solid rgba(26, 95, 141, 0.1);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.rating-summary:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-5px);
}

.rating-summary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a5f8d' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 1;
    z-index: 0;
}

.average-rating {
    display: inline-block;
    position: relative;
    z-index: 1;
}

.rating-number {
    font-size: 48px;
    font-weight: 700;
    color: var(--gold-dark);
    line-height: 1;
    margin-bottom: 10px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.rating-number .max {
    font-size: 24px;
    color: #777;
    -webkit-text-fill-color: #777;
}

.rating-stars {
    margin-bottom: 10px;
    font-size: 24px;
}

.rating-stars i {
    color: var(--gold-light);
    margin: 0 2px;
    transition: var(--transition-fast);
}

.rating-stars i:hover {
    transform: scale(1.2);
}

.rating-count {
    color: #777;
    font-size: 16px;
    font-weight: 500;
}

.post-comments {
    margin-top: 30px;
}

.comment-title {
    font-size: 20px;
    margin-bottom: 20px;
    color: var(--gold-dark);
    position: relative;
    padding-bottom: 10px;
    font-weight: 600;
}

.comment-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(to right, var(--gold-light), var(--gold-dark));
    border-radius: 2px;
}

.comment-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.comment {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 10px;
    box-shadow: var(--shadow-small);
    transition: var(--transition-fast);
}

.comment:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-3px);
}

.comment-author {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.comment-author img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
    object-fit: cover;
}

.comment-author-info {
    flex: 1;
}

.comment-author-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--gold-dark);
    margin-bottom: 5px;
}

.comment-date {
    font-size: 12px;
    color: #777;
}

.comment-rating {
    margin-bottom: 10px;
}

.comment-rating i {
    color: var(--gold-light);
    font-size: 14px;
    margin-right: 2px;
}

.comment-content p {
    margin: 0;
    line-height: 1.6;
    color: #555;
}

.no-reviews-message {
    text-align: center;
    padding: 40px 20px;
    color: #777;
    background-color: rgba(26, 95, 141, 0.05);
    border-radius: 10px;
    margin-top: 20px;
    position: relative;
    overflow: hidden;
}

.no-reviews-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a5f8d' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 1;
    z-index: 0;
}

.no-reviews-message i {
    font-size: 48px;
    color: rgba(26, 95, 141, 0.2);
    margin-bottom: 15px;
    display: block;
    position: relative;
    z-index: 1;
}

.no-reviews-message p {
    position: relative;
    z-index: 1;
}

.review-cta {
    text-align: center;
    margin-top: 30px;
    padding: 30px;
    background: linear-gradient(45deg, rgba(26, 95, 141, 0.03), rgba(3, 53, 92, 0.05));
    border-radius: 10px;
    box-shadow: var(--shadow-small);
    border: 1px solid rgba(26, 95, 141, 0.1);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.review-cta:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-5px);
}

.review-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a5f8d' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 1;
    z-index: 0;
}

.review-cta p {
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
    font-size: 16px;
    color: #555;
}

.review-cta .btn {
    position: relative;
    z-index: 1;
    padding: 10px 25px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 14px;
    transition: var(--transition-fast);
    display: inline-flex;
    align-items: center;
}

.review-cta .btn i {
    margin-right: 8px;
}

.review-cta .btn-outline {
    border: 1px solid var(--gold-medium);
    color: var(--gold-medium);
    background: transparent;
}

.review-cta .btn-outline:hover {
    background-color: var(--gold-medium);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.2);
}

/* Sidebar Styling */
.course-sidebar {
    position: sticky;
    top: 30px;
}

.sidebar-widget {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--shadow-small);
    margin-bottom: 30px;
    overflow: hidden;
    transition: var(--transition-medium);
    border: 1px solid rgba(26, 95, 141, 0.1);
}

.sidebar-widget:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-5px);
}

.course-info-header {
    background: var(--gold-dark);
    padding: 20px;
    color: white;
    position: relative;
    overflow: hidden;
}

.course-info-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
    z-index: 0;
}

.sidebar-widget:hover .course-info-header::before {
    left: 100%;
    transition: 0.7s;
}

.sidebar-widget-title {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.sidebar-widget-content {
    padding: 25px;
    position: relative;
}

.sidebar-widget-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a5f8d' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 1;
    z-index: 0;
}

.course-info-list {
    list-style: none;
    padding: 0;
    margin: 0 0 25px 0;
    position: relative;
    z-index: 1;
}

.course-info-list li {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(26, 95, 141, 0.1);
    transition: var(--transition-fast);
}

.course-info-list li:last-child {
    border-bottom: none;
}

.course-info-list li:hover {
    transform: translateX(5px);
}

.course-info-list li i {
    color: var(--gold-medium);
    margin-right: 15px;
    font-size: 20px;
    min-width: 20px;
    text-align: center;
    transition: var(--transition-fast);
}

.course-info-list li:hover i {
    color: var(--gold-dark);
    transform: scale(1.2);
}

.course-info-list li strong {
    color: var(--gold-dark);
    margin-right: 5px;
    font-weight: 600;
}

.course-action {
    margin-top: 25px;
    position: relative;
    z-index: 1;
}

.btn-block {
    display: block;
    width: 100%;
    text-align: center;
    margin-bottom: 15px;
    padding: 12px 20px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 16px;
    transition: var(--transition-fast);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-block i {
    margin-right: 8px;
}

.btn-primary {
    background: var(--gold-dark);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
    z-index: -1;
}

.btn-primary:hover::before {
    left: 100%;
    transition: 0.7s;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.3);
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--gold-dark);
    color: var(--gold-dark);
}

.btn-outline:hover {
    background-color: var(--gold-dark);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.2);
}

.course-tags-widget .sidebar-widget-title {
    color: var(--gold-dark);
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.course-tags-widget .sidebar-widget-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--gold-dark);
    border-radius: 2px;
    transition: var(--transition-medium);
}

.course-tags-widget:hover .sidebar-widget-title::after {
    width: 60px;
}

.course-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    position: relative;
    z-index: 1;
}

.course-tag {
    background-color: rgba(26, 95, 141, 0.1);
    color: var(--gold-dark);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    transition: var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.course-tag i {
    margin-right: 5px;
    font-size: 12px;
}

.course-tag:hover {
    background-color: var(--gold-dark);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.2);
}

.other-course-widget .sidebar-widget-title {
    color: var(--gold-dark);
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.other-course-widget .sidebar-widget-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: var(--gold-dark);
    border-radius: 2px;
    transition: var(--transition-medium);
}

.other-course-widget:hover .sidebar-widget-title::after {
    width: 60px;
}

.sidebar-widget-course {
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
    z-index: 1;
}

.sidebar-widget-course .single-course {
    display: flex;
    padding: 15px 0;
    border-bottom: 1px solid rgba(26, 95, 141, 0.1);
    transition: var(--transition-fast);
}

.sidebar-widget-course .single-course:last-child {
    border-bottom: none;
}

.sidebar-widget-course .single-course:hover {
    transform: translateX(5px);
}

.sidebar-widget-course .thumbnail {
    width: 80px;
    height: 80px;
    margin-right: 15px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-small);
    transition: var(--transition-fast);
}

.sidebar-widget-course .single-course:hover .thumbnail {
    box-shadow: var(--shadow-medium);
}

.sidebar-widget-course .thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-medium);
}

.sidebar-widget-course .single-course:hover .thumbnail img {
    transform: scale(1.1);
}

.sidebar-widget-course .info {
    flex: 1;
}

.sidebar-widget-course .title {
    margin: 0 0 8px 0;
    font-size: 16px;
    line-height: 1.4;
    font-weight: 600;
}

.sidebar-widget-course .title a {
    color: var(--gold-dark);
    text-decoration: none;
    transition: var(--transition-fast);
}

.sidebar-widget-course .title a:hover {
    color: var(--gold-medium);
}

.sidebar-widget-course .meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 13px;
    color: #777;
}

.sidebar-widget-course .meta span {
    display: flex;
    align-items: center;
    transition: var(--transition-fast);
}

.sidebar-widget-course .meta i {
    margin-right: 5px;
    color: var(--gold-medium);
    transition: var(--transition-fast);
}

.sidebar-widget-course .single-course:hover .meta span {
    color: var(--gold-medium);
}

.sidebar-widget-course .single-course:hover .meta i {
    color: var(--gold-dark);
}

.no-courses-message {
    padding: 20px 0;
    text-align: center;
    color: #777;
    font-style: italic;
    position: relative;
    z-index: 1;
}

.view-all-courses {
    text-align: center;
    margin-top: 25px;
    position: relative;
    z-index: 1;
}

.view-all-courses .btn {
    padding: 8px 20px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 14px;
    transition: var(--transition-fast);
    display: inline-flex;
    align-items: center;
}

.view-all-courses .btn i {
    margin-left: 5px;
    transition: var(--transition-fast);
}

.view-all-courses .btn:hover i {
    transform: translateX(3px);
}

/* Bottom Content */
.bottom-content {
    margin-top: 30px;
    padding: 25px;
    border: 1px solid rgba(26, 95, 141, 0.1);
    border-radius: 10px;
    background: linear-gradient(45deg, rgba(26, 95, 141, 0.03), rgba(3, 53, 92, 0.05));
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.bottom-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a5f8d' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 1;
    z-index: 0;
}

.bottom-content:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-5px);
}

.bottom-content .button {
    position: relative;
    z-index: 1;
}

.bottom-content .btn {
    padding: 10px 25px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 14px;
    transition: var(--transition-fast);
    display: inline-flex;
    align-items: center;
}

.bottom-content .btn i {
    margin-right: 8px;
}

.bottom-content .btn-primary {
    background: var(--gold-dark);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.bottom-content .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
    z-index: -1;
}

.bottom-content .btn-primary:hover::before {
    left: 100%;
    transition: 0.7s;
}

.bottom-content .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.3);
}

.share {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
    z-index: 1;
}

.share li {
    margin-left: 10px;
}

.share li:first-child {
    margin-left: 0;
    margin-right: 10px;
    font-weight: 600;
    color: var(--gold-dark);
}

.share a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: rgba(26, 95, 141, 0.1);
    color: var(--gold-dark);
    border-radius: 50%;
    transition: var(--transition-fast);
}

.share a:hover {
    background-color: var(--gold-dark);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.2);
}

/* Responsive Styles */
@media (max-width: 991px) {
    .course-sidebar {
        margin-top: 50px;
        position: static;
    }

    .breadcrumbs-content h1 {
        font-size: 30px;
    }

    .breadcrumbs-content p {
        font-size: 14px;
    }

    .course-details {
        margin-top: 30px;
    }

    .instructor-profile-inner {
        flex-direction: column;
    }

    .profile-image-col {
        flex: 0 0 100%;
        margin-right: 0;
        margin-bottom: 20px;
    }

    .profile-info-col {
        flex: 0 0 100%;
    }

    .instructor-cta {
        flex-direction: column;
    }

    .instructor-cta .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

@media (max-width: 767px) {
    .breadcrumbs.overlay {
        padding: 60px 0;
    }

    .breadcrumbs-content h1 {
        font-size: 26px;
    }

    .course-details .nav-tabs {
        padding: 3px;
    }

    .course-details .nav-tabs .nav-link {
        padding: 10px;
        font-size: 14px;
    }

    .course-details .nav-tabs .nav-link i {
        margin-right: 5px;
    }

    .instructor-profile {
        padding: 20px;
    }

    .rating-summary {
        padding: 20px;
    }

    .rating-number {
        font-size: 36px;
    }

    .rating-stars {
        font-size: 20px;
    }

    .bottom-content {
        padding: 20px;
    }

    .bottom-content .row {
        flex-direction: column;
    }

    .bottom-content .share {
        justify-content: center;
        margin-top: 15px;
    }

    .course-info-list li strong {
        display: block;
        margin-bottom: 5px;
    }

    .course-action .btn-block {
        padding: 10px 15px;
        font-size: 14px;
    }
}

@media (max-width: 575px) {
    .breadcrumbs.overlay {
        padding: 40px 0;
    }

    .breadcrumbs-content h1 {
        font-size: 22px;
        margin-bottom: 10px;
    }

    .breadcrumbs-content p {
        font-size: 13px;
    }

    .breadcrumb-nav li {
        font-size: 12px;
    }

    .course-details {
        margin-top: 20px;
    }

    .course-details .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        white-space: nowrap;
        padding-bottom: 5px;
    }

    .course-details .nav-tabs .nav-item {
        margin-right: 2px;
    }

    .course-details .nav-tabs .nav-link {
        padding: 8px 10px;
        font-size: 12px;
    }

    .course-details .nav-tabs .nav-link i {
        margin-right: 3px;
        font-size: 14px;
    }

    .course-details .tab-content .title {
        font-size: 20px;
    }

    .instructor-profile {
        padding: 15px;
    }

    .instructor-name {
        font-size: 18px;
    }

    .rating-number {
        font-size: 30px;
    }

    .rating-stars {
        font-size: 18px;
    }

    .share li:first-child {
        display: none;
    }

    .share li {
        margin-left: 5px;
    }

    .share a {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .course-featured-image {
        margin-bottom: 20px;
    }

    .course-content {
        font-size: 14px;
    }

    .section-header {
        padding: 15px;
    }

    .section-number {
        width: 30px;
        height: 30px;
        font-size: 14px;
    }

    .section-left .title {
        font-size: 16px;
    }

    .section-desc {
        margin-left: 45px;
        font-size: 13px;
    }

    .section-item-link {
        padding: 12px 15px;
        font-size: 13px;
    }

    .review-cta p {
        font-size: 14px;
    }
}
