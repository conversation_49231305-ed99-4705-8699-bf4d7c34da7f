/* Animations CSS for Zajel Arabic Theme */

/* Define CSS Variables for Gold Colors */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
    --white: #ffffff;
    --black: #000000;
    --gray-light: #f5f5f5;
    --gray-medium: #e0e0e0;
    --shadow-small: 0 2px 5px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 5px 15px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 10px 25px rgba(0, 0, 0, 0.15);
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --transition-slow: all 0.8s ease;
}

/* Basic Animation Classes */
.fade-in {
    opacity: 0;
    animation: fadeIn 1s ease forwards;
}

.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease forwards;
}

.fade-in-down {
    opacity: 0;
    transform: translateY(-30px);
    animation: fadeInDown 1s ease forwards;
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    animation: fadeInLeft 1s ease forwards;
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    animation: fadeInRight 1s ease forwards;
}

.zoom-in {
    opacity: 0;
    transform: scale(0.9);
    animation: zoomIn 1s ease forwards;
}

/* Animation Delays */
.delay-1 {
    animation-delay: 0.2s;
}

.delay-2 {
    animation-delay: 0.4s;
}

.delay-3 {
    animation-delay: 0.6s;
}

.delay-4 {
    animation-delay: 0.8s;
}

.delay-5 {
    animation-delay: 1s;
}

/* Animation Keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Hover Animations */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.hover-grow {
    transition: transform 0.3s ease;
}

.hover-grow:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 15px var(--gold-light);
}

/* Scroll Animation Trigger */
.animate-on-scroll {
    opacity: 0;
    transition: var(--transition-medium);
}

.animate-on-scroll.visible {
    opacity: 1;
}

/* Button Animations */
.btn {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: var(--transition-fast);
    z-index: -1;
}

.btn:hover::before {
    left: 100%;
}

/* Gold Gradient Text */
.gold-gradient-text {
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

/* Pulse Animation for CTA elements */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(3, 53, 92, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(3, 53, 92, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(3, 53, 92, 0);
    }
}

/* Experience Badge */
.experience-badge {
    position: absolute;
    bottom: 30px;
    right: 30px;
    width: 120px;
    height: 120px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    box-shadow: 0 10px 30px rgba(3, 53, 92, 0.3);
    animation: pulse 2s infinite;
    z-index: 10;
}

.experience-badge .years {
    font-size: 36px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 5px;
}

.experience-badge .text {
    font-size: 14px;
    text-align: center;
    line-height: 1.2;
}

/* Floating Animation */
.float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .fade-in-up, .fade-in-down, .fade-in-left, .fade-in-right {
        animation-duration: 0.8s;
    }

    .delay-1 {
        animation-delay: 0.1s;
    }

    .delay-2 {
        animation-delay: 0.2s;
    }

    .delay-3 {
        animation-delay: 0.3s;
    }

    .delay-4 {
        animation-delay: 0.4s;
    }

    .delay-5 {
        animation-delay: 0.5s;
    }
}

@media only screen and (max-width: 767px) {
    .fade-in-up, .fade-in-down, .fade-in-left, .fade-in-right {
        animation-duration: 0.6s;
    }

    /* Improve spacing on mobile */
    .section {
        padding: 60px 0;
    }

    .section-title {
        margin-bottom: 30px;
    }

    /* Make cards full width on mobile */
    .single-service, .single-course, .single-testimonial, .single-team, .single-news {
        margin-bottom: 30px;
    }

    /* Improve courses on mobile */
    .courses .single-course {
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .courses .single-course .content {
        padding: 20px;
    }

    .courses .single-course .course-image img {
        height: 200px;
    }

    .courses .single-course .content h3 a {
        font-size: 18px;
    }

    .courses .single-course .content .button .btn {
        width: 100%;
        text-align: center;
    }

    /* Adjust experience badge for small screens */
    .experience-badge {
        width: 100px;
        height: 100px;
        bottom: 20px;
        right: 20px;
    }

    .experience-badge .years {
        font-size: 28px;
    }

    .experience-badge .text {
        font-size: 12px;
    }
}

/* Hero Section Specific Animations */
.hero-area .hero-text h1 span {
    color: var(--gold-dark);
    position: relative;
    display: inline-block;
}

.hero-area .hero-text h1 span::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gold-light);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.5s ease;
}

.hero-area .hero-text h1 span.animated::after {
    transform: scaleX(1);
    transform-origin: left;
}

/* Mission & Vision Section Animations */
.mission-vision-section {
    position: relative;
    padding: 80px 0;
    background: linear-gradient(135deg, #fff 0%, #f9f9f9 100%);
    overflow: hidden;
}

.mission-vision-section::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(3, 53, 92, 0.05);
    z-index: 1;
}

.mission-vision-section::after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(3, 53, 92, 0.05);
    z-index: 1;
}

.mission-vision-section .container {
    position: relative;
    z-index: 2;
}

.mission-vision-card {
    position: relative;
    background: #fff;
    border-radius: 15px;
    padding: 40px 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.4s ease;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.mission-vision-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(3, 53, 92, 0.2);
}

.mission-vision-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--gold-dark), var(--gold-light));
}

.mission-vision-card .icon-box {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(3, 53, 92, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    position: relative;
    transition: all 0.4s ease;
}

.mission-vision-card:hover .icon-box {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
}

.mission-vision-card .icon-box i {
    font-size: 36px;
    color: var(--gold-dark);
    transition: all 0.4s ease;
}

.mission-vision-card:hover .icon-box i {
    color: #fff;
    transform: rotateY(360deg);
}

.mission-vision-card .icon-box::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    border: 2px dashed rgba(3, 53, 92, 0.3);
    animation: spin 20s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.mission-vision-card h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #333;
    position: relative;
    padding-bottom: 15px;
    transition: all 0.3s ease;
}

.mission-vision-card:hover h3 {
    color: var(--gold-dark);
}

.mission-vision-card h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--gold-dark);
    transition: all 0.3s ease;
}

.mission-vision-card:hover h3::after {
    width: 80px;
}

.mission-vision-card p {
    font-size: 16px;
    line-height: 1.7;
    color: #666;
    margin-bottom: 25px;
    flex-grow: 1;
}

.mission-vision-card .values-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.mission-vision-card .values-list li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 15px;
    font-size: 15px;
    color: #555;
    transition: all 0.3s ease;
}

.mission-vision-card .values-list li:last-child {
    margin-bottom: 0;
}

.mission-vision-card .values-list li i {
    position: absolute;
    left: 0;
    top: 3px;
    color: var(--gold-dark);
    font-size: 18px;
}

.mission-vision-card .values-list li:hover {
    color: var(--gold-dark);
    transform: translateX(5px);
}

.mission-vision-card .btn {
    margin-top: 20px;
    align-self: flex-start;
}

/* Core Values Cards */
.core-value-card {
    position: relative;
    background: #fff;
    border-radius: 15px;
    padding: 30px 20px;
    margin-bottom: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.4s ease;
    overflow: hidden;
    height: 100%;
}

.values-grid {
    margin-top: 20px;
    margin-bottom: 30px;
}

.values-grid .col-lg-3 {
    padding: 15px;
}

@media only screen and (max-width: 991px) {
    .values-grid {
        margin-top: 10px;
    }

    .values-grid .col-lg-3 {
        padding: 10px;
    }
}

@media only screen and (max-width: 767px) {
    .mission-vision-section .values-grid .col-sm-6 {
        width: 50%;
        padding: 10px;
    }

    .core-value-card {
        padding: 20px 15px;
        margin-bottom: 0;
        height: 100%;
    }

    .core-value-card .icon-box {
        width: 50px;
        height: 50px;
        margin-bottom: 15px;
    }

    .core-value-card .icon-box i {
        font-size: 24px;
    }

    .core-value-card h4 {
        font-size: 16px;
        margin-bottom: 10px;
    }

    .core-value-card p {
        font-size: 13px;
        line-height: 1.4;
    }
}

@media only screen and (max-width: 575px) {
    .mission-vision-section .values-grid .col-sm-6 {
        width: 50%;
        padding: 8px;
    }

    .core-value-card {
        padding: 15px 10px;
    }

    .core-value-card .icon-box {
        width: 40px;
        height: 40px;
        margin-bottom: 10px;
    }

    .core-value-card .icon-box i {
        font-size: 20px;
    }

    .core-value-card h4 {
        font-size: 14px;
        margin-bottom: 8px;
    }

    .core-value-card p {
        font-size: 12px;
        line-height: 1.3;
    }
}

.core-value-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(32, 107, 148, 0.15);
}

.core-value-card .icon-box {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: rgba(32, 107, 148, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    position: relative;
    transition: all 0.4s ease;
}

.core-value-card:hover .icon-box {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
}

.core-value-card .icon-box i {
    font-size: 30px;
    color: var(--gold-dark);
    transition: all 0.4s ease;
}

.core-value-card:hover .icon-box i {
    color: #fff;
    transform: rotateY(360deg);
}

.core-value-card h4 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
    transition: all 0.3s ease;
}

.core-value-card:hover h4 {
    color: var(--gold-dark);
}

.core-value-card p {
    font-size: 15px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 0;
}

.mission-vision-section .section-title {
    margin-bottom: 50px;
}

.mission-vision-section .section-title h2 {
    position: relative;
    display: inline-block;
}

.mission-vision-section .section-title h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--gold-dark), var(--gold-light));
}

.mission-vision-section .section-title p {
    max-width: 700px;
    margin: 20px auto 0;
}

.mission-vision-section .decoration-image {
    position: absolute;
    opacity: 0.1;
    z-index: 1;
}

.mission-vision-section .decoration-image.top-right {
    top: 50px;
    right: 50px;
    animation: float 6s ease-in-out infinite;
}

.mission-vision-section .decoration-image.bottom-left {
    bottom: 50px;
    left: 50px;
    animation: float 8s ease-in-out infinite reverse;
}

@media only screen and (max-width: 991px) {
    .mission-vision-card {
        padding: 30px 20px;
    }

    .mission-vision-card .icon-box {
        width: 70px;
        height: 70px;
    }

    .mission-vision-card .icon-box i {
        font-size: 30px;
    }

    .mission-vision-card h3 {
        font-size: 22px;
    }
}

@media only screen and (max-width: 767px) {
    .mission-vision-section {
        padding: 60px 0;
    }

    .mission-vision-card {
        margin-bottom: 30px;
    }

    .mission-vision-section .col-md-6:last-child .mission-vision-card {
        margin-bottom: 0;
    }

    /* Core Values Cards */
    .core-value-card {
        padding: 30px 20px;
        margin-bottom: 30px;
    }
}

/* Why Choose Us Section */
.why-choose-us-section {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg, #fff 0%, #f9f9f9 100%);
    overflow: hidden;
}

.why-choose-us-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="1" fill="%23206B94" opacity="0.1"/></svg>');
    background-size: 20px 20px;
    opacity: 0.5;
    z-index: 0;
}

.why-choose-us-section .container {
    position: relative;
    z-index: 1;
}

.why-choose-image {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    background-color: #fff;
    padding: 15px;
    border: 1px solid rgba(3, 53, 92, 0.2);
    transition: all 0.5s ease;
}

.why-choose-image:hover {
    box-shadow: 0 20px 50px rgba(3, 53, 92, 0.2);
    transform: translateY(-10px);
}

.why-choose-image img {
    width: 100%;
    border-radius: 15px;
    transition: all 0.5s ease;
}

.why-choose-image:hover img {
    transform: scale(1.05);
}

.experience-badge {
    position: absolute;
    bottom: 30px;
    right: 30px;
    width: 120px;
    height: 120px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    box-shadow: 0 10px 30px rgba(3, 53, 92, 0.3);
    animation: pulse 2s infinite;
    z-index: 10;
}

.experience-badge .years {
    font-size: 36px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 5px;
}

.experience-badge .text {
    font-size: 14px;
    text-align: center;
    line-height: 1.2;
}

.single-feature {
    position: relative;
    padding: 30px 25px;
    margin-bottom: 30px;
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.single-feature:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(32, 107, 148, 0.15);
}

.single-feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 0;
    background: linear-gradient(to bottom, var(--gold-dark), var(--gold-light));
    transition: all 0.3s ease;
}

.single-feature:hover::before {
    height: 100%;
}

.single-feature .icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: rgba(32, 107, 148, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    transition: all 0.3s ease;
    position: relative;
    margin-left: auto;
    margin-right: auto;
}

.single-feature .icon::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    border: 2px dashed rgba(32, 107, 148, 0.3);
    animation: spin 20s linear infinite;
    opacity: 0;
    transition: all 0.3s ease;
}

.single-feature:hover .icon::after {
    opacity: 1;
}

.single-feature:hover .icon {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
}

.single-feature .icon i {
    font-size: 28px;
    color: var(--gold-dark);
    transition: all 0.3s ease;
}

.single-feature:hover .icon i {
    color: #fff;
    transform: rotateY(360deg);
}

.single-feature h4 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    padding-bottom: 15px;
}

.single-feature h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background: var(--gold-dark);
    transition: all 0.3s ease;
}

.single-feature:hover h4 {
    color: var(--gold-dark);
}

.single-feature:hover h4::after {
    width: 60px;
}

.single-feature p {
    font-size: 15px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 0;
    text-align: center;
}

.features-grid {
    margin-top: 20px;
    margin-bottom: 20px;
}

.features-grid .col-md-6 {
    padding: 15px;
}

.why-choose-content {
    padding: 20px 0;
}

@media only screen and (max-width: 991px) {
    .why-choose-us-section {
        padding: 60px 0;
    }

    .experience-badge {
        width: 100px;
        height: 100px;
        bottom: 20px;
        right: 20px;
    }

    .experience-badge .years {
        font-size: 30px;
    }

    .experience-badge .text {
        font-size: 12px;
    }
}

@media only screen and (max-width: 767px) {
    .why-choose-us-section {
        padding: 70px 0;
    }

    .why-choose-image {
        margin-bottom: 50px;
    }

    .experience-badge {
        width: 90px;
        height: 90px;
        bottom: 20px;
        right: 20px;
    }

    .experience-badge .years {
        font-size: 28px;
    }

    .experience-badge .text {
        font-size: 12px;
    }

    .features-grid .col-md-6 {
        padding: 10px;
    }

    .single-feature {
        padding: 25px 20px;
        margin-bottom: 20px;
    }

    .single-feature .icon {
        width: 60px;
        height: 60px;
        margin-bottom: 20px;
    }

    .single-feature .icon i {
        font-size: 26px;
    }

    .single-feature h4 {
        font-size: 18px;
        padding-bottom: 12px;
        margin-bottom: 12px;
    }

    .single-feature p {
        font-size: 14px;
    }

    .why-choose-content {
        padding: 0;
    }
}

/* Video Button Animation */
.video-button {
    position: relative;
}

.video-button::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: rgba(32, 107, 148, 0.3);
    animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
}

@keyframes pulse-ring {
    0% {
        transform: scale(0.8);
        opacity: 0.8;
    }
    80%, 100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* Counter Animation */
.countup {
    display: inline-block;
    position: relative;
}

/* Services Icon Animation */
.single-service .icon {
    transition: var(--transition-medium);
}

.single-service:hover .icon {
    transform: rotateY(180deg);
}

/* Testimonial Card Animation */
.single-testimonial {
    transition: var(--transition-medium);
    transform-style: preserve-3d;
    perspective: 1000px;
}

.single-testimonial:hover {
    transform: rotateY(5deg);
}

/* Call to Action Button Animation */
.call-action .btn {
    position: relative;
    overflow: hidden;
}

.call-action .btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: none;
    animation: shine 2s infinite;
}

@keyframes shine {
    0% {
        left: -100%;
    }
    20% {
        left: 100%;
    }
    100% {
        left: 100%;
    }
}

/* Responsive Improvements */
@media only screen and (max-width: 767px) {
    /* Improve spacing on mobile */
    .section {
        padding: 60px 0;
    }

    .section-title {
        margin-bottom: 30px;
    }

    /* Make cards full width on mobile */
    .single-service, .single-course, .single-testimonial, .single-team, .single-news {
        margin-bottom: 30px;
    }

    /* Improve courses on mobile */
    .courses .single-course {
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .courses .single-course .content {
        padding: 20px;
    }

    .courses .single-course .course-image img {
        height: 200px;
    }

    .courses .single-course .content h3 a {
        font-size: 18px;
    }

    .courses .single-course .content .button .btn {
        width: 100%;
        text-align: center;
    }

    /* Improve button visibility */
    .btn {
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 600;
    }

    /* Center align content on mobile */
    .text-center-mobile {
        text-align: center !important;
    }

    /* Adjust hero section for mobile */
    .hero-area .hero-text h1 {
        font-size: 32px !important;
        line-height: 1.3 !important;
    }

    .hero-area .hero-text p {
        font-size: 16px;
        margin-bottom: 25px;
    }

    /* Improve mobile navigation */
    .navbar-collapse {
        background-color: #fff;
        padding: 20px;
        border-radius: 5px;
        box-shadow: var(--shadow-medium);
    }
}

/* Animation for scroll to top button */
.scroll-top {
    transition: var(--transition-fast);
}

.scroll-top:hover {
    background-color: var(--gold-dark);
    transform: translateY(-3px);
}
