# Mobile Sidebar Menu for Zajel Arabic Theme

## 🎯 نظرة عامة

تم تطوير قائمة جانبية للموبايل تنزلق من الشمال مع الحفاظ على التصميم الأصلي للهيدر في الشاشات الكبيرة، مع دعم dropdown للكورسات والقوائم الفرعية.

## ✨ الميزات الجديدة

### 🖥️ الشاشات الكبيرة (Desktop)
- **تصميم أصلي محفوظ**: الهيدر يبقى زي ما هو بالضبط
- **قائمة أفقية**: Navigation عادي في الأعلى
- **زر التجربة المجانية**: في مكانه الطبيعي
- **dropdown menus**: للقوائم الفرعية عند hover

### 📱 الموبايل والتابلت
- **قائمة جانبية**: تنزلق من الشمال زي الصورة اللي بعتها
- **All Departments**: زر ذهبي في الأعلى
- **قائمة كاملة**: كل الصفحات والأقسام
- **dropdown للكورسات**: ينفتح لتحت عند الضغط عليه
- **زر التجربة المجانية**: في آخر القائمة
- **روابط السوشيال ميديا**: Facebook, Instagram, Twitter, TikTok, Snapchat, YouTube

## 📁 الملفات المضافة

### CSS Files
```
assets/css/mobile-sidebar-menu.css     - تصميم القائمة الجانبية
assets/css/header-responsive-fix.css   - إصلاحات responsive للهيدر
```

### JavaScript Files
```
assets/js/mobile-sidebar-menu.js       - وظائف القائمة الجانبية والdropdown
```

### Modified Files
```
header.php                            - الهيدر محدث بالقائمة الجانبية
```

## 🎨 الألوان المستخدمة

```css
:root {
    --primary-blue: #1a5f8d;        /* الأزرق الأساسي */
    --primary-blue-light: #2980b9;  /* الأزرق الفاتح */
    --primary-blue-dark: #03355c;   /* الأزرق الداكن */
    --accent-gold: #f39c12;         /* الذهبي للأزرار */
    --white: #ffffff;               /* الأبيض */
    --dark-gray: #343a40;           /* الرمادي الداكن */
}
```

## 📱 ميزات القائمة الجانبية

### 🎯 العناصر الأساسية
- ✅ **Header**: لوجو الموقع وزر الإغلاق
- ✅ **All Departments**: زر ذهبي مميز
- ✅ **Home**: الصفحة الرئيسية
- ✅ **About us**: صفحة من نحن
- ✅ **Imam Courses**: الكورسات مع dropdown
  - Quran Memorization
  - Arabic Language  
  - Islamic Studies
  - Tajweed
- ✅ **Plans / Fees**: الخطط والأسعار
- ✅ **Menu**: قائمة إضافية مع dropdown
  - Teachers
  - Testimonials
  - Blog
  - FAQ
- ✅ **Contact**: صفحة التواصل
- ✅ **Your Account**: حساب المستخدم مع dropdown
  - Login
  - Register
  - Dashboard

### 🎭 التأثيرات والانيميشن
- **انزلاق سلس**: من الشمال مع تأثير overlay
- **انيميشن stagger**: للعناصر تظهر واحد ورا التاني
- **dropdown smooth**: انفتاح وإغلاق ناعم للقوائم الفرعية
- **hover effects**: تأثيرات عند المرور بالماوس
- **icons animation**: أيقونات متحركة

## 🔧 كيفية الاستخدام

### فتح القائمة
- **الضغط على الهامبرجر**: الثلاث خطوط في الموبايل
- **الضغط على Overlay**: لإغلاق القائمة
- **زر X**: في أعلى القائمة للإغلاق
- **مفتاح Escape**: لإغلاق القائمة

### التنقل في القائمة
- **الضغط على العناصر**: للانتقال للصفحات
- **الضغط على السهم**: لفتح dropdown
- **التمرير**: للأعلى والأسفل في القائمة
- **الكيبورد**: Tab للتنقل، Enter للاختيار

## 📐 Responsive Design

### الشاشات المختلفة
```css
/* Desktop (992px+) */
- الهيدر الأصلي يظهر
- القائمة الجانبية مخفية

/* Tablet (768px - 991px) */
- قائمة جانبية 350px عرض
- تصميم مريح للتابلت

/* Mobile (576px - 767px) */
- قائمة جانبية 320px عرض
- تصميم محسن للموبايل

/* Small Mobile (أقل من 576px) */
- قائمة جانبية 300px عرض
- عناصر أصغر حجماً
```

## 🎯 الوظائف المتقدمة

### JavaScript Functions
```javascript
// فتح القائمة
mobileSidebarMenu.openMenu();

// إغلاق القائمة
mobileSidebarMenu.closeMenu();

// تبديل القائمة
mobileSidebarMenu.toggleMenu();

// فتح dropdown
mobileSidebarMenu.toggleDropdown(element);

// التحقق من حالة القائمة
mobileSidebarMenu.isOpen();
```

### Event Listeners
- **Click Events**: للأزرار والروابط
- **Keyboard Events**: للتنقل بالكيبورد
- **Resize Events**: لإغلاق القائمة عند تغيير الحجم
- **Touch Events**: للأجهزة اللمسية

## 🔍 اختبار الجودة

### اختبارات الوظائف
- ✅ فتح وإغلاق القائمة
- ✅ dropdown للكورسات
- ✅ الروابط تعمل صح
- ✅ responsive على كل الأجهزة
- ✅ keyboard navigation
- ✅ touch gestures

### اختبارات الأداء
- ✅ سرعة التحميل
- ✅ smooth animations
- ✅ memory usage
- ✅ battery optimization

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة

**القائمة لا تفتح:**
```javascript
// تحقق من وجود العناصر
console.log(document.getElementById('mobileMenuToggle'));
console.log(document.getElementById('mobileSidebarMenu'));
```

**الdropdown لا يعمل:**
```javascript
// تحقق من الـ event listeners
document.querySelectorAll('.dropdown-toggle').forEach(el => {
    console.log('Dropdown element:', el);
});
```

**مشاكل في الموبايل:**
```css
/* إصلاح مشاكل اللمس */
.mobile-nav-link {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}
```

## 📈 التحديثات المستقبلية

### مخطط التطوير
- [ ] إضافة search في القائمة
- [ ] دعم RTL محسن
- [ ] المزيد من الانيميشن
- [ ] تكامل مع WordPress Customizer
- [ ] دعم PWA

## 🎨 التخصيص

### تغيير الألوان
```css
:root {
    --primary-blue: #your-color;
    --accent-gold: #your-accent;
}
```

### تغيير عرض القائمة
```css
.mobile-sidebar-menu {
    width: 400px; /* العرض المطلوب */
    left: -400px;
}
```

### إضافة عناصر جديدة
```html
<li class="mobile-nav-item">
    <a href="#" class="mobile-nav-link">
        <i class="fas fa-star"></i>
        عنصر جديد
    </a>
</li>
```

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: <EMAIL>
- 💬 التواصل المباشر عبر الموقع
- 📱 واتساب: ******-123-4567

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

---

**تم التطوير بواسطة:** Augment Agent  
**التاريخ:** 2025-07-14  
**الإصدار:** 1.0.0

## 🎉 النتيجة النهائية

الآن الموقع عنده:
- **🖥️ Desktop**: هيدر أصلي بالتصميم الكلاسيكي
- **📱 Mobile**: قائمة جانبية عصرية تنزلق من الشمال
- **🎯 Dropdown**: للكورسات والقوائم الفرعية
- **🎨 متناسق**: مع ألوان الموقع الأصلية
- **⚡ سريع**: أداء محسن ومتجاوب
