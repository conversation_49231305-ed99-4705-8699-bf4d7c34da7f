/* Contact Page Styles */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
    --dark-bg: #333333;
    --darker-bg: #222222;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --transition-slow: all 0.8s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 15px 60px rgba(0, 0, 0, 0.2);
}

/* Hero Section */
.contact-hero {
    background: linear-gradient(135deg, rgba(26, 95, 141, 0.1), rgba(3, 53, 92, 0.2)), url('../images/contact-bg.jpg');
    background-size: cover;
    background-position: center;
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.contact-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(3, 53, 92, 0.7), rgba(26, 95, 141, 0.7));
    opacity: 0.85;
    z-index: 1;
}

.contact-hero .container {
    position: relative;
    z-index: 2;
}

.contact-hero h1 {
    color: #fff;
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: fadeInDown 1s ease;
}

.contact-hero p {
    color: #fff;
    font-size: 18px;
    max-width: 600px;
    margin-bottom: 30px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 1s ease;
}

/* Contact Info Section */
.contact-info-section {
    padding: 80px 0;
    background-color: #fff;
    position: relative;
}

.contact-info-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/pattern.png');
    background-size: 300px;
    opacity: 0.05;
    z-index: 0;
}

.contact-info-container {
    position: relative;
    z-index: 1;
}

.contact-info-card {
    background: #fff;
    border-radius: 15px;
    padding: 40px 30px;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
    position: relative;
    overflow: hidden;
    text-align: center;
    margin-bottom: 30px;
    animation: fadeIn 1s ease;
}

.contact-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    transition: var(--transition-medium);
}

.contact-info-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.contact-info-icon {
    width: 60px;
    height: 60px;
    min-width: 60px;
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    position: relative;
    transition: var(--transition-medium);
    box-shadow: 0 5px 15px rgba(64, 84, 178, 0.3);
}

.contact-info-icon::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    border: 2px dashed rgba(110, 193, 228, 0.5);
    animation: spin 20s linear infinite;
    opacity: 0;
    transition: var(--transition-medium);
}

.contact-info-card:hover .contact-info-icon::after {
    opacity: 1;
}

.contact-info-icon i {
    font-size: 36px;
    color: #fff;
    transition: var(--transition-medium);
}

.contact-info-card:hover .contact-info-icon {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
}

.contact-info-card:hover .contact-info-icon i {
    color: #fff;
    transform: rotateY(360deg);
}

.contact-info-title {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
    transition: var(--transition-fast);
}

.contact-info-card:hover .contact-info-title {
    color: var(--gold-dark);
}

.contact-info-text {
    color: #666;
    margin-bottom: 0;
    line-height: 1.7;
}

.contact-info-text a {
    color: #666;
    transition: var(--transition-fast);
}

.contact-info-text a:hover {
    color: var(--gold-dark);
}

/* Contact Form Section */
.contact-form-section {
    padding: 80px 0;
    background-color: #f9f9f9;
    position: relative;
}

.contact-form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/pattern.png');
    background-size: 300px;
    opacity: 0.05;
    z-index: 0;
}

.contact-form-container {
    position: relative;
    z-index: 1;
}

.contact-form-box {
    background: #fff;
    border-radius: 15px;
    box-shadow: var(--shadow-medium);
    padding: 40px;
    transition: var(--transition-medium);
    animation: fadeIn 1s ease;
    border: 1px solid rgba(110, 193, 228, 0.2);
    position: relative;
    overflow: hidden;
}

.contact-form-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, var(--gold-light), var(--gold-dark));
}

.contact-form-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-large);
}

.contact-form-box h3 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 30px;
    color: #333;
    position: relative;
    padding-bottom: 15px;
}

.contact-form-box h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--gold-light), var(--gold-dark));
    border-radius: 2px;
}

.contact-form-box p {
    margin-bottom: 25px;
    color: #666;
}

/* Form Styling */
.contact-form .form-group {
    margin-bottom: 25px;
    position: relative;
}

.contact-form .form-control {
    height: 55px;
    padding: 10px 20px;
    font-size: 16px;
    border: 2px solid #eee;
    border-radius: 8px;
    transition: var(--transition-fast);
    background-color: #f9f9f9;
}

.contact-form .form-control:focus {
    border-color: var(--gold-medium);
    box-shadow: 0 0 0 3px rgba(3, 53, 92, 0.1);
    background-color: #fff;
}

.contact-form textarea.form-control {
    height: 150px;
    resize: none;
}

.contact-form .form-control::placeholder {
    color: #999;
    font-size: 14px;
}

.contact-form .form-label {
    font-weight: 600;
    color: #555;
    margin-bottom: 8px;
    display: block;
}

.contact-form .btn-submit {
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    border: none;
    height: 55px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 50px;
    padding: 0 35px;
    cursor: pointer;
    transition: var(--transition-fast);
    width: 100%;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(110, 193, 228, 0.4);
}

.contact-form .btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(110, 193, 228, 0.6);
}

/* Map Section */
.contact-map-section {
    padding: 80px 0;
    background-color: #fff;
}

.map-container {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    height: 450px;
    position: relative;
}

.map-container iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.map-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    pointer-events: none;
    transition: var(--transition-fast);
}

.map-container:hover .map-overlay {
    background: rgba(255, 255, 255, 0);
}

/* Social Media Section */
.contact-social-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    margin-bottom: 50px;
    box-shadow: 0 10px 30px rgba(110, 193, 228, 0.4);
}

.contact-social-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/pattern-light.png');
    background-size: 300px;
    opacity: 0.1;
    z-index: 0;
}

.social-container {
    position: relative;
    z-index: 1;
    text-align: center;
}

.social-title {
    font-size: 36px;
    font-weight: 700;
    color: #fff;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.social-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    max-width: 700px;
    margin: 0 auto 30px;
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.social-icon {
    width: 60px;
    height: 60px;
    min-width: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    color: #fff;
    font-size: 24px;
    text-decoration: none;
    box-shadow: 0 5px 15px rgba(3, 53, 92, 0.3);
}

.social-icon:hover {
    background: #fff;
    color: var(--gold-dark);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .contact-hero h1 {
        font-size: 36px;
    }

    .contact-hero p {
        font-size: 16px;
    }

    .contact-info-card {
        margin-bottom: 30px;
    }

    .contact-form-box {
        padding: 30px;
        margin-bottom: 30px;
    }

    .contact-form-box h3 {
        font-size: 24px;
    }

    .map-container {
        height: 350px;
    }

    .social-title {
        font-size: 30px;
    }

    .social-description {
        font-size: 16px;
    }

    .social-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}


    .contact-hero {
        padding: 80px 0;
    }

    .contact-hero h1 {
        font-size: 30px;
    }

    .contact-info-section,
    .contact-form-section,
    .contact-map-section,
    .contact-social-section {
        padding: 60px 0;
    }

    .contact-info-card {
        padding: 30px 20px;
    }

    .contact-info-icon {
        width: 70px;
        height: 70px;
    }

    .contact-info-icon i {
        font-size: 30px;
    }

    .contact-info-title {
        font-size: 20px;
    }

    .contact-form-box h3 {
        font-size: 22px;
    }

    .map-container {
        height: 300px;
    }

    .social-title {
        font-size: 26px;
    }

    .social-icons {
        gap: 15px;
    }

    .social-icon {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }


@media only screen and (max-width: 575px) {
    .contact-hero h1 {
        font-size: 26px;
    }

    .contact-hero p {
        font-size: 14px;
    }

    .contact-info-icon {
        width: 60px;
        height: 60px;
    }

    .contact-info-icon i {
        font-size: 24px;
    }

    .contact-info-title {
        font-size: 18px;
    }

    .contact-form-box {
        padding: 25px;
    }

    .contact-form-box h3 {
        font-size: 20px;
    }

    .contact-form .form-control {
        height: 50px;
        font-size: 14px;
    }

    .contact-form .btn-submit {
        height: 50px;
        font-size: 14px;
    }

    .map-container {
        height: 250px;
    }

    .social-title {
        font-size: 22px;
    }

    .social-description {
        font-size: 14px;
    }

    .social-icons {
        gap: 10px;
    }

    .social-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

/* Alert Messages */
.alert {
    padding: 15px 20px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 8px;
    position: relative;
    animation: slideInDown 0.5s ease;
    font-size: 14px;
    line-height: 1.5;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-success::before {
    content: "✓";
    font-weight: bold;
    margin-right: 10px;
    color: #28a745;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-danger::before {
    content: "⚠";
    font-weight: bold;
    margin-right: 10px;
    color: #dc3545;
}

@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
