/* Single Post Styles for Zajel Arabic Theme */
:root {
    --blue-light: #1a5f8d;
    --blue-medium: #0c4a77;
    --blue-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Breadcrumbs */
.breadcrumbs {
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    padding: 110px 0;
    position: relative;
    overflow: hidden;
}

.breadcrumbs::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 1;
}

.breadcrumbs .container {
    position: relative;
    z-index: 2;
}

.breadcrumbs-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.breadcrumbs-content .page-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-bottom: 20px;
}

.breadcrumbs-content .page-icon i {
    color: #fff;
    font-size: 32px;
}

.breadcrumbs-content .page-title {
    color: #fff;
    font-size: 36px;
    font-weight: 800;
    margin-bottom: 15px;
}

.breadcrumb-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 10px;
}

.breadcrumb-nav li {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

.breadcrumb-nav li a {
    color: #fff;
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb-nav li a:hover {
    color: rgba(255, 255, 255, 0.8);
}

.breadcrumb-nav li i {
    margin: 0 5px;
    font-size: 12px;
}

/* Blog Single Area */
.blog-single-area {
    background-color: #f9f9f9;
    padding: 100px 0;
    position: relative;
}

.blog-single-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a5f8d' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 0;
}

.blog-single-area .container {
    position: relative;
    z-index: 1;
}

/* Single Post Content */
.single-post-content {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-small);
    margin-bottom: 30px;
}

.single-post-content .post-thumbnail {
    position: relative;
    overflow: hidden;
}

.single-post-content .post-thumbnail img {
    width: 100%;
    height: auto;
    max-height: 500px;
    object-fit: cover;
    transition: var(--transition-medium);
}

.single-post-content .post-meta {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 20px;
    padding: 20px 25px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fff;
}

.single-post-content .post-meta > div {
    display: flex;
    align-items: center;
    position: relative;
}

.single-post-content .post-meta > div:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 15px;
    background-color: #ddd;
}

.single-post-content .post-meta i {
    color: var(--blue-medium);
    margin-right: 5px;
    font-size: 16px;
}

.single-post-content .post-meta a {
    color: #666;
    text-decoration: none;
    transition: var(--transition-fast);
    font-size: 14px;
}

.single-post-content .post-meta a:hover {
    color: var(--blue-medium);
}

.single-post-content .post-title {
    padding: 25px 25px 0;
    margin-bottom: 20px;
}

.single-post-content .post-title h1 {
    font-size: 32px;
    font-weight: 800;
    line-height: 1.3;
    color: #333;
    margin: 0;
}

.single-post-content .post-content {
    padding: 0 25px 25px;
    color: #666;
    font-size: 16px;
    line-height: 1.7;
}

.single-post-content .post-content p {
    margin-bottom: 20px;
}

.single-post-content .post-content h1,
.single-post-content .post-content h2,
.single-post-content .post-content h3,
.single-post-content .post-content h4,
.single-post-content .post-content h5,
.single-post-content .post-content h6 {
    margin-top: 30px;
    margin-bottom: 15px;
    color: #333;
}

.single-post-content .post-content blockquote {
    padding: 20px;
    background-color: rgba(26, 95, 141, 0.05);
    border-left: 4px solid var(--blue-medium);
    font-style: italic;
    color: #555;
    margin: 25px 0;
}

.single-post-content .post-content img {
    max-width: 100%;
    height: auto;
    border-radius: 5px;
    margin: 20px 0;
}

.single-post-content .post-content ul,
.single-post-content .post-content ol {
    margin-bottom: 20px;
    padding-left: 20px;
}

.single-post-content .post-content li {
    margin-bottom: 10px;
}

.single-post-content .post-content a {
    color: var(--blue-medium);
    text-decoration: none;
    transition: var(--transition-fast);
}

.single-post-content .post-content a:hover {
    color: var(--blue-dark);
    text-decoration: underline;
}

.single-post-content .post-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 25px 0;
}

.single-post-content .post-content table th,
.single-post-content .post-content table td {
    padding: 12px 15px;
    border: 1px solid #ddd;
}

.single-post-content .post-content table th {
    background-color: #f5f5f5;
    font-weight: 600;
    text-align: left;
}

.single-post-content .post-content table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.single-post-content .post-tags {
    padding: 20px 25px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.single-post-content .post-tags h4 {
    margin: 0;
    margin-right: 15px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.single-post-content .post-tags .tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.single-post-content .post-tags .tags a {
    display: inline-block;
    padding: 5px 15px;
    background-color: #f0f0f0;
    color: #666;
    border-radius: 20px;
    font-size: 13px;
    text-decoration: none;
    transition: var(--transition-fast);
}

.single-post-content .post-tags .tags a:hover {
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    color: #fff;
    transform: translateY(-2px);
}

/* Post Navigation */
.post-navigation {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-small);
    display: flex;
    justify-content: space-between;
}

.post-navigation .nav-links {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.post-navigation .nav-previous,
.post-navigation .nav-next {
    max-width: 48%;
}

.post-navigation .nav-previous a,
.post-navigation .nav-next a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #333;
    transition: var(--transition-fast);
}

.post-navigation .nav-previous a:hover,
.post-navigation .nav-next a:hover {
    color: var(--blue-medium);
}

.post-navigation .nav-previous i,
.post-navigation .nav-next i {
    font-size: 20px;
    color: var(--blue-medium);
    transition: var(--transition-fast);
}

.post-navigation .nav-previous i {
    margin-right: 10px;
}

.post-navigation .nav-next i {
    margin-left: 10px;
}

.post-navigation .nav-previous a:hover i {
    transform: translateX(-5px);
}

.post-navigation .nav-next a:hover i {
    transform: translateX(5px);
}

.post-navigation .post-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
}

/* Author Box */
.author-box {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-small);
    display: flex;
    align-items: center;
    gap: 20px;
}

.author-box .author-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.author-box .author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-box .author-info {
    flex-grow: 1;
}

.author-box .author-name {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #333;
}

.author-box .author-bio {
    color: #666;
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 15px;
}

.author-box .author-social {
    display: flex;
    gap: 10px;
}

.author-box .author-social a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    background-color: #f0f0f0;
    color: #666;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition-fast);
}

.author-box .author-social a:hover {
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    color: #fff;
    transform: translateY(-2px);
}

/* Related Posts */
.related-posts {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-small);
}

.related-posts-title {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(26, 95, 141, 0.1);
    position: relative;
    color: #333;
}

.related-posts-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--blue-dark), var(--blue-light));
}

.related-posts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.single-related-post {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.single-related-post .image {
    width: 80px;
    height: 80px;
    border-radius: 5px;
    overflow: hidden;
    flex-shrink: 0;
}

.single-related-post .image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-fast);
}

.single-related-post:hover .image img {
    transform: scale(1.05);
}

.single-related-post .content {
    flex-grow: 1;
}

.single-related-post .title {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 5px;
}

.single-related-post .title a {
    color: #333;
    text-decoration: none;
    transition: var(--transition-fast);
}

.single-related-post .title a:hover {
    color: var(--blue-medium);
}

.single-related-post .date {
    font-size: 13px;
    color: #888;
    display: flex;
    align-items: center;
}

.single-related-post .date i {
    color: var(--blue-light);
    margin-right: 5px;
}

/* Comments Area */
.comments-area {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-small);
}

.comments-title {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(26, 95, 141, 0.1);
    position: relative;
    color: #333;
}

.comments-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--blue-dark), var(--blue-light));
}

.comment-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.comment {
    margin-bottom: 30px;
}

.comment:last-child {
    margin-bottom: 0;
}

.comment-body {
    position: relative;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 10px;
}

.comment-meta {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.comment-author {
    display: flex;
    align-items: center;
    gap: 10px;
}

.comment-author .avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.comment-author .fn {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    text-decoration: none;
    font-style: normal;
}

.comment-metadata {
    font-size: 13px;
    color: #888;
}

.comment-metadata a {
    color: #888;
    text-decoration: none;
    transition: var(--transition-fast);
}

.comment-metadata a:hover {
    color: var(--blue-medium);
}

.comment-content {
    color: #666;
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 15px;
}

.comment-content p:last-child {
    margin-bottom: 0;
}

.reply {
    text-align: right;
}

.reply a {
    display: inline-block;
    padding: 5px 15px;
    background-color: #f0f0f0;
    color: #666;
    border-radius: 20px;
    font-size: 13px;
    text-decoration: none;
    transition: var(--transition-fast);
}

.reply a:hover {
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    color: #fff;
}

.children {
    list-style: none;
    padding-left: 50px;
    margin-top: 30px;
}

/* Comment Form */
.comment-respond {
    margin-top: 50px;
}

.comment-reply-title {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(26, 95, 141, 0.1);
    position: relative;
    color: #333;
}

.comment-reply-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--blue-dark), var(--blue-light));
}

.comment-form {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.comment-form-comment,
.comment-form-cookies-consent,
.form-submit {
    grid-column: span 2;
}

.comment-form label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #333;
}

.comment-form input[type="text"],
.comment-form input[type="email"],
.comment-form input[type="url"],
.comment-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #eee;
    border-radius: 5px;
    font-size: 15px;
    transition: var(--transition-fast);
}

.comment-form input[type="text"]:focus,
.comment-form input[type="email"]:focus,
.comment-form input[type="url"]:focus,
.comment-form textarea:focus {
    border-color: var(--blue-light);
    outline: none;
    box-shadow: 0 0 0 3px rgba(26, 95, 141, 0.1);
}

.comment-form textarea {
    height: 150px;
    resize: vertical;
}

.comment-form-cookies-consent {
    display: flex;
    align-items: center;
    gap: 10px;
}

.comment-form-cookies-consent label {
    margin-bottom: 0;
    font-weight: normal;
    color: #666;
    font-size: 14px;
}

.form-submit {
    text-align: right;
}

.form-submit .submit {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    color: #fff;
    border: none;
    border-radius: 30px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: 0 4px 10px rgba(26, 95, 141, 0.2);
}

.form-submit .submit:hover {
    background: linear-gradient(135deg, var(--blue-dark), var(--blue-light));
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(26, 95, 141, 0.3);
}

/* Blog Sidebar */
.blog-sidebar {
    position: sticky;
    top: 30px;
}

/* Course Information Header */
.course-info-header {
    background: var(--blue-dark);
    padding: 20px;
    color: white;
    position: relative;
    overflow: hidden;
}

.course-info-header h3.sidebar-widget-title {
    color: white !important;
    margin: 0;
    padding: 0;
    font-size: 20px;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.course-info-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
    z-index: 0;
}

.sidebar-widget:hover .course-info-header::before {
    left: 100%;
    transition: 0.7s;
}

.widget {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-small);
}

.widget-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(26, 95, 141, 0.1);
    position: relative;
    color: #333;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--blue-dark), var(--blue-light));
}

/* Schema Markup */
.schema-markup {
    display: none;
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .breadcrumbs {
        padding: 60px 0;
    }

    .breadcrumbs-content .page-title {
        font-size: 32px;
    }

    .blog-single-area {
        padding: 80px 0;
    }

    .single-post-content .post-title h1 {
        font-size: 28px;
    }

    .blog-sidebar {
        margin-top: 50px;
        position: static;
    }

    .related-posts-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

@media only screen and (max-width: 767px) {
    .breadcrumbs {
        padding: 50px 0;
    }

    .breadcrumbs-content .page-icon {
        width: 60px;
        height: 60px;
    }

    .breadcrumbs-content .page-icon i {
        font-size: 24px;
    }

    .breadcrumbs-content .page-title {
        font-size: 28px;
    }

    .breadcrumb-nav li {
        font-size: 14px;
    }

    .blog-single-area {
        padding: 60px 0;
    }

    .single-post-content .post-meta {
        padding: 15px 20px;
        gap: 15px;
    }

    .single-post-content .post-meta > div:not(:last-child)::after {
        display: none;
    }

    .single-post-content .post-title {
        padding: 20px 20px 0;
    }

    .single-post-content .post-title h1 {
        font-size: 24px;
    }

    .single-post-content .post-content {
        padding: 0 20px 20px;
        font-size: 15px;
    }

    .single-post-content .post-tags {
        padding: 15px 20px;
    }

    .post-navigation {
        padding: 20px;
    }

    .post-navigation .post-title {
        font-size: 14px;
    }

    .author-box {
        padding: 20px;
        flex-direction: column;
        text-align: center;
    }

    .author-box .author-social {
        justify-content: center;
    }

    .related-posts,
    .comments-area {
        padding: 20px;
    }

    .related-posts-title,
    .comments-title,
    .comment-reply-title {
        font-size: 20px;
    }

    .children {
        padding-left: 30px;
    }

    .comment-form {
        grid-template-columns: 1fr;
    }

    .comment-form-comment,
    .comment-form-cookies-consent,
    .form-submit {
        grid-column: span 1;
    }
}

@media only screen and (max-width: 575px) {
    .breadcrumbs {
        padding: 40px 0;
    }

    .breadcrumbs-content .page-title {
        font-size: 24px;
    }

    .blog-single-area {
        padding: 50px 0;
    }

    .single-post-content .post-title h1 {
        font-size: 22px;
    }

    .post-navigation .nav-previous,
    .post-navigation .nav-next {
        max-width: 100%;
    }

    .post-navigation .nav-links {
        flex-direction: column;
        gap: 15px;
    }
}
