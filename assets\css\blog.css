/* Blog CSS for Zajel Arabic Theme */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Blog Area Styles */
.blog-area {
    background-color: #f9f9f9;
}

/* Blog Post Styles */
.blog-post {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-small);
    margin-bottom: 30px;
    transition: var(--transition-medium);
}

.blog-post:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.blog-post .post-thumbnail {
    position: relative;
    overflow: hidden;
}

.blog-post .post-thumbnail img {
    width: 100%;
    height: auto;
    transition: var(--transition-medium);
}

.blog-post:hover .post-thumbnail img {
    transform: scale(1.05);
}

.blog-post .post-content {
    padding: 25px;
}

.blog-post .post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 14px;
}

.blog-post .post-meta > div {
    display: flex;
    align-items: center;
}

.blog-post .post-meta i {
    color: var(--gold-dark);
    margin-right: 5px;
}

.blog-post .post-title {
    font-size: 24px;
    margin-bottom: 15px;
    font-weight: 700;
}

.blog-post .post-title a {
    color: #333;
    text-decoration: none;
    transition: var(--transition-fast);
}

.blog-post .post-title a:hover {
    color: var(--gold-dark);
}

.blog-post .post-excerpt {
    margin-bottom: 20px;
    color: #666;
    line-height: 1.7;
}

.blog-post .post-read-more .btn {
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    padding: 10px 25px;
    border-radius: 5px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: var(--transition-fast);
}

.blog-post .post-read-more .btn:hover {
    background: linear-gradient(135deg, var(--gold-light), var(--gold-dark));
    transform: translateY(-3px);
    box-shadow: var(--shadow-small);
}

.blog-post .post-read-more .btn i {
    margin-left: 5px;
    transition: var(--transition-fast);
}

.blog-post .post-read-more .btn:hover i {
    transform: translateX(5px);
}

/* Pagination Styles */
.pagination-area {
    margin-top: 50px;
    margin-bottom: 30px;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.pagination .page-numbers {
    display: flex;
    gap: 5px;
}

.pagination .page-numbers .page-numbers {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #fff;
    border-radius: 5px;
    color: #333;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-small);
}

.pagination .page-numbers .page-numbers:hover {
    background-color: rgba(184, 134, 11, 0.1);
    color: var(--gold-dark);
}

.pagination .page-numbers .page-numbers.current {
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    color: #fff;
}

.pagination .prev-page a,
.pagination .next-page a {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    height: 40px;
    background-color: #fff;
    border-radius: 5px;
    color: #333;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-small);
}

.pagination .prev-page a:hover,
.pagination .next-page a:hover {
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    color: #fff;
}

.pagination .prev-page a i,
.pagination .next-page a i {
    transition: var(--transition-fast);
}

.pagination .prev-page a:hover i {
    transform: translateX(-3px);
}

.pagination .next-page a:hover i {
    transform: translateX(3px);
}

/* No Results Styles */
.no-results {
    text-align: center;
    padding: 50px 0;
}

.no-results .icon {
    font-size: 60px;
    color: var(--gold-medium);
    margin-bottom: 20px;
}

.no-results .page-title {
    font-size: 28px;
    margin-bottom: 15px;
}

.no-results p {
    margin-bottom: 20px;
    color: #666;
}

.no-results .search-form {
    max-width: 500px;
    margin: 0 auto;
}

/* Sidebar Styles */
.blog-sidebar {
    position: sticky;
    top: 30px;
}

.widget {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-small);
}

.widget-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(184, 134, 11, 0.1);
    position: relative;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--gold-dark), var(--gold-light));
}

/* Search Widget */
.search-widget .search-form {
    position: relative;
}

.search-widget .search-form input[type="search"] {
    width: 100%;
    padding: 12px 50px 12px 15px;
    border: 1px solid #eee;
    border-radius: 5px;
    outline: none;
    transition: var(--transition-fast);
}

.search-widget .search-form input[type="search"]:focus {
    border-color: var(--gold-medium);
    box-shadow: 0 0 0 3px rgba(218, 165, 32, 0.1);
}

.search-widget .search-form button {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 50px;
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    border: none;
    border-radius: 0 5px 5px 0;
    color: #fff;
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-widget .search-form button:hover {
    background: linear-gradient(135deg, var(--gold-light), var(--gold-dark));
}

/* Categories Widget */
.categories-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.categories-list li {
    margin-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
}

.categories-list li:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
}

.categories-list li a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #666;
    text-decoration: none;
    transition: var(--transition-fast);
}

.categories-list li a:hover {
    color: var(--gold-dark);
    padding-left: 5px;
}

.categories-list li a i {
    color: var(--gold-dark);
    margin-right: 8px;
}

.categories-list li a span {
    background-color: rgba(184, 134, 11, 0.1);
    color: var(--gold-dark);
    border-radius: 20px;
    padding: 2px 8px;
    font-size: 12px;
    font-weight: 600;
}

/* Recent Posts Widget */
.recent-posts .single-post {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.recent-posts .single-post:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.recent-posts .post-img {
    width: 80px;
    height: 80px;
    margin-right: 15px;
    border-radius: 5px;
    overflow: hidden;
}

.recent-posts .post-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-fast);
}

.recent-posts .single-post:hover .post-img img {
    transform: scale(1.1);
}

.recent-posts .post-info {
    flex: 1;
}

.recent-posts .post-info h5 {
    font-size: 15px;
    margin-bottom: 5px;
    font-weight: 600;
    line-height: 1.4;
}

.recent-posts .post-info h5 a {
    color: #333;
    text-decoration: none;
    transition: var(--transition-fast);
}

.recent-posts .post-info h5 a:hover {
    color: var(--gold-dark);
}

.recent-posts .post-info span {
    font-size: 12px;
    color: #888;
    display: flex;
    align-items: center;
}

.recent-posts .post-info span i {
    color: var(--gold-dark);
    margin-right: 5px;
}

/* Tags Widget */
.tags-widget .tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tags-widget .tags a {
    display: inline-block;
    padding: 5px 12px;
    background-color: rgba(184, 134, 11, 0.1);
    color: var(--gold-dark);
    border-radius: 5px;
    font-size: 13px;
    text-decoration: none;
    transition: var(--transition-fast);
}

.tags-widget .tags a:hover {
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    transform: translateY(-3px);
}

/* Single Post Styles */
.single-post-content {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-small);
    margin-bottom: 30px;
}

.single-post-content .post-thumbnail {
    position: relative;
    overflow: hidden;
}

.single-post-content .post-thumbnail img {
    width: 100%;
    height: auto;
}

.single-post-content .post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 20px 25px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
}

.single-post-content .post-meta > div {
    display: flex;
    align-items: center;
}

.single-post-content .post-meta i {
    color: var(--gold-dark);
    margin-right: 5px;
}

.single-post-content .post-content {
    padding: 25px;
}

.single-post-content .post-content p,
.single-post-content .post-content ul,
.single-post-content .post-content ol,
.single-post-content .post-content blockquote {
    margin-bottom: 20px;
    color: #666;
    line-height: 1.8;
}

.single-post-content .post-content h1,
.single-post-content .post-content h2,
.single-post-content .post-content h3,
.single-post-content .post-content h4,
.single-post-content .post-content h5,
.single-post-content .post-content h6 {
    margin-top: 30px;
    margin-bottom: 15px;
    color: #333;
}

.single-post-content .post-content blockquote {
    padding: 20px;
    background-color: rgba(184, 134, 11, 0.05);
    border-left: 4px solid var(--gold-medium);
    font-style: italic;
    color: #555;
}

.single-post-content .post-content img {
    max-width: 100%;
    height: auto;
    border-radius: 5px;
    margin: 20px 0;
}

.single-post-content .post-tags {
    padding: 20px 25px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.single-post-content .post-tags h4 {
    margin-right: 15px;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.single-post-content .post-tags .tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.single-post-content .post-tags .tags a {
    display: inline-block;
    padding: 5px 12px;
    background-color: rgba(184, 134, 11, 0.1);
    color: var(--gold-dark);
    border-radius: 5px;
    font-size: 13px;
    text-decoration: none;
    transition: var(--transition-fast);
}

.single-post-content .post-tags .tags a:hover {
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    color: #fff;
}

.single-post-content .post-navigation {
    padding: 25px;
    border-top: 1px solid #f0f0f0;
}

.single-post-content .post-navigation .prev-post,
.single-post-content .post-navigation .next-post {
    background-color: #f9f9f9;
    border-radius: 5px;
    overflow: hidden;
    transition: var(--transition-fast);
}

.single-post-content .post-navigation .prev-post:hover,
.single-post-content .post-navigation .next-post:hover {
    background-color: rgba(184, 134, 11, 0.1);
}

.single-post-content .post-navigation a {
    display: flex;
    align-items: center;
    padding: 15px;
    color: #333;
    text-decoration: none;
}

.single-post-content .post-navigation .prev-post a {
    justify-content: flex-start;
}

.single-post-content .post-navigation .next-post a {
    justify-content: flex-end;
    text-align: right;
}

.single-post-content .post-navigation i {
    font-size: 20px;
    color: var(--gold-dark);
}

.single-post-content .post-navigation .prev-post i {
    margin-right: 15px;
}

.single-post-content .post-navigation .next-post i {
    margin-left: 15px;
}

.single-post-content .post-navigation .post-nav-content span {
    display: block;
    font-size: 12px;
    color: #888;
    margin-bottom: 5px;
}

.single-post-content .post-navigation .post-nav-content h4 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    transition: var(--transition-fast);
}

.single-post-content .post-navigation a:hover .post-nav-content h4 {
    color: var(--gold-dark);
}

/* Comments Styles */
.comments-area {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-small);
}

.comments-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(26, 95, 141, 0.1);
    position: relative;
}

.comments-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 80px;
    height: 2px;
    background: linear-gradient(90deg, #03355c, #1a5f8d);
}

.comment-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.comment {
    margin-bottom: 30px;
}

.comment:last-child {
    margin-bottom: 0;
}

.comment-body {
    position: relative;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 10px;
}

.comment-author {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.comment-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 15px;
}

.comment-author .fn {
    font-size: 18px;
    font-weight: 600;
    font-style: normal;
}

.comment-metadata {
    font-size: 12px;
    color: #888;
    margin-bottom: 15px;
}

.comment-metadata a {
    color: #888;
    text-decoration: none;
}

.comment-content p {
    margin-bottom: 15px;
    color: #666;
    line-height: 1.7;
}

.reply {
    text-align: right;
}

.reply a {
    display: inline-block;
    padding: 5px 15px;
    background-color: rgba(26, 95, 141, 0.1);
    color: #03355c;
    border-radius: 5px;
    font-size: 14px;
    text-decoration: none;
    transition: var(--transition-fast);
}

.reply a:hover {
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    color: #fff;
}

.children {
    list-style: none;
    padding-left: 60px;
    margin-top: 30px;
}

.comment-respond {
    margin-top: 50px;
}

.comment-reply-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(26, 95, 141, 0.1);
    position: relative;
}

.comment-reply-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 80px;
    height: 2px;
    background: linear-gradient(90deg, #03355c, #1a5f8d);
}

.comment-form {
    margin-top: 20px;
}

.comment-form label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
}

.comment-form input[type="text"],
.comment-form input[type="email"],
.comment-form input[type="url"],
.comment-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #eee;
    border-radius: 5px;
    margin-bottom: 20px;
    outline: none;
    transition: var(--transition-fast);
}

.comment-form input[type="text"]:focus,
.comment-form input[type="email"]:focus,
.comment-form input[type="url"]:focus,
.comment-form textarea:focus {
    border-color: var(--gold-medium);
    box-shadow: 0 0 0 3px rgba(218, 165, 32, 0.1);
}

.comment-form textarea {
    height: 150px;
    resize: vertical;
}

.comment-form .form-submit {
    margin-top: 20px;
}

.comment-form input[type="submit"] {
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    padding: 12px 30px;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.comment-form input[type="submit"]:hover {
    background: linear-gradient(135deg, var(--gold-light), var(--gold-dark));
    transform: translateY(-3px);
    box-shadow: var(--shadow-small);
}

/* Related Posts Styles */
.related-posts {
    background-color: #f9f9f9;
    padding: 80px 0;
}

.single-related-post {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-small);
    margin-bottom: 30px;
    transition: var(--transition-medium);
}

.single-related-post:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.single-related-post .post-thumbnail {
    position: relative;
    overflow: hidden;
}

.single-related-post .post-thumbnail img {
    width: 100%;
    height: auto;
    transition: var(--transition-medium);
}

.single-related-post:hover .post-thumbnail img {
    transform: scale(1.05);
}

.single-related-post .post-info {
    padding: 20px;
}

.single-related-post .category {
    display: inline-block;
    margin-bottom: 10px;
    font-size: 13px;
    color: var(--gold-dark);
}

.single-related-post .title {
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 700;
    line-height: 1.4;
}

.single-related-post .title a {
    color: #333;
    text-decoration: none;
    transition: var(--transition-fast);
}

.single-related-post .title a:hover {
    color: var(--gold-dark);
}

.single-related-post .date {
    font-size: 13px;
    color: #888;
    display: flex;
    align-items: center;
}

.single-related-post .date i {
    color: var(--gold-dark);
    margin-right: 5px;
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .blog-sidebar {
        margin-top: 50px;
        position: static;
    }

    .single-post-content .post-navigation .prev-post,
    .single-post-content .post-navigation .next-post {
        margin-bottom: 15px;
    }
}

@media only screen and (max-width: 767px) {
    .blog-post .post-title {
        font-size: 20px;
    }

    .blog-post .post-meta {
        font-size: 12px;
    }

    .pagination .page-numbers .page-numbers,
    .pagination .prev-page a,
    .pagination .next-page a {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .widget {
        padding: 20px;
    }

    .widget-title {
        font-size: 18px;
    }

    .single-post-content .post-meta {
        padding: 15px 20px;
    }

    .single-post-content .post-content {
        padding: 20px;
    }

    .single-post-content .post-tags {
        padding: 15px 20px;
    }

    .single-post-content .post-navigation {
        padding: 20px;
    }

    .comments-area {
        padding: 20px;
    }

    .comments-title,
    .comment-reply-title {
        font-size: 20px;
    }

    .children {
        padding-left: 30px;
    }
}

@media only screen and (max-width: 575px) {
    .blog-post .post-meta {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .blog-post .post-content {
        padding: 20px;
    }

    .pagination .page-numbers {
        gap: 3px;
    }

    .pagination .page-numbers .page-numbers,
    .pagination .prev-page a,
    .pagination .next-page a {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }

    .single-post-content .post-meta {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .single-post-content .post-tags h4 {
        width: 100%;
        margin-bottom: 10px;
    }

    .single-post-content .post-navigation .post-nav-content h4 {
        font-size: 14px;
    }

    .comment-author img {
        width: 40px;
        height: 40px;
    }

    .comment-author .fn {
        font-size: 16px;
    }

    .children {
        padding-left: 15px;
    }
}
