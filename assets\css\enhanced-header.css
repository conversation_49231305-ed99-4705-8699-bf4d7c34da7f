/* Enhanced Header CSS for Zajel Arabic Theme */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --transition-slow: all 0.8s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 15px 60px rgba(0, 0, 0, 0.2);
}

/*======================================
    Enhanced Header CSS
========================================*/
.header.navbar-area {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 999;
    background-color: #fff;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
}

.header.navbar-area.sticky {
    background-color: #fff;
    box-shadow: var(--shadow-medium);
}

.nav-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
}

/* Logo Styling */
.navbar-brand {
    display: flex;
    align-items: center;
    padding: 0;
}

.navbar-brand img {
    max-height: 60px;
    width: auto;
    transition: var(--transition-fast);
}

.header.navbar-area.sticky .navbar-brand img {
    max-height: 50px;
}

.default-logo {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--gold-dark);
    transition: var(--transition-fast);
}

.default-logo i {
    color: var(--gold-dark);
    margin-right: 10px;
    font-size: 2rem;
    transition: var(--transition-fast);
}

/* Main Navigation */
.navbar-nav {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
}

.navbar-nav .nav-item {
    position: relative;
    margin-left: 40px;
}

.navbar-nav .nav-item:first-child {
    margin-left: 0;
}

.navbar-nav .nav-item a {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    padding: 30px 0;
    display: block;
    position: relative;
    transition: var(--transition-fast);
}

.navbar-nav .nav-item a:hover,
.navbar-nav .nav-item a.active {
    color: var(--gold-dark);
}

.navbar-nav .nav-item a::before {
    content: '';
    position: absolute;
    bottom: 25px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    transition: var(--transition-fast);
}

.navbar-nav .nav-item a:hover::before,
.navbar-nav .nav-item a.active::before {
    width: 100%;
}

/* Dropdown Menu */
.navbar-nav .nav-item .sub-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 220px;
    background-color: #fff;
    box-shadow: var(--shadow-medium);
    padding: 10px 0;
    border-radius: 0 0 5px 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: var(--transition-medium);
    z-index: 99;
}

.navbar-nav .nav-item:hover .sub-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.navbar-nav .nav-item .sub-menu li {
    position: relative;
    padding: 0;
}

.navbar-nav .nav-item .sub-menu li a {
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    display: block;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.navbar-nav .nav-item .sub-menu li:last-child a {
    border-bottom: none;
}

.navbar-nav .nav-item .sub-menu li a::before {
    display: none;
}

.navbar-nav .nav-item .sub-menu li a:hover {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    padding-left: 25px;
}

/* Social Icons */
.header-social {
    display: flex;
    align-items: center;
    margin-left: 30px;
}

.header-social ul {
    display: flex;
    padding: 0;
    margin: 0;
    list-style: none;
}

.header-social ul li {
    margin-right: 15px;
}

.header-social ul li:last-child {
    margin-right: 0;
}

.header-social ul li a {
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    display: block;
    border-radius: 50%;
    color: #333;
    background-color: #f5f5f5;
    font-size: 16px;
    transition: var(--transition-fast);
}

.header-social ul li a:hover {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    transform: translateY(-3px);
}

/* Trial Class Button */
.trial-class-button {
    margin-left: 30px;
}

.trial-class-btn {
    padding: 10px 20px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    border-radius: 30px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
    z-index: 1;
    display: inline-block;
}

.trial-class-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
    z-index: -1;
}

.trial-class-btn:hover::before {
    left: 100%;
    transition: 0.7s;
}

.trial-class-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(184, 134, 11, 0.3);
    color: #fff;
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    padding: 0;
    border: none;
    background: transparent;
    cursor: pointer;
}

.mobile-menu-btn:focus {
    outline: none;
    box-shadow: none;
}

.mobile-menu-btn .toggler-icon {
    width: 30px;
    height: 2px;
    background-color: #333;
    display: block;
    margin: 6px 0;
    position: relative;
    transition: var(--transition-fast);
}

.mobile-menu-btn.active .toggler-icon:nth-child(1) {
    transform: rotate(45deg);
    top: 8px;
}

.mobile-menu-btn.active .toggler-icon:nth-child(2) {
    opacity: 0;
}

.mobile-menu-btn.active .toggler-icon:nth-child(3) {
    transform: rotate(-45deg);
    top: -8px;
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-medium);
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .nav-inner {
        padding: 15px 0;
    }

    .navbar-nav .nav-item a {
        padding: 15px 0;
    }

    .mobile-menu-btn {
        display: block;
        position: relative;
        z-index: 999;
    }

    .navbar-collapse {
        position: fixed;
        top: 0;
        left: -280px;
        width: 280px;
        height: 100%;
        background-color: #fff;
        z-index: 999;
        padding: 0;
        transition: var(--transition-medium);
        overflow-y: auto;
        box-shadow: var(--shadow-medium);
    }

    .navbar-collapse.show {
        left: 0;
    }

    .navbar-nav {
        flex-direction: column;
        padding: 20px;
        margin-top: 60px;
    }

    .navbar-nav .nav-item {
        margin: 0;
        padding: 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .navbar-nav .nav-item:last-child {
        border-bottom: none;
    }

    .navbar-nav .nav-item a {
        padding: 12px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .navbar-nav .nav-item a::before {
        display: none;
    }

    .navbar-nav .nav-item a.dd-menu::after {
        content: '\f107';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        transition: var(--transition-fast);
    }

    .navbar-nav .nav-item a.dd-menu.active::after {
        transform: rotate(180deg);
    }

    .navbar-nav .nav-item .sub-menu {
        position: static;
        width: 100%;
        opacity: 1;
        visibility: visible;
        box-shadow: none;
        background-color: #f5f5f5;
        padding: 0;
        border-radius: 0;
        display: none;
        transform: none;
    }

    .navbar-nav .nav-item .sub-menu.show {
        display: block;
    }

    .navbar-nav .nav-item .sub-menu li a {
        padding: 10px 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .navbar-nav .nav-item .sub-menu li:last-child a {
        border-bottom: none;
    }

    /* Mobile Menu Header */
    .mobile-menu-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        position: fixed;
        top: 0;
        left: 0;
        width: 280px;
        background-color: #fff;
        z-index: 1000;
    }

    .mobile-menu-logo {
        max-width: 150px;
    }

    .mobile-menu-logo img {
        max-height: 40px;
        width: auto;
    }

    .mobile-menu-close {
        width: 30px;
        height: 30px;
        background-color: #f5f5f5;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition-fast);
    }

    .mobile-menu-close:hover {
        background-color: #e5e5e5;
    }

    .mobile-menu-close i {
        font-size: 16px;
        color: #333;
    }

    /* Mobile Social Icons */
    .mobile-social {
        padding: 20px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .mobile-social ul {
        display: flex;
        justify-content: center;
        padding: 0;
        margin: 0;
        list-style: none;
    }

    .mobile-social ul li {
        margin: 0 10px;
    }

    .mobile-social ul li a {
        width: 35px;
        height: 35px;
        line-height: 35px;
        text-align: center;
        display: block;
        border-radius: 50%;
        color: #333;
        background-color: #f5f5f5;
        font-size: 16px;
        transition: var(--transition-fast);
    }

    .mobile-social ul li a:hover {
        background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
        color: #fff;
    }

    /* Hide Desktop Elements on Mobile */
    .header-social,
    .trial-class-button {
        display: none;
    }

    /* Mobile Trial Class Button */
    .mobile-trial-button {
        padding: 20px;
        text-align: center;
    }

    .mobile-trial-btn {
        padding: 10px 20px;
        background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
        color: #fff;
        border-radius: 30px;
        font-weight: 600;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: var(--transition-fast);
        display: inline-block;
        width: 100%;
    }

    .mobile-trial-btn:hover {
        color: #fff;
        opacity: 0.9;
    }
}

@media only screen and (max-width: 767px) {
    .nav-inner {
        padding: 10px 0;
    }

    .navbar-brand img {
        max-height: 50px;
    }

    .header.navbar-area.sticky .navbar-brand img {
        max-height: 45px;
    }

    .default-logo {
        font-size: 1.5rem;
    }

    .default-logo i {
        font-size: 1.7rem;
    }

    .mobile-menu-btn .toggler-icon {
        width: 25px;
        height: 2px;
        margin: 5px 0;
    }
}
