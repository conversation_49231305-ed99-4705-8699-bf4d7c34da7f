<?php
// Add theme support
add_theme_support('menus');
add_theme_support('post-thumbnails');
add_theme_support('title-tag');

// Register menus
function register_edugrid_menus() {
    register_nav_menus(array(
        'primary-menu' => __('Primary Menu', 'edugrid'),
        'footer-menu-pages' => __('Footer Menu - Pages', 'edugrid'),
        'footer-menu-courses' => __('Footer Menu - Courses', 'edugrid'),
    ));
}
add_action('init', 'register_edugrid_menus');

// Enqueue styles and scripts
function edugrid_enqueue_scripts() {
    // Load CSS
    wp_enqueue_style('edugrid-style', get_stylesheet_uri());
    wp_enqueue_style('bootstrap', get_template_directory_uri() . '/assets/css/bootstrap.min.css');
    wp_enqueue_style('animate', get_template_directory_uri() . '/assets/css/animate.css');
    wp_enqueue_style('lineicons', get_template_directory_uri() . '/assets/css/LineIcons.2.0.css');
    wp_enqueue_style('fontawesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
    wp_enqueue_style('tiny-slider', get_template_directory_uri() . '/assets/css/tiny-slider.css');
    wp_enqueue_style('glightbox', get_template_directory_uri() . '/assets/css/glightbox.min.css');
    wp_enqueue_style('menu-fix', get_template_directory_uri() . '/assets/css/menu-fix.css');
    wp_enqueue_style('main', get_template_directory_uri() . '/assets/css/main.css');
    if (is_page('contact')) {
        wp_enqueue_style('contact-styles', get_template_directory_uri() . '/assets/css/contact.css', array(), '1.0.3');
    }
    if (is_page('front-page')) {
        wp_enqueue_style('enhanced-sections', get_template_directory_uri() . '/assets/css/enhanced-sections.css', array(), '1.0.4');
        }

    // Load page-specific CSS
        wp_enqueue_style('about-us', get_template_directory_uri() . '/assets/css/about-us.css', array(), '1.0.4');

    // Load testimonial slider script for About Us page and front page
    if (is_page('about-us') || is_page_template('page-about-us.php') || is_front_page()) {
        wp_enqueue_script('testimonial-slider', get_template_directory_uri() . '/assets/js/testimonial-slider-advanced.js', array('jquery'), '1.0.0', true);
    }

    // Load advisors slider script for About Us page
    if (is_page('about-us') || is_page_template('page-about-us.php')) {
        wp_enqueue_script('advisors-slider', get_template_directory_uri() . '/assets/js/advisors-slider.js', array('jquery'), '1.0.1', true);
    }


    // Load animations CSS
    wp_enqueue_style('animations', get_template_directory_uri() . '/assets/css/animations.css', array(), '1.0.4');

    // Load enhanced footer styles
    wp_enqueue_style('footer-enhanced', get_template_directory_uri() . '/assets/css/footer-enhanced.css', array(), '1.0.4');

    // Load enhanced sections styles

    // Load blog styles
    wp_enqueue_style('blog-styles', get_template_directory_uri() . '/assets/css/blog.css', array(), '1.0.4');

    // Load enhanced blog styles and scripts for front page
    if (is_front_page()) {
        wp_enqueue_style('blog-enhanced', get_template_directory_uri() . '/assets/css/blog-enhanced.css', array(), '1.0.0');
        wp_enqueue_script('blog-enhanced-js', get_template_directory_uri() . '/assets/js/blog-enhanced.js', array('jquery'), '1.0.0', true);
    }

    // Load blog page styles and scripts
    if (is_home() || is_page_template('page-blog.php') || strpos($_SERVER['REQUEST_URI'], '/blog/') !== false || is_single()) {
        wp_enqueue_style('blog-page', get_template_directory_uri() . '/assets/css/blog-page.css', array(), '1.0.0');
        wp_enqueue_style('blog-professional', get_template_directory_uri() . '/assets/css/blog-professional.css', array(), '1.0.0');
        wp_enqueue_style('comments-fix', get_template_directory_uri() . '/assets/css/comments-fix.css', array(), '1.0.0');
        wp_enqueue_script('blog-professional-js', get_template_directory_uri() . '/assets/js/blog-professional.js', array('jquery'), '1.0.0', true);
    }

    // Load archive page styles
    if (is_archive()) {
        wp_enqueue_style('archive-page', get_template_directory_uri() . '/assets/css/archive-page.css', array(), '1.0.0');
    }

    // Load single post styles
    if (is_singular('post')) {
        wp_enqueue_style('single-post', get_template_directory_uri() . '/assets/css/single-post.css', array(), '1.0.0');
    }

    // Load courses slider script and styles for front page
    if (is_front_page()) {
        wp_enqueue_style('courses-slider-css', get_template_directory_uri() . '/assets/css/courses-slider.css', array(), '1.0.0');
        wp_enqueue_script('courses-slider', get_template_directory_uri() . '/assets/js/courses-slider.js', array('jquery'), '1.0.0', true);
    }

    // Load pricing page styles if on pricing page
    if (is_page('pricing')) {
        wp_enqueue_style('pricing-page', get_template_directory_uri() . '/assets/css/pricing-page.css', array(), '1.0.4');
        wp_enqueue_script('pricing-page-js', get_template_directory_uri() . '/assets/js/pricing-page.js', array('jquery'), '1.0', true);
    }


    // Load teachers page styles if on teachers page
    if (is_page('teachers') || is_page_template('page-teachers.php')) {
        wp_enqueue_style('teachers-page', get_template_directory_uri() . '/assets/css/teachers-page.css', array(), '1.0.4');
        wp_enqueue_style('teachers-slider', get_template_directory_uri() . '/assets/css/teachers-slider.css', array(), '1.0.0');
    }

    // Load course single page styles if on single course page
    if (is_singular('course')) {
        wp_enqueue_style('course-single', get_template_directory_uri() . '/assets/css/course-single.css', array(), '1.0.0');
        wp_enqueue_style('single-course', get_template_directory_uri() . '/assets/css/single-course.css', array(), '1.0.0');
    }

    // Load courses page styles if on courses page
    if (is_page('courses')) {
        wp_enqueue_style('courses-archive', get_template_directory_uri() . '/assets/css/courses-archive.css', array(), '1.0.0');
    }


    // Load Contact Form 7 custom styles
    if (function_exists('wpcf7_enqueue_scripts')) {
        wp_enqueue_style('cf7-custom', get_template_directory_uri() . '/assets/css/cf7-custom.css');
    }


    // Load JS
    wp_enqueue_script('jquery'); // التأكد من تحميل jQuery
    // wp_enqueue_script('bootstrap-js', get_template_directory_uri() . '/assets/js/bootstrap.min.js', array('jquery'), '5.1.3', true);
    wp_enqueue_script('count-up', get_template_directory_uri() . '/assets/js/count-up.min.js', array('jquery'), '1.0', true);
    wp_enqueue_script('wow', get_template_directory_uri() . '/assets/js/wow.min.js', array('jquery'), '1.0', true);
    wp_enqueue_script('tiny-slider', get_template_directory_uri() . '/assets/js/tiny-slider.js', array('jquery'), '1.0', true);
    wp_enqueue_script('glightbox', get_template_directory_uri() . '/assets/js/glightbox.min.js', array('jquery'), '1.0', true);
    wp_enqueue_script('main-js', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), '1.0', true);
    wp_enqueue_script('custom-js', get_template_directory_uri() . '/assets/js/customizer.js', array('jquery'), '1.0', true); // إضافة custom.js
    wp_enqueue_script('footer-scripts', get_template_directory_uri() . '/assets/js/footer-scripts.js', array('jquery'), '1.0', true); // إضافة footer-scripts.js
    wp_enqueue_script('enhanced-sections', get_template_directory_uri() . '/assets/js/enhanced-sections.js', array('jquery'), '1.0', true);

    // Load teachers page scripts if on teachers page
    if (is_page('teachers') || is_page_template('page-teachers.php')) {
        wp_enqueue_script('teachers-page', get_template_directory_uri() . '/assets/js/teachers-page.js', array('jquery'), '1.0', true);
        wp_enqueue_script('teachers-slider', get_template_directory_uri() . '/assets/js/teachers-slider.js', array('jquery'), '1.0.0', true);
    }


// Inline JavaScript for Sliders and GLightbox
$inline_js = "
jQuery(document).ready(function($) {

    tns({
        container: '.client-logo-carousel',
        slideBy: 'page',
        autoplay: true,
        autoplayButtonOutput: false,
        mouseDrag: true,
        gutter: 15,
        nav: false,
        controls: false,
        responsive: {
            0: { items: 1 },
            540: { items: 3 },
            768: { items: 4 },
            992: { items: 4 },
            1170: { items: 6 }
        }
    });

    GLightbox({
        selector: '.glightbox',
        type: 'video',
        source: 'youtube',
        width: 900,
        autoplayVideos: true,
    });
});
";
wp_add_inline_script('main-js', $inline_js);
// Dynamic Hero Background, Overlay, and Height CSS
$hero_background_image = get_theme_mod('zajel_hero_background_image', '');
$hero_overlay_color = get_theme_mod('zajel_hero_overlay_color', '#000000');
$hero_overlay_opacity = get_theme_mod('zajel_hero_overlay_opacity', '0.5');
$hero_height = get_theme_mod('zajel_hero_height', '780');

// Default background image if none is set
$background_image_url = $hero_background_image ? esc_url($hero_background_image) : esc_url(get_template_directory_uri() . 'assets/images/homepage.jpg');
$custom_css = "
    .hero-area.style3 .hero-inner {
        position: relative;
        z-index: 2;
        padding: 0;
        background: url('" . $background_image_url . "') center center / cover no-repeat;
        height: " . esc_attr($hero_height) . "px;
    }
    .hero-area.style3 .hero-inner .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: " . esc_attr($hero_overlay_color) . ";
        opacity: " . esc_attr($hero_overlay_opacity) . ";
        z-index: 1;
    }
    .hero-area.style3 .hero-inner .container {
        position: relative;
        z-index: 2;
    }
";
wp_add_inline_style('edugrid-style', $custom_css);

}
add_action('wp_enqueue_scripts', 'edugrid_enqueue_scripts');

function zajel_register_custom_post_types() {
    // Courses CPT
    register_post_type('course', array(
        'labels' => array(
            'name' => __('Courses', 'zajel'),
            'singular_name' => __('Course', 'zajel'),
            'add_new' => __('Add New Course', 'zajel'),
            'add_new_item' => __('Add New Course', 'zajel'),
            'edit_item' => __('Edit Course', 'zajel'),
            'new_item' => __('New Course', 'zajel'),
            'view_item' => __('View Course', 'zajel'),
            'search_items' => __('Search Courses', 'zajel'),
            'not_found' => __('No courses found', 'zajel'),
            'not_found_in_trash' => __('No courses found in Trash', 'zajel'),
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'comments', 'custom-fields', 'yoast-seo'),
        'menu_icon' => 'dashicons-book-alt',
        'rewrite' => array('slug' => 'courses'),
        // 'show_in_rest' => true, // Enable REST API for Gutenberg
        'taxonomies' => array('category', 'post_tag'), // Add categories and tags
    ));


    // Teachers CPT
    register_post_type('teacher', array(
        'labels' => array(
            'name' => __('Teachers', 'zajel'),
            'singular_name' => __('Teacher', 'zajel'),
            'add_new' => __('Add New Teacher', 'zajel'),
            'add_new_item' => __('Add New Teacher', 'zajel'),
            'edit_item' => __('Edit Teacher', 'zajel'),
            'new_item' => __('New Teacher', 'zajel'),
            'view_item' => __('View Teacher', 'zajel'),
            'search_items' => __('Search Teachers', 'zajel'),
            'not_found' => __('No teachers found', 'zajel'),
            'not_found_in_trash' => __('No teachers found in Trash', 'zajel'),
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
        'menu_icon' => 'dashicons-groups',
        'rewrite' => array('slug' => 'teachers'),
    ));

    // Testimonials CPT
    register_post_type('testimonial', array(
        'labels' => array(
            'name' => __('Testimonials', 'zajel'),
            'singular_name' => __('Testimonial', 'zajel'),
        ),
        'public' => true,
        'has_archive' => false,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-testimonial',
    ));
}
add_action('init', 'zajel_register_custom_post_types');

// Bootstrap Nav Walker (for dropdown menus)
if (!class_exists('WP_Bootstrap_Navwalker')) {
    class WP_Bootstrap_Navwalker extends Walker_Nav_Menu {
        public function start_lvl(&$output, $depth = 0, $args = null) {
            $output .= '<ul class="sub-menu collapse" id="submenu-' . $depth . '-' . rand(1, 999) . '">';
        }

        public function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
            $classes = empty($item->classes) ? array() : (array) $item->classes;
            $classes[] = 'nav-item';
            if (in_array('menu-item-has-children', $classes)) {
                $classes[] = 'dd-menu';
            }
            $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
            $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

            $output .= '<li' . $class_names . '>';

            $attributes = !empty($item->attr_title) ? ' title="' . esc_attr($item->attr_title) . '"' : '';
            $attributes .= !empty($item->target) ? ' target="' . esc_attr($item->target) . '"' : '';
            $attributes .= !empty($item->xfn) ? ' rel="' . esc_attr($item->xfn) . '"' : '';
            $attributes .= !empty($item->url) ? ' href="' . esc_attr($item->url) . '"' : '';
            if (in_array('menu-item-has-children', $classes)) {
                $attributes .= ' class="page-scroll dd-menu collapsed" data-bs-toggle="collapse" data-bs-target="#submenu-' . $depth . '-' . rand(1, 999) . '" aria-expanded="false" aria-label="Toggle navigation"';
            } else {
                $attributes .= ' class="page-scroll"';
            }

            $item_output = $args->before;
            $item_output .= '<a' . $attributes . '>';
            $item_output .= $args->link_before . apply_filters('the_title', $item->title, $item->ID) . $args->link_after;
            $item_output .= '</a>';
            $item_output .= $args->after;

            $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
        }
    }
}

// دالة لإنشاء الجدول
function zajel_create_contact_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'contact'; // اسم الجدول مع الـ prefix (مثلاً wp_contact)
    $charset_collate = $wpdb->get_charset_collate(); // إعدادات الترميز

    // تحقق إن الجدول مش موجود قبل ما نعمله
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            subject VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            message TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // إضافة إصدار الجدول في الخيارات
        update_option('zajel_contact_table_version', '1.0');
    }
}

/**
 * Custom Pagination for Blog Pages
 */
function zajel_pagination() {
    global $wp_query;
    $big = 999999999; // need an unlikely integer

    if ($wp_query->max_num_pages <= 1) {
        return; // Don't print pagination if there's only one page
    }

    $pages = paginate_links(array(
        'base'      => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
        'format'    => '?paged=%#%',
        'current'   => max(1, get_query_var('paged')),
        'total'     => $wp_query->max_num_pages,
        'type'      => 'array',
        'prev_text' => '<i class="fas fa-angle-left"></i> Previous',
        'next_text' => 'Next <i class="fas fa-angle-right"></i>',
        'end_size'  => 1,
        'mid_size'  => 2
    ));

    if (is_array($pages)) {
        echo '<div class="pagination">';

        // Previous button
        if (get_previous_posts_link()) {
            echo '<div class="prev-page">';
            previous_posts_link('<i class="fas fa-angle-left"></i> Previous');
            echo '</div>';
        }

        // Page numbers
        echo '<div class="page-numbers">';
        foreach ($pages as $page) {
            echo $page;
        }
        echo '</div>';

        // Next button
        if (get_next_posts_link()) {
            echo '<div class="next-page">';
            next_posts_link('Next <i class="fas fa-angle-right"></i>');
            echo '</div>';
        }

        echo '</div>';
    }
}

// تشغيل الدالة عند تحميل ووردبريس
add_action('init', 'zajel_create_contact_table');

// إضافة صفحة مخصصة في الداشبورد لعرض الرسائل
function zajel_contact_messages_menu() {
    add_menu_page(
        'Contact Messages',
        'Contact Messages',
        'manage_options',
        'zajel-contact-messages',
        'zajel_contact_messages_page',
        'dashicons-email-alt',
        20
    );
}
add_action('admin_menu', 'zajel_contact_messages_menu');

// دالة لعرض محتوى الصفحة
function zajel_contact_messages_page() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'contact';
    $messages = $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC");

    ?>
    <div class="wrap">
        <h1>Contact Messages</h1>
        <?php if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) : ?>
            <p style="color: red;">الجدول <?php echo $table_name; ?> غير موجود في قاعدة البيانات. تأكد من إنشاء الجدول.</p>
        <?php else : ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Subject</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Message</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($messages) : ?>
                        <?php foreach ($messages as $message) : ?>
                            <tr>
                                <td><?php echo esc_html($message->id); ?></td>
                                <td><?php echo esc_html($message->name); ?></td>
                                <td><?php echo esc_html($message->subject); ?></td>
                                <td><?php echo esc_html($message->email); ?></td>
                                <td><?php echo esc_html($message->phone); ?></td>
                                <td><?php echo esc_html($message->message); ?></td>
                                <td><?php echo esc_html($message->created_at); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else : ?>
                        <tr>
                            <td colspan="7">لا توجد رسائل بعد.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
    <?php
}
// Custom Walker Class for Navigation Menu
class Zajel_Nav_Walker extends Walker_Nav_Menu {
    // Start Level (ul)
    function start_lvl(&$output, $depth = 0, $args = null) {
        $indent = str_repeat("\t", $depth);
        $submenu_id = "submenu-" . rand(1, 9999); // ID عشوائي لكل submenu
        $output .= "\n$indent<ul class=\"sub-menu collapse\" id=\"$submenu_id\">\n";
    }

    // Start Element (li)
    function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
        $indent = ($depth) ? str_repeat("\t", $depth) : '';

        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'nav-item';

        // إضافة كلاسات للـ <li>
        $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

        $id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';

        $output .= $indent . '<li' . $id . $class_names . '>';

        // إعداد السمات (Attributes) للـ <a>
        $atts = array();
        $atts['title']  = !empty($item->attr_title) ? $item->attr_title : '';
        $atts['target'] = !empty($item->target) ? $item->target : '';
        $atts['rel']    = !empty($item->xfn) ? $item->xfn : '';
        $atts['href']   = !empty($item->url) ? $item->url : '#';

        // إضافة الكلاس active للـ <a> لو الصفحة الحالية أو الأب
        $link_classes = array();
        if (in_array('current-menu-item', $classes) || in_array('current-menu-parent', $classes)) {
            $link_classes[] = 'active';
        }

        // إضافة كلاسات و Data Attributes لو فيه Submenu
        if ($this->has_children) {
            $link_classes[] = 'dd-menu collapsed';
            $atts['href'] = 'javascript:void(0)';
            $atts['data-bs-toggle'] = 'collapse';
            $atts['data-bs-target'] = '#submenu-' . rand(1, 9999);
            $atts['aria-controls'] = 'navbarSupportedContent';
            $atts['aria-expanded'] = 'false';
            $atts['aria-label'] = 'Toggle navigation';
        }

        // إضافة كلاسات الـ <a>
        if (!empty($link_classes)) {
            $atts['class'] = implode(' ', $link_classes);
        }

        $atts = apply_filters('nav_menu_link_attributes', $atts, $item, $args);

        $attributes = '';
        foreach ($atts as $attr => $value) {
            if (!empty($value)) {
                $value = ('href' === $attr) ? esc_url($value) : esc_attr($value);
                $attributes .= ' ' . $attr . '="' . $value . '"';
            }
        }

        $item_output = $args->before;
        $item_output .= '<a' . $attributes . '>';
        $item_output .= $args->link_before . apply_filters('the_title', $item->title, $item->ID) . $args->link_after;
        $item_output .= '</a>';
        $item_output .= $args->after;

        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }
}

// التأكد من إن الثيم يدعم الـ Custom Logo والـ Menu
function zajel_theme_setup() {
    add_theme_support('custom-logo', array(
        'height'      => 100,
        'width'       => 300,
        'flex-height' => true,
        'flex-width'  => true,
    ));

    register_nav_menus(array(
        'primary-menu' => __('Primary Menu', 'zajel'),
        'footer-pages' => __('Footer Pages Menu', 'zajel'),
        'footer-courses' => __('Footer Courses Menu', 'zajel'),
    ));
}

// Fix course links in navigation menu
function zajel_fix_course_links_in_menu($items) {
    foreach ($items as $item) {
        // Check if the URL contains '/courses/' and is a course post type link
        if (strpos($item->url, '/courses/') !== false && strpos($item->url, home_url('/courses/')) !== false) {
            // Replace '/courses/' with '/course/' in the URL
            $item->url = str_replace('/courses/', '/course/', $item->url);
        }
    }
    return $items;
}
add_filter('wp_nav_menu_objects', 'zajel_fix_course_links_in_menu');
add_action('after_setup_theme', 'zajel_theme_setup');

// إضافة إعدادات السوشيال ميديا في الـ Customizer
function zajel_social_media_customizer($wp_customize) {
    // إضافة قسم جديد للسوشيال ميديا
    $wp_customize->add_section('zajel_social_media', array(
        'title'    => __('Social Media Links', 'zajel'),
        'priority' => 30,
    ));

    // إعداد وحقل لينك Facebook
    $wp_customize->add_setting('zajel_facebook_url', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    $wp_customize->add_control('zajel_facebook_url', array(
        'label'    => __('Facebook URL', 'zajel'),
        'section'  => 'zajel_social_media',
        'type'     => 'url',
    ));

    // إعداد وحقل لينك Twitter
    $wp_customize->add_setting('zajel_twitter_url', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    $wp_customize->add_control('zajel_twitter_url', array(
        'label'    => __('Twitter URL', 'zajel'),
        'section'  => 'zajel_social_media',
        'type'     => 'url',
    ));

    // إعداد وحقل لينك LinkedIn
    $wp_customize->add_setting('zajel_linkedin_url', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    $wp_customize->add_control('zajel_linkedin_url', array(
        'label'    => __('LinkedIn URL', 'zajel'),
        'section'  => 'zajel_social_media',
        'type'     => 'url',
    ));

    // إعداد وحقل لينك Instagram
    $wp_customize->add_setting('zajel_instagram_url', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    $wp_customize->add_control('zajel_instagram_url', array(
        'label'    => __('Instagram URL', 'zajel'),
        'section'  => 'zajel_social_media',
        'type'     => 'url',
    ));

    // إعداد وحقل لينك YouTube
    $wp_customize->add_setting('zajel_youtube_url', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    $wp_customize->add_control('zajel_youtube_url', array(
        'label'    => __('YouTube URL', 'zajel'),
        'section'  => 'zajel_social_media',
        'type'     => 'url',
    ));

    // إعداد وحقل لينك WhatsApp
    $wp_customize->add_setting('zajel_whatsapp_url', array(
        'default'           => 'https://wa.me/1234567890',
        'sanitize_callback' => 'esc_url_raw',
    ));
    $wp_customize->add_control('zajel_whatsapp_url', array(
        'label'    => __('WhatsApp URL (https://wa.me/YOUR_NUMBER)', 'zajel'),
        'section'  => 'zajel_social_media',
        'type'     => 'url',
    ));
}
add_action('customize_register', 'zajel_social_media_customizer');

// إضافة إعدادات الـ Footer في الـ Customizer
function zajel_footer_customizer($wp_customize) {
    // إضافة قسم جديد للـ Footer
    $wp_customize->add_section('zajel_footer_section', array(
        'title'    => __('Footer Settings', 'zajel'),
        'priority' => 35,
    ));

    // إعداد وحقل نص الـ About
    $wp_customize->add_setting('zajel_footer_about', array(
        'default'           => 'Zajel Arabic Institute is dedicated to teaching Arabic language, Quran, and Islamic studies to non-Arabic speakers worldwide through interactive online classes with qualified native Arabic-speaking teachers.',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_footer_about', array(
        'label'    => __('Footer About Text', 'zajel'),
        'section'  => 'zajel_footer_section',
        'type'     => 'textarea',
    ));

    // إعداد وحقل عنوان قسم الصفحات
    $wp_customize->add_setting('zajel_footer_pages_title', array(
        'default'           => 'Quick Links',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_footer_pages_title', array(
        'label'    => __('Pages Section Title', 'zajel'),
        'section'  => 'zajel_footer_section',
        'type'     => 'text',
    ));

    // إعداد وحقل عنوان قسم الدورات
    $wp_customize->add_setting('zajel_footer_courses_title', array(
        'default'           => 'Our Courses',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_footer_courses_title', array(
        'label'    => __('Courses Section Title', 'zajel'),
        'section'  => 'zajel_footer_section',
        'type'     => 'text',
    ));

    // إعداد وحقل عنوان قسم النشرة الإخبارية
    $wp_customize->add_setting('zajel_footer_newsletter_title', array(
        'default'           => 'Newsletter',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_footer_newsletter_title', array(
        'label'    => __('Newsletter Section Title', 'zajel'),
        'section'  => 'zajel_footer_section',
        'type'     => 'text',
    ));

    // إعداد وحقل وصف النشرة الإخبارية
    $wp_customize->add_setting('zajel_footer_newsletter_description', array(
        'default'           => 'Subscribe to our newsletter to receive updates on new courses, special offers, and educational content.',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_footer_newsletter_description', array(
        'label'    => __('Newsletter Description', 'zajel'),
        'section'  => 'zajel_footer_section',
        'type'     => 'textarea',
    ));

    // إعداد وحقل شورتكود نموذج النشرة الإخبارية
    $wp_customize->add_setting('zajel_newsletter_form_shortcode', array(
        'default'           => '[contact-form-7 id="124" title="Newsletter Form"]',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_newsletter_form_shortcode', array(
        'label'    => __('Newsletter Form Shortcode', 'zajel'),
        'section'  => 'zajel_footer_section',
        'type'     => 'text',
    ));
}
add_action('customize_register', 'zajel_footer_customizer');

function zajel_front_page_customizer($wp_customize) {
    // Hero Section
    $wp_customize->add_section('zajel_hero_section', array(
        'title' => __('Hero Section', 'zajel'),
        'priority' => 30,
    ));
    $wp_customize->add_setting('zajel_hero_subtitle', array(
        'default' => 'Hi, I am Devid Milan.',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_hero_subtitle', array(
        'label' => __('Hero Subtitle', 'zajel'),
        'section' => 'zajel_hero_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_hero_title', array(
        'default' => 'Bringing You a positive and awakening <span>perspective</span> on <span>your future.</span>',
        'sanitize_callback' => 'wp_kses_post',
    ));
    $wp_customize->add_control('zajel_hero_title', array(
        'label' => __('Hero Title', 'zajel'),
        'section' => 'zajel_hero_section',
        'type' => 'textarea',
    ));
    $wp_customize->add_setting('zajel_hero_description', array(
        'default' => 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since.',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    $wp_customize->add_control('zajel_hero_description', array(
        'label' => __('Hero Description', 'zajel'),
        'section' => 'zajel_hero_section',
        'type' => 'textarea',
    ));
    $wp_customize->add_setting('zajel_hero_button_text', array(
        'default' => 'Download Free E-Book',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_hero_button_text', array(
        'label' => __('Hero Button Text', 'zajel'),
        'section' => 'zajel_hero_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_hero_button_url', array(
        'default' => '#',
        'sanitize_callback' => 'esc_url_raw',
    ));
    $wp_customize->add_control('zajel_hero_button_url', array(
        'label' => __('Hero Button URL', 'zajel'),
        'section' => 'zajel_hero_section',
        'type' => 'url',
    ));
    $wp_customize->add_setting('zajel_hero_video_url', array(
        'default' => 'https://www.youtube.com/watch?v=r44RKWyfcFw',
        'sanitize_callback' => 'esc_url_raw',
    ));
    $wp_customize->add_control('zajel_hero_video_url', array(
        'label' => __('Hero Video URL', 'zajel'),
        'section' => 'zajel_hero_section',
        'type' => 'url',
    ));
// Hero Background Image
$wp_customize->add_setting('zajel_hero_background_image', array(
    'default' => '',
    'sanitize_callback' => 'esc_url_raw',
));
$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'zajel_hero_background_image', array(
    'label' => __('Hero Background Image', 'zajel'),
    'section' => 'zajel_hero_section',
    'settings' => 'zajel_hero_background_image',
)));

// Hero Overlay Color
$wp_customize->add_setting('zajel_hero_overlay_color', array(
    'default' => '#000000',
    'sanitize_callback' => 'sanitize_hex_color',
));
$wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'zajel_hero_overlay_color', array(
    'label' => __('Hero Overlay Color', 'zajel'),
    'section' => 'zajel_hero_section',
    'settings' => 'zajel_hero_overlay_color',
)));

// Hero Overlay Opacity
$wp_customize->add_setting('zajel_hero_overlay_opacity', array(
    'default' => '0.5',
    'sanitize_callback' => 'sanitize_text_field',
));
$wp_customize->add_control('zajel_hero_overlay_opacity', array(
    'label' => __('Hero Overlay Opacity (0 to 1)', 'zajel'),
    'section' => 'zajel_hero_section',
    'type' => 'number',
    'input_attrs' => array(
        'min' => 0,
        'max' => 1,
        'step' => 0.1,
    ),
));

// Hero Section Height
$wp_customize->add_setting('zajel_hero_height', array(
    'default' => '780',
    'sanitize_callback' => 'absint',
));
$wp_customize->add_control('zajel_hero_height', array(
    'label' => __('Hero Section Height (px)', 'zajel'),
    'section' => 'zajel_hero_section',
    'type' => 'number',
    'input_attrs' => array(
        'min' => 400,
        'max' => 1200,
        'step' => 10,
    ),
));

// باقي الإعدادات للأقسام الأخرى (Mission, Achievement, إلخ) موجودة هنا
// ...

    // Mission Section
    $wp_customize->add_section('zajel_mission_section', array(
        'title' => __('Mission Section', 'zajel'),
        'priority' => 31,
    ));
    $wp_customize->add_setting('zajel_mission_subtitle', array(
        'default' => 'My Mission & Philosophy',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_mission_subtitle', array(
        'label' => __('Mission Subtitle', 'zajel'),
        'section' => 'zajel_mission_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_mission_quote', array(
        'default' => '“There is nothing noble in being superior to your fellow man; true nobility is being superior to your former self.”',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    $wp_customize->add_control('zajel_mission_quote', array(
        'label' => __('Mission Quote', 'zajel'),
        'section' => 'zajel_mission_section',
        'type' => 'textarea',
    ));
    $wp_customize->add_setting('zajel_mission_image', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'zajel_mission_image', array(
        'label' => __('Mission Image', 'zajel'),
        'section' => 'zajel_mission_section',
        'settings' => 'zajel_mission_image',
    )));

     // Achievement Section
     $wp_customize->add_section('zajel_achievement_section', array(
        'title' => __('Achievement Section', 'zajel'),
        'priority' => 32,
    ));

    // Background Color
    $wp_customize->add_setting('zajel_achievement_bg_color', array(
        'default' => '#B8860B',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport' => 'postMessage',
    ));
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'zajel_achievement_bg_color', array(
        'label' => __('Background Color', 'zajel'),
        'section' => 'zajel_achievement_section',
    )));

    // Background Image
    $wp_customize->add_setting('zajel_achievement_bg_image', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
        'transport' => 'postMessage',
    ));
    $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'zajel_achievement_bg_image', array(
        'label' => __('Background Image', 'zajel'),
        'section' => 'zajel_achievement_section',
        'settings' => 'zajel_achievement_bg_image',
    )));

    // Overlay Color (for ::before)
    $wp_customize->add_setting('zajel_achievement_overlay_color', array(
        'default' => '#B8860B',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport' => 'postMessage',
    ));
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'zajel_achievement_overlay_color', array(
        'label' => __('Overlay Color', 'zajel'),
        'section' => 'zajel_achievement_section',
    )));

    // Overlay Opacity (for ::before)
    $wp_customize->add_setting('zajel_achievement_overlay_opacity', array(
        'default' => '0.92',
        'sanitize_callback' => 'sanitize_text_field',
        'transport' => 'postMessage',
    ));
    $wp_customize->add_control('zajel_achievement_overlay_opacity', array(
        'label' => __('Overlay Opacity', 'zajel'),
        'section' => 'zajel_achievement_section',
        'type' => 'range',
        'input_attrs' => array(
            'min' => 0,
            'max' => 1,
            'step' => 0.01,
        ),
    ));

    // Achievement Fields (Number and Title)
    for ($i = 1; $i <= 4; $i++) {
        $wp_customize->add_setting('zajel_achievement_' . $i . '_number', array(
            'default' => '500',
            'sanitize_callback' => 'sanitize_text_field',
            'transport' => 'postMessage',
        ));
        $wp_customize->add_control('zajel_achievement_' . $i . '_number', array(
            'label' => sprintf(__('Achievement %d Number', 'zajel'), $i),
            'section' => 'zajel_achievement_section',
            'type' => 'text',
        ));

        $wp_customize->add_setting('zajel_achievement_' . $i . '_title', array(
            'default' => 'Happy Clients',
            'sanitize_callback' => 'sanitize_text_field',
            'transport' => 'postMessage',
        ));
        $wp_customize->add_control('zajel_achievement_' . $i . '_title', array(
            'label' => sprintf(__('Achievement %d Title', 'zajel'), $i),
            'section' => 'zajel_achievement_section',
            'type' => 'text',
        ));
    }


    // Services Section
    $wp_customize->add_section('zajel_services_section', array(
        'title' => __('Services Section', 'zajel'),
        'priority' => 33,
    ));
    $wp_customize->add_setting('zajel_services_subtitle', array(
        'default' => 'What Do I offer',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_services_subtitle', array(
        'label' => __('Services Subtitle', 'zajel'),
        'section' => 'zajel_services_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_services_title', array(
        'default' => 'Explore Services',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_services_title', array(
        'label' => __('Services Title', 'zajel'),
        'section' => 'zajel_services_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_services_description', array(
        'default' => 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    $wp_customize->add_control('zajel_services_description', array(
        'label' => __('Services Description', 'zajel'),
        'section' => 'zajel_services_section',
        'type' => 'textarea',
    ));
    for ($i = 1; $i <= 3; $i++) {
        $wp_customize->add_setting('zajel_service_' . $i . '_icon', array(
            'default' => 'lni lni-microphone',
            'sanitize_callback' => 'sanitize_text_field',
        ));
        $wp_customize->add_control('zajel_service_' . $i . '_icon', array(
            'label' => sprintf(__('Service %d Icon (LineIcons Class)', 'zajel'), $i),
            'section' => 'zajel_services_section',
            'type' => 'text',
        ));
        $wp_customize->add_setting('zajel_service_' . $i . '_title', array(
            'default' => 'Language Learning',
            'sanitize_callback' => 'sanitize_text_field',
        ));
        $wp_customize->add_control('zajel_service_' . $i . '_title', array(
            'label' => sprintf(__('Service %d Title', 'zajel'), $i),
            'section' => 'zajel_services_section',
            'type' => 'text',
        ));
        $wp_customize->add_setting('zajel_service_' . $i . '_description', array(
            'default' => 'We dejoy working with discerning clients, people for whom quality, service, integrity & aesthetics.',
            'sanitize_callback' => 'sanitize_textarea_field',
        ));
        $wp_customize->add_control('zajel_service_' . $i . '_description', array(
            'label' => sprintf(__('Service %d Description', 'zajel'), $i),
            'section' => 'zajel_services_section',
            'type' => 'textarea',
        ));
        $wp_customize->add_setting('zajel_service_' . $i . '_url', array(
            'default' => '#',
            'sanitize_callback' => 'esc_url_raw',
        ));
        $wp_customize->add_control('zajel_service_' . $i . '_url', array(
            'label' => sprintf(__('Service %d URL', 'zajel'), $i),
            'section' => 'zajel_services_section',
            'type' => 'url',
        ));
        $wp_customize->add_setting('zajel_service_' . $i . '_button_text', array(
            'default' => 'Read More',
            'sanitize_callback' => 'sanitize_text_field',
        ));
        $wp_customize->add_control('zajel_service_' . $i . '_button_text', array(
            'label' => sprintf(__('Service %d Button Text', 'zajel'), $i),
            'section' => 'zajel_services_section',
            'type' => 'text',
        ));
    }

    // Courses Section
    $wp_customize->add_section('zajel_courses_section', array(
        'title' => __('Courses Section', 'zajel'),
        'priority' => 34,
    ));

    // Courses Page Background Color
    $wp_customize->add_setting('zajel_courses_bg_color', array(
        'default' => '#ffffff',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport' => 'postMessage',
    ));
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'zajel_courses_bg_color', array(
        'label' => __('Courses Page Background Color', 'zajel'),
        'section' => 'zajel_courses_section',
    )));

    // Courses Subtitle
    $wp_customize->add_setting('zajel_courses_subtitle', array(
        'default' => 'Online Learning',
        'sanitize_callback' => 'sanitize_text_field',
        'transport' => 'postMessage',
    ));
    $wp_customize->add_control('zajel_courses_subtitle', array(
        'label' => __('Courses Subtitle', 'zajel'),
        'section' => 'zajel_courses_section',
        'type' => 'text',
    ));

    // Courses Title
    $wp_customize->add_setting('zajel_courses_title', array(
        'default' => 'Recent Online Courses',
        'sanitize_callback' => 'sanitize_text_field',
        'transport' => 'postMessage',
    ));
    $wp_customize->add_control('zajel_courses_title', array(
        'label' => __('Courses Title', 'zajel'),
        'section' => 'zajel_courses_section',
        'type' => 'text',
    ));

    // Courses Description
    $wp_customize->add_setting('zajel_courses_description', array(
        'default' => 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.',
        'sanitize_callback' => 'sanitize_textarea_field',
        'transport' => 'postMessage',
    ));
    $wp_customize->add_control('zajel_courses_description', array(
        'label' => __('Courses Description', 'zajel'),
        'section' => 'zajel_courses_section',
        'type' => 'textarea',
    ));

    // Courses Button Text
    $wp_customize->add_setting('zajel_courses_button_text', array(
        'default' => 'Browsing All Courses',
        'sanitize_callback' => 'sanitize_text_field',
        'transport' => 'postMessage',
    ));
    $wp_customize->add_control('zajel_courses_button_text', array(
        'label' => __('Courses Button Text', 'zajel'),
        'section' => 'zajel_courses_section',
        'type' => 'text',
    ));

    // Teachers Section
    $wp_customize->add_section('zajel_teachers_section', array(
        'title' => __('Teachers Section', 'zajel'),
        'priority' => 35,
    ));
    $wp_customize->add_setting('zajel_teachers_subtitle', array(
        'default' => 'Teachers',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_teachers_subtitle', array(
        'label' => __('Teachers Subtitle', 'zajel'),
        'section' => 'zajel_teachers_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_teachers_title', array(
        'default' => 'Our Experienced Advisors',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_teachers_title', array(
        'label' => __('Teachers Title', 'zajel'),
        'section' => 'zajel_teachers_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_teachers_description', array(
        'default' => 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    $wp_customize->add_control('zajel_teachers_description', array(
        'label' => __('Teachers Description', 'zajel'),
        'section' => 'zajel_teachers_section',
        'type' => 'textarea',
    ));

    // Testimonials Section
    $wp_customize->add_section('zajel_testimonials_section', array(
        'title' => __('Testimonials Section', 'zajel'),
        'priority' => 36,
    ));
    $wp_customize->add_setting('zajel_testimonials_subtitle', array(
        'default' => 'Testimonials',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_testimonials_subtitle', array(
        'label' => __('Testimonials Subtitle', 'zajel'),
        'section' => 'zajel_testimonials_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_testimonials_title', array(
        'default' => 'What Our Students Say',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_testimonials_title', array(
        'label' => __('Testimonials Title', 'zajel'),
        'section' => 'zajel_testimonials_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_testimonials_description', array(
        'default' => 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    $wp_customize->add_control('zajel_testimonials_description', array(
        'label' => __('Testimonials Description', 'zajel'),
        'section' => 'zajel_testimonials_section',
        'type' => 'textarea',
    ));

    // Enroll Section
    $wp_customize->add_section('zajel_enroll_section', array(
        'title' => __('Enroll Section', 'zajel'),
        'priority' => 37,
    ));
    $wp_customize->add_setting('zajel_enroll_title', array(
        'default' => 'Enroll Now',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_enroll_title', array(
        'label' => __('Enroll Title', 'zajel'),
        'section' => 'zajel_enroll_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_enroll_image', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'zajel_enroll_image', array(
        'label' => __('Enroll Image', 'zajel'),
        'section' => 'zajel_enroll_section',
        'settings' => 'zajel_enroll_image',
    )));
    $wp_customize->add_setting('zajel_enroll_form_shortcode', array(
        'default' => '[contact-form-7 id="123" title="Enroll Form"]',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_enroll_form_shortcode', array(
        'label' => __('Enroll Form Shortcode', 'zajel'),
        'section' => 'zajel_enroll_section',
        'type' => 'text',
    ));

    // Newsletter Section
    $wp_customize->add_section('zajel_newsletter_section', array(
        'title' => __('Newsletter Section', 'zajel'),
        'priority' => 38,
    ));
    $wp_customize->add_setting('zajel_newsletter_title', array(
        'default' => 'Get our latest updates',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_newsletter_title', array(
        'label' => __('Newsletter Title', 'zajel'),
        'section' => 'zajel_newsletter_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_newsletter_description', array(
        'default' => 'Subscribe to us to always stay in touch with us and get the latest news about our company and all of our activities!',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    $wp_customize->add_control('zajel_newsletter_description', array(
        'label' => __('Newsletter Description', 'zajel'),
        'section' => 'zajel_newsletter_section',
        'type' => 'textarea',
    ));
    $wp_customize->add_setting('zajel_newsletter_form_shortcode', array(
        'default' => '[contact-form-7 id="124" title="Newsletter Form"]',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_newsletter_form_shortcode', array(
        'label' => __('Newsletter Form Shortcode', 'zajel'),
        'section' => 'zajel_newsletter_section',
        'type' => 'text',
    ));

    // Call to Action Section
    $wp_customize->add_section('zajel_cta_section', array(
        'title' => __('Call to Action Section', 'zajel'),
        'priority' => 39,
    ));
    $wp_customize->add_setting('zajel_cta_title', array(
        'default' => 'Ready to dive in? <br>Start your free trial today.',
        'sanitize_callback' => 'wp_kses_post',
    ));
    $wp_customize->add_control('zajel_cta_title', array(
        'label' => __('CTA Title', 'zajel'),
        'section' => 'zajel_cta_section',
        'type' => 'textarea',
    ));
    $wp_customize->add_setting('zajel_cta_button_text', array(
        'default' => 'Get Started for Free',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_cta_button_text', array(
        'label' => __('CTA Button Text', 'zajel'),
        'section' => 'zajel_cta_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_cta_button_url', array(
        'default' => '#',
        'sanitize_callback' => 'esc_url_raw',
    ));
    $wp_customize->add_control('zajel_cta_button_url', array(
        'label' => __('CTA Button URL', 'zajel'),
        'section' => 'zajel_cta_section',
        'type' => 'url',
    ));

    // News Section
    $wp_customize->add_section('zajel_news_section', array(
        'title' => __('News Section', 'zajel'),
        'priority' => 40,
    ));
    $wp_customize->add_setting('zajel_news_subtitle', array(
        'default' => 'Blogs',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_news_subtitle', array(
        'label' => __('News Subtitle', 'zajel'),
        'section' => 'zajel_news_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_news_title', array(
        'default' => 'Latest News & Blog',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('zajel_news_title', array(
        'label' => __('News Title', 'zajel'),
        'section' => 'zajel_news_section',
        'type' => 'text',
    ));
    $wp_customize->add_setting('zajel_news_description', array(
        'default' => 'There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form.',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    $wp_customize->add_control('zajel_news_description', array(
        'label' => __('News Description', 'zajel'),
        'section' => 'zajel_news_section',
        'type' => 'textarea',
    ));

    // Clients Section
    $wp_customize->add_section('zajel_clients_section', array(
        'title' => __('Clients Section', 'zajel'),
        'priority' => 41,
    ));
    for ($i = 1; $i <= 6; $i++) {
        $wp_customize->add_setting('zajel_client_' . $i . '_image', array(
            'default' => '',
            'sanitize_callback' => 'esc_url_raw',
        ));
        $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'zajel_client_' . $i . '_image', array(
            'label' => sprintf(__('Client %d Image', 'zajel'), $i),
            'section' => 'zajel_clients_section',
            'settings' => 'zajel_client_' . $i . '_image',
        )));
    }
}
add_action('customize_register', 'zajel_front_page_customizer');
function zajel_add_yoast_seo_support() {
    add_post_type_support('course', 'yoast-seo');
}
add_action('init', 'zajel_add_yoast_seo_support');

// Add custom meta boxes for teachers
function zajel_add_teacher_meta_boxes() {
    add_meta_box(
        'zajel_teacher_details',
        __('Teacher Details', 'zajel'),
        'zajel_teacher_details_callback',
        'teacher',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'zajel_add_teacher_meta_boxes');

// Add custom meta boxes for courses
function zajel_add_course_meta_boxes() {
    add_meta_box(
        'zajel_course_details',
        __('Course Details', 'zajel'),
        'zajel_course_details_callback',
        'course',
        'normal',
        'high'
    );

    add_meta_box(
        'zajel_course_curriculum_description',
        __('Course Curriculum Description', 'zajel'),
        'zajel_course_curriculum_description_callback',
        'course',
        'normal',
        'high'
    );
}

// Callback function to display the curriculum description meta box content
function zajel_course_curriculum_description_callback($post) {
    // Add a nonce field for security
    wp_nonce_field('zajel_save_course_curriculum_description', 'zajel_course_curriculum_description_nonce');

    // Get the current value
    $curriculum_description = get_post_meta($post->ID, 'course_curriculum_description', true);

    // Output the editor
    echo '<p>' . __('Enter a description for the course curriculum section. This will be displayed at the top of the curriculum tab.', 'zajel') . '</p>';
    wp_editor($curriculum_description, 'course_curriculum_description', array(
        'textarea_name' => 'course_curriculum_description',
        'textarea_rows' => 10,
        'media_buttons' => true,
        'teeny'         => false,
        'quicktags'     => true,
    ));
}
add_action('add_meta_boxes', 'zajel_add_course_meta_boxes');

// Callback function for teacher details meta box
function zajel_teacher_details_callback($post) {
    // Add nonce for security
    wp_nonce_field('zajel_teacher_meta_box', 'zajel_teacher_meta_box_nonce');

    // Get current values
    $designation = get_post_meta($post->ID, 'teacher_designation', true);
    $experience = get_post_meta($post->ID, 'teacher_experience', true);
    $courses = get_post_meta($post->ID, 'teacher_courses', true);

    // Output fields
    ?>
    <style>
        .zajel-meta-field {
            margin-bottom: 15px;
        }
        .zajel-meta-field label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .zajel-meta-field input[type="text"],
        .zajel-meta-field input[type="number"],
        .zajel-meta-field textarea {
            width: 100%;
            max-width: 500px;
        }
        .zajel-meta-field .description {
            display: block;
            font-style: italic;
            color: #777;
            margin-top: 3px;
        }
    </style>

    <div class="zajel-meta-field">
        <label for="teacher_designation"><?php _e('Designation/Role:', 'zajel'); ?></label>
        <input type="text" id="teacher_designation" name="teacher_designation" value="<?php echo esc_attr($designation); ?>" placeholder="e.g. Arabic Teacher, Quran Instructor">
        <span class="description"><?php _e('The teacher\'s role or position', 'zajel'); ?></span>
    </div>

    <div class="zajel-meta-field">
        <label for="teacher_experience"><?php _e('Years of Experience:', 'zajel'); ?></label>
        <input type="number" id="teacher_experience" name="teacher_experience" value="<?php echo esc_attr($experience); ?>" min="1" max="50">
        <span class="description"><?php _e('Number of years of teaching experience (will show as "X+ Years")', 'zajel'); ?></span>
    </div>

    <div class="zajel-meta-field">
        <label for="teacher_courses"><?php _e('Courses/Skills:', 'zajel'); ?></label>
        <textarea id="teacher_courses" name="teacher_courses" rows="3"><?php echo esc_textarea($courses); ?></textarea>
        <span class="description"><?php _e('Enter courses or skills separated by commas (e.g. "Arabic, Tajweed, Quran, Islamic Studies")', 'zajel'); ?></span>
    </div>

    <div class="zajel-meta-field">
        <label for="teacher_summary"><?php _e('Teacher Summary:', 'zajel'); ?></label>
        <textarea id="teacher_summary" name="teacher_summary" rows="4"><?php echo esc_textarea(get_post_meta($post->ID, 'teacher_summary', true)); ?></textarea>
        <span class="description"><?php _e('A short summary about the teacher. This will appear in the About Us page.', 'zajel'); ?></span>
        <span class="description" style="color: #d63638;"><?php _e('Note: You can also use the Excerpt field below the main editor for this purpose.', 'zajel'); ?></span>
    </div>
    <?php
}

// Save teacher meta box data
function zajel_save_teacher_meta_box_data($post_id) {
    // Check if our nonce is set
    if (!isset($_POST['zajel_teacher_meta_box_nonce'])) {
        return;
    }

    // Verify that the nonce is valid
    if (!wp_verify_nonce($_POST['zajel_teacher_meta_box_nonce'], 'zajel_teacher_meta_box')) {
        return;
    }

    // If this is an autosave, we don't want to do anything
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Check the user's permissions
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Save designation
    if (isset($_POST['teacher_designation'])) {
        update_post_meta($post_id, 'teacher_designation', sanitize_text_field($_POST['teacher_designation']));
    }

    // Save experience
    if (isset($_POST['teacher_experience'])) {
        update_post_meta($post_id, 'teacher_experience', absint($_POST['teacher_experience']));
    }

    // Save courses
    if (isset($_POST['teacher_courses'])) {
        update_post_meta($post_id, 'teacher_courses', sanitize_textarea_field($_POST['teacher_courses']));
    }

    // Save summary
    if (isset($_POST['teacher_summary'])) {
        update_post_meta($post_id, 'teacher_summary', sanitize_textarea_field($_POST['teacher_summary']));
    }
}
add_action('save_post_teacher', 'zajel_save_teacher_meta_box_data');

// Callback function for course details meta box
function zajel_course_details_callback($post) {
    // Add nonce for security
    wp_nonce_field('zajel_course_meta_box', 'zajel_course_meta_box_nonce');

    // Get current values
    $duration = get_post_meta($post->ID, 'course_duration', true);
    $level = get_post_meta($post->ID, 'course_level', true);
    $students = get_post_meta($post->ID, 'course_students', true);
    $language = get_post_meta($post->ID, 'course_language', true);
    $certificate = get_post_meta($post->ID, 'course_certificate', true);

    // CSS for meta box
    ?>
    <style>
        .zajel-meta-field {
            margin-bottom: 15px;
        }
        .zajel-meta-field label {
            display: block;
            font-weight: 600;
            margin-bottom: 5px;
        }
        .zajel-meta-field input[type="text"],
        .zajel-meta-field input[type="number"],
        .zajel-meta-field select {
            width: 100%;
            padding: 8px;
        }
        .zajel-meta-field .description {
            display: block;
            color: #666;
            font-style: italic;
            margin-top: 5px;
            font-size: 13px;
        }
    </style>

    <p><?php _e('Please use the Featured Image section to add an image for this course.', 'zajel'); ?></p>

    <div class="zajel-meta-field">
        <label for="course_duration"><?php _e('Duration:', 'zajel'); ?></label>
        <input type="text" id="course_duration" name="course_duration" value="<?php echo esc_attr($duration); ?>" placeholder="e.g. 8 weeks, 3 months">
        <span class="description"><?php _e('The duration of the course', 'zajel'); ?></span>
    </div>

    <div class="zajel-meta-field">
        <label for="course_level"><?php _e('Level:', 'zajel'); ?></label>
        <select id="course_level" name="course_level">
            <option value="Beginner" <?php selected($level, 'Beginner'); ?>><?php _e('Beginner', 'zajel'); ?></option>
            <option value="Intermediate" <?php selected($level, 'Intermediate'); ?>><?php _e('Intermediate', 'zajel'); ?></option>
            <option value="Advanced" <?php selected($level, 'Advanced'); ?>><?php _e('Advanced', 'zajel'); ?></option>
            <option value="All Levels" <?php selected($level, 'All Levels'); ?>><?php _e('All Levels', 'zajel'); ?></option>
        </select>
        <span class="description"><?php _e('The difficulty level of the course', 'zajel'); ?></span>
    </div>

    <div class="zajel-meta-field">
        <label for="course_students"><?php _e('Students:', 'zajel'); ?></label>
        <input type="number" id="course_students" name="course_students" value="<?php echo esc_attr($students); ?>" min="0">
        <span class="description"><?php _e('Number of students enrolled in this course', 'zajel'); ?></span>
    </div>

    <div class="zajel-meta-field">
        <label for="course_language"><?php _e('Language:', 'zajel'); ?></label>
        <input type="text" id="course_language" name="course_language" value="<?php echo esc_attr($language); ?>" placeholder="e.g. Arabic, English">
        <span class="description"><?php _e('The language of instruction', 'zajel'); ?></span>
    </div>

    <div class="zajel-meta-field">
        <label for="course_certificate"><?php _e('Certificate:', 'zajel'); ?></label>
        <select id="course_certificate" name="course_certificate">
            <option value="Yes" <?php selected($certificate, 'Yes'); ?>><?php _e('Yes', 'zajel'); ?></option>
            <option value="No" <?php selected($certificate, 'No'); ?>><?php _e('No', 'zajel'); ?></option>
        </select>
        <span class="description"><?php _e('Whether a certificate is provided upon completion', 'zajel'); ?></span>
    </div>

    <div class="zajel-meta-field">
        <label for="course_info_title"><?php _e('Course Information Title:', 'zajel'); ?></label>
        <input type="text" id="course_info_title" name="course_info_title" value="<?php echo esc_attr(get_post_meta($post->ID, 'course_info_title', true) ?: 'Course Information'); ?>" placeholder="Course Information">
        <span class="description"><?php _e('The title for the Course Information section in the sidebar', 'zajel'); ?></span>
    </div>

    <div class="zajel-meta-field">
        <label for="course_teacher_id"><?php _e('Course Instructor:', 'zajel'); ?></label>
        <select id="course_teacher_id" name="course_teacher_id">
            <option value=""><?php _e('-- Select an Instructor --', 'zajel'); ?></option>
            <?php
            // Get all teachers
            $teachers = get_posts(array(
                'post_type' => 'teacher',
                'posts_per_page' => -1,
                'orderby' => 'title',
                'order' => 'ASC'
            ));

            // Get current teacher ID
            $current_teacher_id = get_post_meta($post->ID, 'course_teacher_id', true);

            // Display options
            foreach ($teachers as $teacher) {
                printf(
                    '<option value="%s" %s>%s</option>',
                    esc_attr($teacher->ID),
                    selected($current_teacher_id, $teacher->ID, false),
                    esc_html($teacher->post_title)
                );
            }
            ?>
        </select>
        <span class="description"><?php _e('Select the instructor for this course', 'zajel'); ?></span>
    </div>
    <?php
}

// Save course meta box data
function zajel_save_course_meta_box_data($post_id) {
    // Check if our nonce is set
    if (!isset($_POST['zajel_course_meta_box_nonce'])) {
        return;
    }

    // Verify that the nonce is valid
    if (!wp_verify_nonce($_POST['zajel_course_meta_box_nonce'], 'zajel_course_meta_box')) {
        return;
    }

    // If this is an autosave, we don't want to do anything
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Check the user's permissions
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Save course details
    if (isset($_POST['course_duration'])) {
        update_post_meta($post_id, 'course_duration', sanitize_text_field($_POST['course_duration']));
    }

    if (isset($_POST['course_level'])) {
        update_post_meta($post_id, 'course_level', sanitize_text_field($_POST['course_level']));
    }

    if (isset($_POST['course_students'])) {
        update_post_meta($post_id, 'course_students', absint($_POST['course_students']));
    }

    if (isset($_POST['course_language'])) {
        update_post_meta($post_id, 'course_language', sanitize_text_field($_POST['course_language']));
    }

    if (isset($_POST['course_certificate'])) {
        update_post_meta($post_id, 'course_certificate', sanitize_text_field($_POST['course_certificate']));
    }

    if (isset($_POST['course_info_title'])) {
        update_post_meta($post_id, 'course_info_title', sanitize_text_field($_POST['course_info_title']));
    }

    if (isset($_POST['course_teacher_id'])) {
        update_post_meta($post_id, 'course_teacher_id', absint($_POST['course_teacher_id']));
    }
}
add_action('save_post_course', 'zajel_save_course_meta_box_data');

// Save course curriculum description
function zajel_save_course_curriculum_description($post_id) {
    // Check if our nonce is set and verify it
    if (!isset($_POST['zajel_course_curriculum_description_nonce']) ||
        !wp_verify_nonce($_POST['zajel_course_curriculum_description_nonce'], 'zajel_save_course_curriculum_description')) {
        return;
    }

    // Check if this is an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Check the user's permissions
    if (isset($_POST['post_type']) && 'course' == $_POST['post_type']) {
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
    }

    // Save the curriculum description
    if (isset($_POST['course_curriculum_description'])) {
        update_post_meta(
            $post_id,
            'course_curriculum_description',
            wp_kses_post($_POST['course_curriculum_description'])
        );
    }
}
add_action('save_post', 'zajel_save_course_curriculum_description');




/**
 * Handle the /blog/ URL
 */
function zajel_handle_blog_url() {
    // Check if the current URL is /blog/
    if (isset($_SERVER['REQUEST_URI']) && $_SERVER['REQUEST_URI'] === '/blog/') {
        // Load the blog template
        include(get_template_directory() . '/page-blog.php');
        exit;
    }
}
add_action('template_redirect', 'zajel_handle_blog_url');

// ===== نظام الأمان المتكامل للفورمز =====

// إنشاء جدول Trial Class Bookings
function zajel_create_trial_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'trial_bookings';
    $charset_collate = $wpdb->get_charset_collate();

    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            full_name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            course VARCHAR(100) NOT NULL,
            additional_notes TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        update_option('zajel_trial_table_version', '1.0');
    }
}
add_action('init', 'zajel_create_trial_table');

// إضافة صفحة Trial Bookings في الداشبورد
function zajel_trial_bookings_menu() {
    add_menu_page(
        'Trial Bookings',
        'Trial Bookings',
        'manage_options',
        'zajel-trial-bookings',
        'zajel_trial_bookings_page',
        'dashicons-calendar-alt',
        21
    );
}
add_action('admin_menu', 'zajel_trial_bookings_menu');

// صفحة عرض Trial Bookings
function zajel_trial_bookings_page() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'trial_bookings';
    $bookings = $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC");
    ?>
    <div class="wrap">
        <h1>Trial Class Bookings</h1>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Full Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Course</th>
                    <th>Additional Notes</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($bookings) : ?>
                    <?php foreach ($bookings as $booking) : ?>
                        <tr>
                            <td><?php echo esc_html($booking->id); ?></td>
                            <td><?php echo esc_html($booking->full_name); ?></td>
                            <td><?php echo esc_html($booking->email); ?></td>
                            <td><?php echo esc_html($booking->phone); ?></td>
                            <td><?php echo esc_html($booking->course); ?></td>
                            <td><?php echo esc_html(wp_trim_words($booking->additional_notes, 10)); ?></td>
                            <td><?php echo esc_html($booking->created_at); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php else : ?>
                    <tr>
                        <td colspan="7">No trial bookings found.</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    <?php
}

// دالة التحقق من الأمان العامة (مبسطة)
function zajel_validate_form_security($action, $nonce_field) {
    error_log("Security validation started for action: $action, nonce_field: $nonce_field");

    // التحقق من nonce
    if (!isset($_POST[$nonce_field])) {
        error_log("Nonce field not found: $nonce_field");
        return false;
    }

    if (!wp_verify_nonce($_POST[$nonce_field], $action)) {
        error_log("Nonce verification failed for action: $action");
        return false;
    }

    // التحقق من Honeypot
    if (!empty($_POST['honeypot'])) {
        error_log("Honeypot triggered");
        return false;
    }

    error_log("Security validation passed");
    return true;
}

// معالجة Contact Form
function zajel_handle_contact_form() {
    error_log("Contact form handler called");

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        error_log("Not a POST request");
        return null;
    }

    error_log("POST request detected, validating security");

    // التحقق من الأمان
    if (!zajel_validate_form_security('contact_form_action', 'contact_nonce')) {
        error_log("Security validation failed");
        return 'security_error';
    }

    error_log("Security validation passed");

    // التحقق من الحقول المطلوبة
    if (empty($_POST['name']) || empty($_POST['email']) || empty($_POST['message'])) {
        return 'missing_fields';
    }

    // تنظيف البيانات
    $name = sanitize_text_field($_POST['name']);
    $subject = sanitize_text_field($_POST['subject']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $message = sanitize_textarea_field($_POST['message']);

    // التحقق من صحة البيانات
    if (!is_email($email)) {
        return 'invalid_email';
    }

    if (strlen($name) > 100 || strlen($subject) > 200 || strlen($message) > 1000) {
        return 'field_too_long';
    }

    // حفظ في قاعدة البيانات
    global $wpdb;
    $table_name = $wpdb->prefix . 'contact';

    $result = $wpdb->insert(
        $table_name,
        array(
            'name' => $name,
            'subject' => $subject,
            'email' => $email,
            'phone' => $phone,
            'message' => $message,
            'created_at' => current_time('mysql'),
        ),
        array('%s', '%s', '%s', '%s', '%s', '%s')
    );

    return $result !== false ? 'success' : 'database_error';
}

// معالجة Trial Class Form
function zajel_handle_trial_form() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        return null;
    }

    // التحقق من الأمان
    if (!zajel_validate_form_security('trial_form_action', 'trial_form_nonce')) {
        return 'security_error';
    }

    // التحقق من الحقول المطلوبة
    if (empty($_POST['full_name']) || empty($_POST['email']) || empty($_POST['phone']) || empty($_POST['course'])) {
        return 'missing_fields';
    }

    // تنظيف البيانات
    $full_name = sanitize_text_field($_POST['full_name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $course = sanitize_text_field($_POST['course']);
    $additional_notes = sanitize_textarea_field($_POST['additional_notes']);

    // التحقق من صحة البيانات
    if (!is_email($email)) {
        return 'invalid_email';
    }

    if (strlen($full_name) > 100 || strlen($phone) > 20 || strlen($additional_notes) > 1000) {
        return 'field_too_long';
    }

    // التحقق من صحة الكورس
    $valid_courses = array('quran', 'tajweed', 'arabic', 'islamic');
    if (!in_array($course, $valid_courses)) {
        return 'invalid_course';
    }

    // حفظ في قاعدة البيانات
    global $wpdb;
    $table_name = $wpdb->prefix . 'trial_bookings';

    $result = $wpdb->insert(
        $table_name,
        array(
            'full_name' => $full_name,
            'email' => $email,
            'phone' => $phone,
            'course' => $course,
            'additional_notes' => $additional_notes,
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'created_at' => current_time('mysql'),
        ),
        array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
    );

    return $result !== false ? 'success' : 'database_error';
}
?>