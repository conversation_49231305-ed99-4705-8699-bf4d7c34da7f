<?php
// ملف لعرض الـ debug logs
echo "<h1>Debug Logs</h1>";

// مسارات محتملة للـ error logs
$log_paths = array(
    ABSPATH . 'wp-content/debug.log',
    ABSPATH . 'debug.log',
    ABSPATH . 'error_log',
    ABSPATH . 'wp-content/error_log',
    '/tmp/error_log',
    ini_get('error_log')
);

echo "<h2>Checking Log Paths:</h2>";
foreach ($log_paths as $path) {
    if ($path && file_exists($path)) {
        echo "<p>✅ Found: $path</p>";
        
        // قراءة آخر 50 سطر من الـ log
        $lines = file($path);
        $recent_lines = array_slice($lines, -50);
        
        echo "<h3>Last 50 lines from: $path</h3>";
        echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 400px; overflow-y: scroll;'>";
        foreach ($recent_lines as $line) {
            // تمييز الـ logs الخاصة بالفورمز
            if (strpos($line, 'Contact Form') !== false || strpos($line, 'Trial Form') !== false) {
                echo "<strong style='color: red;'>$line</strong>";
            } else {
                echo htmlspecialchars($line);
            }
        }
        echo "</pre>";
        break;
    } else {
        echo "<p>❌ Not found: $path</p>";
    }
}

// معلومات إضافية
echo "<h2>Server Info:</h2>";
echo "<p>PHP Error Log: " . ini_get('error_log') . "</p>";
echo "<p>Log Errors: " . (ini_get('log_errors') ? 'ON' : 'OFF') . "</p>";
echo "<p>Display Errors: " . (ini_get('display_errors') ? 'ON' : 'OFF') . "</p>";
echo "<p>WordPress Debug: " . (defined('WP_DEBUG') && WP_DEBUG ? 'ON' : 'OFF') . "</p>";
echo "<p>WordPress Debug Log: " . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'ON' : 'OFF') . "</p>";

// اختبار كتابة الـ log
error_log("=== DEBUG TEST LOG ENTRY ===");
echo "<p>✅ Test log entry written</p>";

// عرض معلومات الطلب الحالي
echo "<h2>Current Request Info:</h2>";
echo "<p>Request Method: " . $_SERVER['REQUEST_METHOD'] . "</p>";
echo "<p>Request URI: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>POST Data: " . (empty($_POST) ? 'None' : 'Present') . "</p>";

if (!empty($_POST)) {
    echo "<h3>POST Data:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
}

// تحقق من وجود الجداول
echo "<h2>Database Tables Check:</h2>";
global $wpdb;

$contact_table = $wpdb->prefix . 'contact';
$trial_table = $wpdb->prefix . 'trial_bookings';

if ($wpdb->get_var("SHOW TABLES LIKE '$contact_table'") == $contact_table) {
    echo "<p>✅ Contact table exists: $contact_table</p>";
    $count = $wpdb->get_var("SELECT COUNT(*) FROM $contact_table");
    echo "<p>Records in contact table: $count</p>";
} else {
    echo "<p>❌ Contact table missing: $contact_table</p>";
}

if ($wpdb->get_var("SHOW TABLES LIKE '$trial_table'") == $trial_table) {
    echo "<p>✅ Trial table exists: $trial_table</p>";
    $count = $wpdb->get_var("SELECT COUNT(*) FROM $trial_table");
    echo "<p>Records in trial table: $count</p>";
} else {
    echo "<p>❌ Trial table missing: $trial_table</p>";
}

// تحقق من الدوال
echo "<h2>Functions Check:</h2>";
$functions = array('wp_verify_nonce', 'sanitize_text_field', 'sanitize_email', 'is_email', 'current_time');
foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "<p>✅ Function exists: $func</p>";
    } else {
        echo "<p>❌ Function missing: $func</p>";
    }
}
?>
