/*======================================
    Servicess CSS
========================================*/
.services {
    background-color: $gray;

    .section-title {
        margin-bottom: 40px;
    }

    .single-service {
        position: relative;
        text-align: center;
        padding: 50px;
        box-shadow: 0px 0px 20px rgba(224, 224, 224, 0.23);
        border-radius: 0;
        border: 1px solid #eee;
        background-color: $white;
        overflow: hidden;
        margin-top: 30px;
        transition: all 0.4s ease;

        &::before {
            position: absolute;
            left: 0;
            bottom: 0;
            height: 4px;
            width: 0%;
            content: "";
            background-color: $theme-color;
            transition: all 0.4s ease-in-out;
        }

        &:hover {
            box-shadow: 0px 10px 50px rgba(116, 116, 116, 0.23);
        }

        &:hover::before {
            width: 100%;
        }


        .icon {
            font-size: 30px;
            height: 70px;
            width: 70px;
            line-height: 70px;
            text-align: center;
            display: inline-block;
            color: $black;
            border: 1px solid #eee;
            font-weight: 700;
            margin-bottom: 25px;
            border-radius: 50%;
        }

        &:hover {
            .icon {
                border-color: transparent;
                color: $white;
                background-color: $theme-color;
            }
        }

        h3 {
            margin-bottom: 20px;
            line-height: 30px;

            a {
                font-size: 22px;
                font-weight: 700;

                &:hover {
                    color: $theme-color;
                }
            }
        }

        p {
            font-weight: 400;
            font-size: 14px;
            color: #888;
        }

        .button {
            margin-top: 30px;
        }
    }
}