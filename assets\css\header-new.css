/* New Header CSS for Zajel Arabic Theme */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
}

/* Header Styles */
.header.navbar-area {
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    position: relative;
}

/* Logo Styles */
.navbar-brand {
    padding: 0;
    margin: 0;
}

.navbar-brand img {
    max-height: 50px;
    width: auto;
}

.default-logo {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--gold-dark);
}

.default-logo i {
    color: var(--gold-dark);
    margin-right: 10px;
    font-size: 2rem;
}

/* Main Navigation */
.navbar {
    padding: 0;
}

.navbar-nav {
    display: flex;
    align-items: center;
}

.navbar-nav .nav-item {
    margin: 0 15px;
    position: relative;
}

.navbar-nav .nav-item a {
    color: #333;
    font-weight: 600;
    padding: 10px 0;
    position: relative;
    display: block;
    transition: all 0.3s ease;
}

.navbar-nav .nav-item a:hover,
.navbar-nav .nav-item a.active {
    color: var(--gold-dark);
    text-decoration: none;
}

.navbar-nav .nav-item a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    transition: all 0.3s ease;
}

.navbar-nav .nav-item a:hover::after,
.navbar-nav .nav-item a.active::after {
    width: 100%;
}

/* Social Icons */
.header-social {
    display: flex;
    margin-left: 15px;
}

.header-social ul {
    display: flex;
    padding: 0;
    margin: 0;
    list-style: none;
}

.header-social ul li {
    margin-right: 10px;
}

.header-social ul li:last-child {
    margin-right: 0;
}

.header-social ul li a {
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    display: block;
    border-radius: 50%;
    color: #333;
    background-color: #f5f5f5;
    font-size: 16px;
    transition: all 0.3s ease;
}

.header-social ul li a:hover {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    transform: translateY(-3px);
}

/* Trial Class Button */
.trial-class-button {
    margin-left: 20px;
}

.trial-class-btn {
    padding: 10px 20px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    border-radius: 30px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    display: inline-block;
    box-shadow: 0 4px 10px rgba(218, 165, 32, 0.3);
}

.trial-class-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(218, 165, 32, 0.4);
    color: #fff;
}

/* Mobile Menu Button */
.navbar-toggler {
    padding: 0;
    border: none;
    background: transparent;
}

.navbar-toggler:focus {
    box-shadow: none;
    outline: none;
}

.navbar-toggler .toggler-icon {
    width: 30px;
    height: 2px;
    background-color: #333;
    display: block;
    margin: 6px 0;
    position: relative;
    transition: all 0.3s ease;
}

/* Mobile Menu Styles */
@media only screen and (max-width: 991px) {
    .navbar-collapse {
        position: fixed;
        top: 0;
        left: -280px;
        width: 280px;
        height: 100vh;
        background-color: #fff;
        z-index: 9999;
        padding: 20px;
        transition: all 0.3s ease;
        overflow-y: auto;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        display: none !important;
    }

    .navbar-collapse.show {
        left: 0;
        display: block !important;
    }

    /* Mobile Menu Overlay */
    .mobile-menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .mobile-menu-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* Mobile Menu Header */
    .mobile-menu-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .mobile-menu-logo img {
        max-height: 40px;
    }

    .mobile-menu-close {
        width: 30px;
        height: 30px;
        background-color: #f5f5f5;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .mobile-menu-close i {
        font-size: 16px;
    }

    /* Mobile Menu Items */
    .navbar-nav {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 20px;
    }

    .navbar-nav .nav-item {
        margin: 0;
        padding: 0;
        width: 100%;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .navbar-nav .nav-item:last-child {
        border-bottom: none;
    }

    .navbar-nav .nav-item a {
        padding: 12px 0;
        display: block;
        width: 100%;
    }

    .navbar-nav .nav-item a::after {
        display: none;
    }

    /* Mobile Social Icons */
    .mobile-social {
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .mobile-social ul {
        display: flex;
        justify-content: center;
        padding: 0;
        margin: 0;
        list-style: none;
    }

    .mobile-social ul li {
        margin: 0 10px;
    }

    .mobile-social ul li a {
        width: 35px;
        height: 35px;
        line-height: 35px;
        text-align: center;
        display: block;
        border-radius: 50%;
        color: #333;
        background-color: #f5f5f5;
        font-size: 16px;
        transition: all 0.3s ease;
    }

    .mobile-social ul li a:hover {
        background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
        color: #fff;
    }

    /* Mobile Trial Button */
    .mobile-trial {
        margin-top: 20px;
        text-align: center;
    }

    .mobile-trial .trial-class-btn {
        width: 100%;
        display: block;
    }

    /* Hide desktop elements on mobile */
    .header-social,
    .trial-class-button {
        display: none;
    }

    /* Navbar toggler animation */
    .navbar-toggler[aria-expanded="true"] .toggler-icon:nth-child(1) {
        transform: rotate(45deg);
        top: 8px;
    }

    .navbar-toggler[aria-expanded="true"] .toggler-icon:nth-child(2) {
        opacity: 0;
    }

    .navbar-toggler[aria-expanded="true"] .toggler-icon:nth-child(3) {
        transform: rotate(-45deg);
        top: -8px;
    }
}
