<?php
// اختبار النظام الجديد
require_once('../../../../wp-config.php');
require_once('functions.php');

echo "<h1>Testing Zajel Forms System</h1>";

// اختبار إنشاء الجداول
zajel_create_contact_and_trial_tables();
echo "<p>✓ Tables created successfully!</p>";

// اختبار وظائف الحماية
$_POST['trial_form_nonce'] = wp_create_nonce('trial_form_action');
$_POST['full_name'] = 'Test User';
$_POST['email'] = '<EMAIL>';
$_POST['phone'] = '123456789';
$_POST['course'] = 'quran';
$_POST['additional_notes'] = 'Test notes';
$_SERVER['REQUEST_METHOD'] = 'POST';

echo "<h2>Testing Trial Form Security</h2>";
if (zajel_validate_form_security('trial_form_action', 'trial_form_nonce')) {
    echo "<p>✓ Security validation passed!</p>";
} else {
    echo "<p>✗ Security validation failed!</p>";
}

// اختبار حفظ البيانات
$result = zajel_handle_trial_form();
echo "<h2>Testing Trial Form Save</h2>";
echo "<p>Result: " . $result . "</p>";

echo "<h2>System is ready!</h2>";
echo "<p>Contact form: <a href='/contact'>Visit Contact Page</a></p>";
echo "<p>Trial class: <a href='/trial-class'>Visit Trial Class Page</a></p>";
echo "<p>Dashboard: <a href='/wp-admin'>Go to Dashboard</a></p>";
?>