/* Mobile Menu CSS for Zajel Arabic Theme */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --transition-slow: all 0.8s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 15px 60px rgba(0, 0, 0, 0.2);
}

/* Mobile Menu Styles */
@media only screen and (max-width: 991px) {
    /* Mobile Menu Button */
    .mobile-menu-btn {
        display: block;
        background: transparent;
        border: none;
        padding: 0;
        cursor: pointer;
    }

    .mobile-menu-btn:focus {
        outline: none;
    }

    .mobile-menu-btn .toggler-icon {
        width: 30px;
        height: 2px;
        background-color: #333;
        display: block;
        margin: 6px 0;
        position: relative;
        transition: var(--transition-fast);
    }

    /* Mobile Menu Button Animation */
    .navbar-toggler[aria-expanded="true"] .toggler-icon:nth-child(1) {
        transform: rotate(45deg);
        top: 8px;
    }

    .navbar-toggler[aria-expanded="true"] .toggler-icon:nth-child(2) {
        opacity: 0;
    }

    .navbar-toggler[aria-expanded="true"] .toggler-icon:nth-child(3) {
        transform: rotate(-45deg);
        top: -8px;
    }

    /* Mobile Menu Container */
    .navbar-collapse {
        position: fixed;
        top: 0;
        left: -280px;
        width: 280px;
        height: 100%;
        background-color: #fff;
        z-index: 999;
        padding: 20px 0;
        transition: var(--transition-medium);
        overflow-y: auto;
        box-shadow: var(--shadow-medium);
    }

    .navbar-collapse.show {
        left: 0;
    }

    /* Mobile Menu Overlay */
    .mobile-menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        z-index: 998;
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-medium);
    }

    .mobile-menu-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* Mobile Menu Items */
    .navbar-nav {
        flex-direction: column;
        padding: 20px;
        margin-top: 60px;
    }

    .navbar-nav .nav-item {
        margin: 0;
        padding: 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .navbar-nav .nav-item:last-child {
        border-bottom: none;
    }

    .navbar-nav .nav-item a {
        padding: 12px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #333;
        font-weight: 500;
    }

    .navbar-nav .nav-item a:hover,
    .navbar-nav .nav-item a.active {
        color: var(--gold-dark);
    }

    /* Mobile Menu Dropdown */
    .navbar-nav .nav-item .sub-menu {
        position: static;
        width: 100%;
        opacity: 1;
        visibility: visible;
        box-shadow: none;
        background-color: #f5f5f5;
        padding: 0;
        border-radius: 0;
        max-height: 0;
        overflow: hidden;
        transition: var(--transition-medium);
    }

    .navbar-nav .nav-item .sub-menu.show {
        max-height: 500px;
    }

    .navbar-nav .nav-item .sub-menu li a {
        padding: 10px 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 14px;
    }

    .navbar-nav .nav-item .sub-menu li:last-child a {
        border-bottom: none;
    }

    /* Mobile Menu Header */
    .mobile-menu-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        background-color: #fff;
        z-index: 1;
    }

    .mobile-menu-logo {
        max-width: 150px;
    }

    .mobile-menu-logo img {
        max-height: 40px;
        width: auto;
    }

    .mobile-menu-close {
        width: 30px;
        height: 30px;
        background-color: #f5f5f5;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition-fast);
    }

    .mobile-menu-close:hover {
        background-color: #e5e5e5;
    }

    .mobile-menu-close i {
        font-size: 16px;
        color: #333;
    }

    /* Mobile Social Icons */
    .mobile-social {
        padding: 20px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        margin-top: 20px;
    }

    .mobile-social ul {
        display: flex;
        justify-content: center;
        padding: 0;
        margin: 0;
        list-style: none;
    }

    .mobile-social ul li {
        margin: 0 10px;
    }

    .mobile-social ul li a {
        width: 35px;
        height: 35px;
        line-height: 35px;
        text-align: center;
        display: block;
        border-radius: 50%;
        color: #333;
        background-color: #f5f5f5;
        font-size: 16px;
        transition: var(--transition-fast);
    }

    .mobile-social ul li a:hover {
        background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
        color: #fff;
    }

    /* Mobile Trial Class Button */
    .mobile-trial-button {
        padding: 20px;
        text-align: center;
    }

    .mobile-trial-btn {
        padding: 10px 20px;
        background: linear-gradient(45deg, #2980B9, #6BBBFF);
        color: #fff;
        border-radius: 30px;
        font-weight: 600;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: var(--transition-fast);
        display: inline-block;
        width: 100%;
    }

    .mobile-trial-btn:hover {
        color: #fff;
        background: linear-gradient(45deg, #6BBBFF, #2980B9);
        box-shadow: 0 2px 8px rgba(41, 128, 185, 0.3);
    }
}

/* Desktop Menu Styles */
@media only screen and (min-width: 992px) {
    /* Hide Mobile Elements */
    .mobile-menu-header,
    .mobile-social,
    .mobile-trial-button,
    .mobile-menu-overlay {
        display: none;
    }

    /* Desktop Menu Items */
    .navbar-nav {
        display: flex;
        flex-direction: row;
    }

    .navbar-nav .nav-item {
        margin-left: 30px;
        position: relative;
    }

    .navbar-nav .nav-item:first-child {
        margin-left: 0;
    }

    .navbar-nav .nav-item a {
        padding: 30px 0;
        display: block;
        color: #333;
        font-weight: 600;
        position: relative;
    }

    .navbar-nav .nav-item a:hover,
    .navbar-nav .nav-item a.active {
        color: var(--gold-dark);
    }

    .navbar-nav .nav-item a::before {
        content: '';
        position: absolute;
        bottom: 25px;
        left: 0;
        width: 0;
        height: 2px;
        background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
        transition: var(--transition-fast);
    }

    .navbar-nav .nav-item a:hover::before,
    .navbar-nav .nav-item a.active::before {
        width: 100%;
    }

    /* Desktop Dropdown Menu */
    .navbar-nav .nav-item .sub-menu {
        position: absolute;
        top: 100%;
        left: 0;
        width: 220px;
        background-color: #fff;
        box-shadow: var(--shadow-medium);
        padding: 10px 0;
        border-radius: 0 0 5px 5px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(10px);
        transition: var(--transition-medium);
        z-index: 99;
    }

    .navbar-nav .nav-item:hover .sub-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .navbar-nav .nav-item .sub-menu li {
        position: relative;
        padding: 0;
    }

    .navbar-nav .nav-item .sub-menu li a {
        padding: 10px 20px;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        display: block;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .navbar-nav .nav-item .sub-menu li:last-child a {
        border-bottom: none;
    }

    .navbar-nav .nav-item .sub-menu li a::before {
        display: none;
    }

    .navbar-nav .nav-item .sub-menu li a:hover {
        background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
        color: #fff;
        padding-left: 25px;
    }
}
