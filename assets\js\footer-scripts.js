/**
 * <PERSON>er Scripts for Zajel Arabic Theme
 */

(function($) {
    'use strict';
    
    // Scroll to top button functionality
    $(window).on('scroll', function() {
        if ($(this).scrollTop() > 200) {
            $('.scroll-top').addClass('active');
        } else {
            $('.scroll-top').removeClass('active');
        }
    });
    
    $('.scroll-top').on('click', function(e) {
        e.preventDefault();
        $('html, body').animate({
            scrollTop: 0
        }, 800);
        return false;
    });
    
    // WhatsApp button pulse animation
    $('.whatsapp-float').hover(
        function() {
            $(this).css('animation', 'none');
        },
        function() {
            $(this).css('animation', 'pulse 2s infinite');
        }
    );
    
    // Footer links hover effect
    $('.f-link ul li a').hover(
        function() {
            $(this).closest('li').addClass('active');
        },
        function() {
            $(this).closest('li').removeClass('active');
        }
    );
    
    // Newsletter form submission animation
    $('.footer-newsletter form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $input = $form.find('input[type="email"]');
        var $button = $form.find('button');
        
        if ($input.val().trim() !== '') {
            $button.html('<i class="lni lni-spinner lni-spin-effect"></i>');
            
            // Simulate form submission (replace with actual AJAX call)
            setTimeout(function() {
                $form.slideUp();
                $form.after('<div class="newsletter-success">Thank you for subscribing to our newsletter!</div>');
                $('.newsletter-success').hide().slideDown();
            }, 1500);
        }
    });
    
})(jQuery);
