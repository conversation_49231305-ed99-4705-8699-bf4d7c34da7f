/* Courses Slider Styles */
.courses-slider {
    position: relative;
    overflow: hidden;
    padding: 20px 0;
}

/* Asegurarse de que todos los elementos ocupen el ancho completo */
.courses-slider * {
    box-sizing: border-box;
}

.course-image a {
    display: block;
    width: 100%;
    margin: 0;
    padding: 0;
}

.course-slide {
    transition: all 0.3s ease;
    margin-bottom: 30px;
    padding: 0 15px;
    box-sizing: border-box;
}

.single-course {
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    background: #fff;
    width: 100%;
    margin: 0;
    padding: 0;
    max-width: 100%;
}

.single-course:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.course-image {
    position: relative;
    overflow: hidden;
    width: 100%;
    margin: 0;
    padding: 0;
}

.course-image img {
    width: 100%;
    height: 230px;
    object-fit: cover;
    transition: transform 0.3s ease;
    display: block;
    margin: 0;
    padding: 0;
}

.single-course:hover .course-image img {
    transform: scale(1.05);
}

.teacher-badge {
    position: absolute;
    bottom: -20px;
    left: 20px;
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 30px;
    padding: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 2;
}

.teacher-image {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #1a5f8d;
}

.teacher-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.teacher-badge span {
    padding: 0 10px;
    font-size: 12px;
    font-weight: 600;
    color: #1a5f8d;
}

.single-course .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 30px 20px 20px;
    background: #fff;
}

.single-course h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 15px;
    line-height: 1.4;
}

.single-course h3 a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.single-course h3 a:hover {
    color: #1a5f8d;
}

.single-course .excerpt {
    flex: 1;
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.6;
    min-height: 70px;
}

.single-course .button {
    text-align: center;
    margin-top: auto;
}

.single-course .btn {
    background: linear-gradient(135deg, #1a5f8d 0%, #03355c 100%);
    color: #fff;
    padding: 10px 25px;
    border-radius: 30px;
    display: inline-block;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    text-decoration: none;
}

.single-course .btn:hover {
    background: linear-gradient(135deg, #03355c 0%, #1a5f8d 100%);
    transform: translateY(-2px);
}

.courses-navigation {
    margin-top: 20px;
    text-align: center;
}

.courses-nav-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.courses-nav-buttons button {
    cursor: pointer;
    transition: all 0.3s ease;
    background: #1a5f8d;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.courses-nav-buttons button:hover {
    background: #03355c;
}

.courses-nav-buttons button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

/* Responsive styles */
@media (max-width: 991px) {
    .course-slide {
        margin-bottom: 30px;
    }

    .single-course {
        max-width: 400px;
        margin: 0 auto;
    }
}

@media (max-width: 767px) {
    .course-slide {
        margin-bottom: 20px;
        padding: 0 10px;
    }

    .single-course .content {
        padding: 25px 15px 15px;
    }

    .single-course h3 {
        font-size: 16px;
    }

    .single-course .excerpt {
        font-size: 13px;
        min-height: 60px;
    }

    .single-course .btn {
        padding: 8px 20px;
        font-size: 14px;
    }
}
