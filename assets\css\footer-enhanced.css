/* Enhanced <PERSON>er Styles for Zajel Arabic Theme */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --transition-slow: all 0.8s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 15px 60px rgba(0, 0, 0, 0.2);
}

/* Footer Styles */
.footer {
    background-color: #1a1a1a;
    color: #fff;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/pattern-dark.png');
    background-size: 300px;
    opacity: 0.05;
    z-index: 0;
}

/* Footer Middle */
.footer-middle {
    padding: 80px 0 50px;
    position: relative;
    z-index: 1;
}

/* Single Footer */
.single-footer {
    margin-bottom: 30px;
}

.single-footer h3 {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 15px;
    background: linear-gradient(to right, var(--gold-light), var(--gold-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    display: inline-block;
}

.single-footer h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    border-radius: 2px;
}

/* About Section */
.f-about p {
    margin-bottom: 25px;
    color: #ffffff;
    line-height: 1.8;
}

.f-about .logo {
    margin-bottom: 20px;
}

.f-about .logo img {
    max-width: 160px;
    height: auto;
}

.f-about .default-logo {
    font-size: 24px;
    font-weight: 700;
    color: #fff;
    display: flex;
    align-items: center;
}

.f-about .default-logo i {
    font-size: 28px;
    margin-right: 10px;
    color: var(--gold-light);
}

/* Footer Social */
.footer-social {
    margin-top: 25px;
}

.footer-social ul {
    display: flex;
    padding: 0;
    margin: 0;
    list-style: none;
}

.footer-social ul li {
    margin-right: 15px;
}

.footer-social ul li:last-child {
    margin-right: 0;
}

.footer-social ul li a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 18px;
    transition: var(--transition-fast);
}

.footer-social ul li a:hover {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    transform: translateY(-5px);
}

/* Footer Links */
.f-link ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

.f-link ul li {
    margin-bottom: 12px;
    position: relative;
    padding-left: 22px;
}

.f-link ul li:last-child {
    margin-bottom: 0;
}

.f-link ul li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 8px;
    width: 8px;
    height: 8px;
    background: var(--gold-medium);
    border-radius: 50%;
    transition: var(--transition-fast);
}

.f-link ul li:hover::before {
    background: var(--gold-light);
    transform: scale(1.5);
}

.f-link ul li a {
    color: #ffffff;
    font-size: 15px;
    font-weight: 400;
    transition: var(--transition-fast);
    position: relative;
    display: inline-block;
}

.f-link ul li:hover a {
    color: var(--gold-light);
    padding-left: 5px;
}

.f-link ul li a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    transition: var(--transition-fast);
}

.f-link ul li:hover a::after {
    width: 100%;
}

/* Newsletter */
.footer-newsletter p {
    color: #ffffff;
    margin-bottom: 20px;
    line-height: 1.8;
}

.footer-newsletter form {
    position: relative;
    margin-top: 25px;
}

.footer-newsletter input {
    width: 100%;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 0 20px;
    color: #fff;
    font-size: 14px;
    transition: var(--transition-fast);
}

.footer-newsletter input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--gold-medium);
    outline: none;
}

.footer-newsletter input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.footer-newsletter button {
    position: absolute;
    right: 0;
    top: 0;
    height: 50px;
    width: 50px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    border: none;
    border-radius: 0 8px 8px 0;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    transition: var(--transition-fast);
    overflow: hidden;
}

.footer-newsletter button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
}

.footer-newsletter button:hover::before {
    left: 100%;
    transition: 0.7s;
}

.footer-newsletter button:hover {
    background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
    box-shadow: 0 0 10px rgba(3, 53, 92, 0.5);
}

/* Footer Bottom */
.footer-bottom {
    padding: 25px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 1;
    background: linear-gradient(to right, rgba(3, 53, 92, 0.5), rgba(26, 95, 141, 0.5));
}

.footer-bottom .left p {
    color: #ffffff;
    margin: 0;
    font-size: 14px;
    text-align: center;
}

.footer-bottom .left p .lni-heart-filled {
    color: var(--gold-light);
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.3); }
    28% { transform: scale(1); }
    42% { transform: scale(1.3); }
    70% { transform: scale(1); }
}

/* WhatsApp Floating Button */
.whatsapp-float {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 30px;
    box-shadow: 0 5px 15px rgba(3, 53, 92, 0.3);
    z-index: 999;
    transition: var(--transition-fast);
    animation: pulse 2s infinite;
}

.whatsapp-float:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(3, 53, 92, 0.5);
    background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(3, 53, 92, 0.7);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(3, 53, 92, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(3, 53, 92, 0);
    }
}

/* Scroll Top Button */
.scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-fast);
}

.scroll-top.active {
    opacity: 1;
    visibility: visible;
}

.scroll-top:hover {
    background: linear-gradient(45deg, var(--gold-light), var(--gold-dark));
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(3, 53, 92, 0.5);
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .footer-middle {
        padding: 60px 0 30px;
    }

    .single-footer h3 {
        font-size: 20px;
        margin-bottom: 20px;
    }

    .f-about .logo img {
        max-width: 140px;
    }

    .footer-social ul li a {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
}

@media only screen and (max-width: 767px) {
    .footer-middle {
        padding: 50px 0 20px;
    }

    .single-footer {
        margin-bottom: 40px;
    }

    .single-footer h3 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .f-link ul li {
        margin-bottom: 10px;
    }

    .whatsapp-float {
        width: 50px;
        height: 50px;
        font-size: 24px;
        bottom: 20px;
        left: 20px;
    }

    .scroll-top {
        width: 40px;
        height: 40px;
        font-size: 16px;
        bottom: 20px;
        right: 20px;
    }
}

@media only screen and (max-width: 575px) {
    .footer-middle {
        padding: 40px 0 10px;
    }

    .f-about .default-logo {
        font-size: 20px;
    }

    .f-about .default-logo i {
        font-size: 24px;
    }

    .footer-social ul li {
        margin-right: 10px;
    }

    .footer-social ul li a {
        width: 30px;
        height: 30px;
        font-size: 14px;
    }

    .whatsapp-float {
        width: 45px;
        height: 45px;
        font-size: 22px;
        bottom: 15px;
        left: 15px;
    }
}
