/*======================================
	Features CSS
========================================*/

.features {

    .padding-zero {
        padding: 0 !important;
    }

    &.style2 {
        .single-feature {
            padding: 50px;
            border-bottom: 1px solid #eee;

            h3 {
                line-height: 32px;

                a {
                    font-size: 20px;
                    font-weight: 700;

                    &:hover {
                        color: $theme-color-style2;
                    }
                }

            }

            &::before {
                background-color: $theme-color-style2;
            }
        }
    }

    .single-feature {
        position: relative;
        padding-right: 62px;
        padding: 60px;
        border-right: 1px solid #eee;
        position: relative;
        height: 100%;

        &::before {
            position: absolute;
            content: "";
            left: 0;
            bottom: 0;
            height: 5px;
            width: 0%;
            background-color: $theme-color;
            transition: all 0.4s ease-in-out;
        }


        &:hover::before {
            width: 100%;
        }

        &.last {
            border-right: none;
        }


        &:hover {
            h5:before {
                width: 100%;
            }
        }

        &:hover {
            h5 {
                letter-spacing: 2px;
            }
        }

        h3 {
            line-height: 32px;

            a {
                font-size: 22px;
                font-weight: 700;

                &:hover {
                    color: $theme-color;
                }
            }

        }

        p {
            display: block;
            margin-top: 20px;
        }

        &:hover {
            span {
                color: $theme-color;
                opacity: 1;
            }
        }

        .button {
            margin-top: 30px;

            .btn {
                color: $black;
                border: 1px solid #eee;
                background-color: transparent;
                padding: 10px 30px;

                &::before {
                    width: 0px;
                }

                &:hover::before {
                    width: 100%;
                }

                i {
                    display: inline-block;
                    margin-left: 5px;
                    font-size: 15px;
                    position: relative;
                    top: 2px;
                }

                &:hover {
                    color: $white;
                    border-color: transparent;
                }
            }
        }

    }
}