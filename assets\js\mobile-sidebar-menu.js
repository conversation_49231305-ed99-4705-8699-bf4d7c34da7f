/**
 * Mobile Sidebar Menu JavaScript for Zajel Arabic Theme
 * Simple and effective mobile menu functionality
 */

// Simple Mobile Menu Handler
document.addEventListener('DOMContentLoaded', function() {
    const menuToggle = document.getElementById('mobileMenuToggle');
    const menuClose = document.getElementById('mobileMenuClose');
    const sidebarMenu = document.getElementById('mobileSidebarMenu');
    const menuOverlay = document.getElementById('mobileMenuOverlay');
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');

    let isMenuOpen = false;

    // Debug: Check if elements exist
    console.log('Menu Toggle:', menuToggle);
    console.log('Sidebar Menu:', sidebarMenu);
    console.log('Menu Overlay:', menuOverlay);

    // Initialize menu functionality
    initMobileMenu();

    function initMobileMenu() {
        bindEvents();
        setupDropdowns();
    }

    function bindEvents() {
        // Menu toggle button
        if (menuToggle) {
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Menu toggle clicked!');
                toggleMenu();
            });
        }

        // Menu close button
        if (menuClose) {
            menuClose.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Menu close clicked!');
                closeMenu();
            });
        }

        // Overlay click to close
        if (menuOverlay) {
            menuOverlay.addEventListener('click', function() {
                console.log('Overlay clicked!');
                closeMenu();
            });
        }

        // Escape key to close menu
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isMenuOpen) {
                closeMenu();
            }
        });

        // Close menu on window resize to desktop
        window.addEventListener('resize', function() {
            if (window.innerWidth > 991 && isMenuOpen) {
                closeMenu();
            }
        });
    }

    function toggleMenu() {
        console.log('Toggle menu called, current state:', isMenuOpen);
        if (isMenuOpen) {
            closeMenu();
        } else {
            openMenu();
        }
    }

    function openMenu() {
        console.log('Opening menu...');
        isMenuOpen = true;

        // Add active classes
        if (menuToggle) menuToggle.classList.add('active');
        if (sidebarMenu) sidebarMenu.classList.add('active');
        if (menuOverlay) menuOverlay.classList.add('active');

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        console.log('Menu opened successfully');
    }

    function closeMenu() {
        console.log('Closing menu...');
        isMenuOpen = false;

        // Remove active classes
        if (menuToggle) menuToggle.classList.remove('active');
        if (sidebarMenu) sidebarMenu.classList.remove('active');
        if (menuOverlay) menuOverlay.classList.remove('active');

        // Restore body scroll
        document.body.style.overflow = '';

        // Close all dropdowns
        closeAllDropdowns();

        console.log('Menu closed successfully');
    }

    function setupDropdowns() {
        dropdownToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Dropdown clicked:', toggle);
                toggleDropdown(toggle);
            });
        });
    }

    function toggleDropdown(toggle) {
        const parentItem = toggle.closest('.mobile-nav-item');
        const isActive = parentItem.classList.contains('active');

        console.log('Toggling dropdown, current state:', isActive);

        // Close all other dropdowns
        closeAllDropdowns();

        // Toggle current dropdown
        if (!isActive) {
            parentItem.classList.add('active');

            // Animate dropdown
            const dropdown = parentItem.querySelector('.mobile-dropdown');
            if (dropdown) {
                dropdown.style.maxHeight = dropdown.scrollHeight + 'px';
            }
        }
    }

    function closeAllDropdowns() {
        const activeDropdowns = document.querySelectorAll('.mobile-nav-item.has-dropdown.active');
        activeDropdowns.forEach(function(item) {
            item.classList.remove('active');

            const dropdown = item.querySelector('.mobile-dropdown');
            if (dropdown) {
                dropdown.style.maxHeight = '0px';
            }
        });
    }

    // Simple function to highlight current page
    function highlightCurrentPage() {
        const currentPath = window.location.pathname;
        const menuLinks = document.querySelectorAll('.mobile-nav-link');

        menuLinks.forEach(function(link) {
            try {
                const linkPath = new URL(link.href).pathname;
                if (linkPath === currentPath) {
                    link.style.color = 'var(--primary-blue)';
                    link.style.backgroundColor = 'var(--light-gray)';
                }
            } catch (e) {
                // Handle relative URLs
            }
        });
    }

    // Call highlight function
    highlightCurrentPage();

    console.log('Mobile menu initialized successfully');
});


