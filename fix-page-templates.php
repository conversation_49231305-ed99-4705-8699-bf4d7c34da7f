<?php
// إصلاح templates الصفحات
require_once('../../../wp-load.php');

echo "<h1>Fix Page Templates</h1>";

// التحقق من الصلاحيات
if (!current_user_can('manage_options')) {
    echo "<p style='color: red;'>❌ You need admin privileges to update pages.</p>";
    echo "<p><a href='/wp-admin/'>Login to WordPress Admin</a></p>";
    exit;
}

$updates = array();
$errors = array();

// إصلاح Contact Page Template
$contact_page = get_page_by_path('contact');
if ($contact_page) {
    $result = update_post_meta($contact_page->ID, '_wp_page_template', 'page-contact.php');
    if ($result !== false) {
        $updates[] = "✅ Contact page (ID: {$contact_page->ID}) template updated to page-contact.php";
    } else {
        $errors[] = "❌ Failed to update Contact page template";
    }
} else {
    $errors[] = "❌ Contact page not found";
}

// إصلاح Trial Class Page Template
$trial_page = get_page_by_path('trial-class');
if ($trial_page) {
    $result = update_post_meta($trial_page->ID, '_wp_page_template', 'page-trial-class.php');
    if ($result !== false) {
        $updates[] = "✅ Trial Class page (ID: {$trial_page->ID}) template updated to page-trial-class.php";
    } else {
        $errors[] = "❌ Failed to update Trial Class page template";
    }
} else {
    $errors[] = "❌ Trial Class page not found";
}

// Flush rewrite rules
flush_rewrite_rules();
$updates[] = "✅ Rewrite rules flushed";

// عرض النتائج
if (!empty($updates)) {
    echo "<div style='background: #d4edda; color: #155724; padding: 10px; margin: 10px 0; border: 1px solid #c3e6cb;'>";
    echo "<h3>Updates Applied:</h3>";
    foreach ($updates as $update) {
        echo "<p>$update</p>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0; border: 1px solid #f5c6cb;'>";
    echo "<h3>Errors:</h3>";
    foreach ($errors as $error) {
        echo "<p>$error</p>";
    }
    echo "</div>";
}

// التحقق من النتائج
echo "<h2>Verification:</h2>";

$contact_template = get_page_template_slug($contact_page->ID);
$trial_template = get_page_template_slug($trial_page->ID);

echo "<p><strong>Contact Page Template:</strong> " . ($contact_template ? $contact_template : 'default') . "</p>";
echo "<p><strong>Trial Class Template:</strong> " . ($trial_template ? $trial_template : 'default') . "</p>";

// روابط الاختبار
echo "<h2>Test the Pages Now:</h2>";
$contact_url = get_permalink($contact_page->ID);
$trial_url = get_permalink($trial_page->ID);

echo "<p><a href='$contact_url' target='_blank' style='background: #007cba; color: white; padding: 10px; text-decoration: none; margin: 5px;'>Test Contact Page</a></p>";
echo "<p><a href='$trial_url' target='_blank' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px;'>Test Trial Class Page</a></p>";

echo "<h2>What Should Happen Now:</h2>";
echo "<ol>";
echo "<li>Click the test links above</li>";
echo "<li>You should see the actual Contact and Trial Class pages (not 404)</li>";
echo "<li>Try submitting the forms - you should see the yellow debug box</li>";
echo "<li>The forms should work and save data to the database</li>";
echo "</ol>";

// معلومات إضافية
echo "<h2>Debug Info:</h2>";
echo "<p><strong>Contact Page URL:</strong> $contact_url</p>";
echo "<p><strong>Trial Class URL:</strong> $trial_url</p>";
echo "<p><strong>Theme Directory:</strong> " . get_template_directory() . "</p>";

// التحقق من وجود ملفات الـ templates
$theme_dir = get_template_directory();
$contact_template_file = $theme_dir . '/page-contact.php';
$trial_template_file = $theme_dir . '/page-trial-class.php';

echo "<p><strong>Contact Template File:</strong> " . (file_exists($contact_template_file) ? "✅ Exists" : "❌ Missing") . "</p>";
echo "<p><strong>Trial Template File:</strong> " . (file_exists($trial_template_file) ? "✅ Exists" : "❌ Missing") . "</p>";
?>
