/**
 * Page Animations for Zajel Arabic Theme
 * Handles animations and interactions for Trial Class and Contact pages
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize animations
        initAnimations();
        
        // Initialize FAQ accordion
        initFaqAccordion();
        
        // Initialize form animations
        initFormAnimations();
    });

    /**
     * Initialize animations for page elements
     */
    function initAnimations() {
        // Add animation classes to elements as they appear in viewport
        $('.fade-in').each(function() {
            var $element = $(this);
            
            if(isElementInViewport($element)) {
                $element.addClass('animated');
            }
            
            $(window).on('scroll', function() {
                if(isElementInViewport($element)) {
                    $element.addClass('animated');
                }
            });
        });
        
        // Add animation classes with delay
        $('.fade-in-up, .fade-in-down, .fade-in-left, .fade-in-right').each(function() {
            var $element = $(this);
            
            if(isElementInViewport($element)) {
                $element.addClass('animated');
            }
            
            $(window).on('scroll', function() {
                if(isElementInViewport($element)) {
                    $element.addClass('animated');
                }
            });
        });
        
        // Animate feature cards
        $('.feature-card, .contact-info-card').each(function(index) {
            var $element = $(this);
            var delay = index * 200;
            
            $element.css('animation-delay', delay + 'ms');
            
            if(isElementInViewport($element)) {
                setTimeout(function() {
                    $element.addClass('animated');
                }, delay);
            }
            
            $(window).on('scroll', function() {
                if(isElementInViewport($element)) {
                    setTimeout(function() {
                        $element.addClass('animated');
                    }, delay);
                }
            });
        });
    }

    /**
     * Initialize FAQ accordion functionality
     */
    function initFaqAccordion() {
        $('.faq-question').on('click', function() {
            var $this = $(this);
            var $parent = $this.parent();
            var $answer = $parent.find('.faq-answer');
            
            if($parent.hasClass('active')) {
                $parent.removeClass('active');
            } else {
                $('.faq-item').removeClass('active');
                $parent.addClass('active');
            }
        });
    }

    /**
     * Initialize form animations
     */
    function initFormAnimations() {
        // Add focus and blur events to form fields
        $('.trial-form .form-control, .contact-form .form-control').on('focus', function() {
            $(this).parent('.form-group').addClass('focused');
        }).on('blur', function() {
            var $this = $(this);
            var $parent = $this.parent('.form-group');
            
            if($this.val() === '') {
                $parent.removeClass('focused');
            }
        });
        
        // Check if fields already have values (e.g., on page reload)
        $('.trial-form .form-control, .contact-form .form-control').each(function() {
            var $this = $(this);
            
            if($this.val() !== '') {
                $this.parent('.form-group').addClass('focused');
            }
        });
        
        // Add animation to submit button
        $('.btn-submit').on('mouseenter', function() {
            $(this).addClass('pulse');
        }).on('mouseleave', function() {
            var $this = $(this);
            
            setTimeout(function() {
                $this.removeClass('pulse');
            }, 500);
        });
    }

    /**
     * Check if element is in viewport
     * @param {jQuery} $element - The element to check
     * @return {boolean} - True if element is in viewport
     */
    function isElementInViewport($element) {
        var elementTop = $element.offset().top;
        var elementBottom = elementTop + $element.outerHeight();
        var viewportTop = $(window).scrollTop();
        var viewportBottom = viewportTop + $(window).height();
        
        return elementBottom > viewportTop && elementTop < viewportBottom;
    }

})(jQuery);
