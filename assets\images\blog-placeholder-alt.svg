<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="450" viewBox="0 0 800 450" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <rect width="800" height="450" fill="url(#bg-gradient)"/>
  
  <!-- Decorative wave pattern -->
  <path d="M0 350C133.333 300 266.667 275 400 275C533.333 275 666.667 300 800 350V450H0V350Z" fill="url(#wave-gradient)" opacity="0.3"/>
  <path d="M0 375C133.333 325 266.667 300 400 300C533.333 300 666.667 325 800 375V450H0V375Z" fill="url(#wave-gradient)" opacity="0.5"/>
  <path d="M0 400C133.333 375 266.667 362.5 400 362.5C533.333 362.5 666.667 375 800 400V450H0V400Z" fill="url(#wave-gradient)" opacity="0.7"/>
  
  <!-- Subtle pattern overlay -->
  <rect width="800" height="450" fill="url(#dot-pattern)" opacity="0.03"/>
  
  <!-- Center icon -->
  <g transform="translate(400, 200)">
    <!-- Circle background -->
    <circle cx="0" cy="0" r="100" fill="#FFFFFF" opacity="0.95" filter="url(#glow)"/>
    
    <!-- Stylized Arabic letter ز (Zay) for Zajel -->
    <path d="M-40 -20C-40 -20 -20 -40 0 -40C20 -40 40 -20 40 -20" stroke="#03355c" stroke-width="8" stroke-linecap="round"/>
    <path d="M-40 0C-40 0 -20 -20 0 -20C20 -20 40 0 40 0" stroke="#03355c" stroke-width="8" stroke-linecap="round"/>
    <path d="M-40 20C-40 20 -20 0 0 0C20 0 40 20 40 20" stroke="#03355c" stroke-width="8" stroke-linecap="round"/>
    
    <!-- Dot for the letter -->
    <circle cx="0" cy="-50" r="8" fill="#1a5f8d"/>
  </g>
  
  <!-- Text at bottom -->
  <g transform="translate(400, 350)">
    <rect x="-200" y="-25" width="400" height="50" rx="25" fill="#FFFFFF" opacity="0.1"/>
    <text x="0" y="7" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#FFFFFF" text-anchor="middle">Zajel Arabic Academy Blog</text>
  </g>
  
  <!-- Definitions -->
  <defs>
    <!-- Main background gradient -->
    <linearGradient id="bg-gradient" x1="0" y1="0" x2="800" y2="450" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1a5f8d"/>
      <stop offset="1" stop-color="#03355c"/>
    </linearGradient>
    
    <!-- Wave gradient -->
    <linearGradient id="wave-gradient" x1="400" y1="275" x2="400" y2="450" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF" stop-opacity="0.1"/>
      <stop offset="1" stop-color="#FFFFFF" stop-opacity="0.3"/>
    </linearGradient>
    
    <!-- Dot pattern -->
    <pattern id="dot-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="#FFFFFF"/>
    </pattern>
    
    <!-- Glow effect for center circle -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
  </defs>
</svg>
