/* Archive Page Styles for Zajel Arabic Theme */
:root {
    --blue-light: #1a5f8d;
    --blue-medium: #0c4a77;
    --blue-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Breadcrumbs */
.breadcrumbs {
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.breadcrumbs::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 1;
}

.breadcrumbs .container {
    position: relative;
    z-index: 2;
}

.breadcrumbs-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.breadcrumbs-content .page-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-bottom: 20px;
}

.breadcrumbs-content .page-icon i {
    color: #fff;
    font-size: 32px;
}

.breadcrumbs-content .page-title {
    color: #fff;
    font-size: 36px;
    font-weight: 800;
    margin-bottom: 15px;
}

.archive-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 20px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.breadcrumb-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 10px;
}

.breadcrumb-nav li {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

.breadcrumb-nav li a {
    color: #fff;
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb-nav li a:hover {
    color: rgba(255, 255, 255, 0.8);
}

.breadcrumb-nav li i {
    margin: 0 5px;
    font-size: 12px;
}

/* Blog Area */
.blog-area {
    background-color: #f9f9f9;
    padding: 100px 0;
    position: relative;
}

.blog-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a5f8d' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 0;
}

.blog-area .container {
    position: relative;
    z-index: 1;
}

/* Blog Posts */
.blog-posts {
    margin-bottom: 50px;
}

/* Archive Post Item */
.archive-post {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-small);
    margin-bottom: 30px;
    transition: var(--transition-medium);
    display: flex;
    flex-direction: row;
}

.archive-post:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.archive-post .post-thumbnail {
    flex: 0 0 300px;
    position: relative;
    overflow: hidden;
}

.archive-post .post-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-medium);
}

.archive-post:hover .post-thumbnail img {
    transform: scale(1.05);
}

.archive-post .post-content {
    flex: 1;
    padding: 25px;
    display: flex;
    flex-direction: column;
}

.archive-post .post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 14px;
}

.archive-post .post-meta > div {
    display: flex;
    align-items: center;
    position: relative;
}

.archive-post .post-meta > div:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 12px;
    background-color: #ddd;
}

.archive-post .post-meta i {
    color: var(--blue-medium);
    margin-right: 5px;
}

.archive-post .post-meta a {
    color: #666;
    text-decoration: none;
    transition: var(--transition-fast);
}

.archive-post .post-meta a:hover {
    color: var(--blue-medium);
}

.archive-post .post-title {
    font-size: 24px;
    margin-bottom: 15px;
    font-weight: 700;
    line-height: 1.4;
}

.archive-post .post-title a {
    color: #333;
    text-decoration: none;
    transition: var(--transition-fast);
}

.archive-post .post-title a:hover {
    color: var(--blue-medium);
}

.archive-post .post-excerpt {
    color: #666;
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 20px;
    flex-grow: 1;
}

.archive-post .read-more {
    margin-top: auto;
    align-self: flex-start;
}

.archive-post .read-more a {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, var(--blue-light), var(--blue-medium));
    color: #fff;
    padding: 10px 20px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-fast);
    box-shadow: 0 4px 10px rgba(26, 95, 141, 0.2);
}

.archive-post .read-more a i {
    margin-left: 5px;
    transition: var(--transition-fast);
}

.archive-post .read-more a:hover {
    background: linear-gradient(135deg, var(--blue-medium), var(--blue-dark));
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(26, 95, 141, 0.3);
}

.archive-post .read-more a:hover i {
    transform: translateX(3px);
}

/* Sticky Post */
.archive-post.sticky {
    position: relative;
    border-left: 4px solid var(--blue-medium);
}

.archive-post.sticky::before {
    content: 'Featured';
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    color: #fff;
    padding: 5px 15px;
    border-radius: 30px;
    font-size: 12px;
    font-weight: 600;
    z-index: 10;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

/* Pagination */
.pagination-area {
    margin-top: 50px;
    text-align: center;
}

.pagination {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0;
    padding: 0;
}

.pagination .page-numbers {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination .page-numbers .page-numbers {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #fff;
    color: #333;
    border-radius: 50%;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-small);
}

.pagination .page-numbers .page-numbers:hover {
    background-color: var(--blue-light);
    color: #fff;
}

.pagination .page-numbers .page-numbers.current {
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    color: #fff;
    box-shadow: 0 4px 10px rgba(26, 95, 141, 0.2);
}

.pagination .prev-page a,
.pagination .next-page a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #fff;
    color: #333;
    border-radius: 50%;
    font-size: 16px;
    text-decoration: none;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-small);
}

.pagination .prev-page a:hover,
.pagination .next-page a:hover {
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    color: #fff;
    box-shadow: 0 4px 10px rgba(26, 95, 141, 0.2);
}

.pagination .prev-page a i,
.pagination .next-page a i {
    transition: var(--transition-fast);
}

.pagination .prev-page a:hover i {
    transform: translateX(-3px);
}

.pagination .next-page a:hover i {
    transform: translateX(3px);
}

/* Blog Sidebar */
.blog-sidebar {
    position: sticky;
    top: 30px;
}

.widget {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-small);
}

.widget-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(26, 95, 141, 0.1);
    position: relative;
    color: #333;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--blue-dark), var(--blue-light));
}

/* Search Widget */
.widget_search .search-form {
    position: relative;
}

.widget_search .search-field {
    width: 100%;
    padding: 12px 20px;
    padding-right: 50px;
    border: 1px solid #eee;
    border-radius: 30px;
    font-size: 15px;
    transition: var(--transition-fast);
}

.widget_search .search-field:focus {
    border-color: var(--blue-light);
    outline: none;
    box-shadow: 0 0 0 3px rgba(26, 95, 141, 0.1);
}

.widget_search .search-submit {
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
    height: 100%;
    background-color: transparent;
    border: none;
    color: #666;
    font-size: 18px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.widget_search .search-submit:hover {
    color: var(--blue-medium);
}

/* Categories Widget */
.widget_categories ul,
.widget_recent_entries ul,
.widget_archive ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.widget_categories li,
.widget_recent_entries li,
.widget_archive li {
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: var(--transition-fast);
}

.widget_categories li:last-child,
.widget_recent_entries li:last-child,
.widget_archive li:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.widget_categories li:first-child,
.widget_recent_entries li:first-child,
.widget_archive li:first-child {
    padding-top: 0;
}

.widget_categories a,
.widget_archive a {
    color: #666;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: var(--transition-fast);
}

.widget_categories a::before,
.widget_archive a::before {
    content: '\f105';
    font-family: 'Line Awesome Free';
    font-weight: 900;
    margin-right: 10px;
    color: var(--blue-light);
    transition: var(--transition-fast);
}

.widget_categories a:hover,
.widget_archive a:hover {
    color: var(--blue-medium);
    padding-left: 5px;
}

.widget_categories a:hover::before,
.widget_archive a:hover::before {
    margin-right: 15px;
}

.widget_categories a span {
    background-color: #f0f0f0;
    color: #666;
    padding: 2px 8px;
    border-radius: 20px;
    font-size: 12px;
    transition: var(--transition-fast);
}

.widget_categories a:hover span {
    background-color: var(--blue-light);
    color: #fff;
}

/* Recent Posts Widget */
.widget_recent_entries li {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.widget_recent_entries .post-image {
    width: 70px;
    height: 70px;
    border-radius: 5px;
    overflow: hidden;
    flex-shrink: 0;
}

.widget_recent_entries .post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-fast);
}

.widget_recent_entries li:hover .post-image img {
    transform: scale(1.05);
}

.widget_recent_entries .post-content {
    flex-grow: 1;
}

.widget_recent_entries .post-title {
    font-size: 15px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 5px;
}

.widget_recent_entries .post-title a {
    color: #333;
    text-decoration: none;
    transition: var(--transition-fast);
}

.widget_recent_entries .post-title a:hover {
    color: var(--blue-medium);
}

.widget_recent_entries .post-date {
    font-size: 12px;
    color: #888;
    display: flex;
    align-items: center;
}

.widget_recent_entries .post-date i {
    color: var(--blue-light);
    margin-right: 5px;
}

/* Tag Cloud Widget */
.widget_tag_cloud .tagcloud {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.widget_tag_cloud .tag-cloud-link {
    display: inline-block;
    padding: 5px 15px;
    background-color: #f0f0f0;
    color: #666;
    border-radius: 20px;
    font-size: 13px !important;
    text-decoration: none;
    transition: var(--transition-fast);
}

.widget_tag_cloud .tag-cloud-link:hover {
    background: linear-gradient(135deg, var(--blue-light), var(--blue-dark));
    color: #fff;
    transform: translateY(-2px);
}

/* No Results */
.no-results {
    text-align: center;
    padding: 50px 0;
}

.no-results .page-title {
    font-size: 28px;
    margin-bottom: 15px;
    color: #333;
}

.no-results p {
    margin-bottom: 20px;
    color: #666;
}

.no-results .search-form {
    max-width: 500px;
    margin: 0 auto;
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .breadcrumbs {
        padding: 60px 0;
    }
    
    .breadcrumbs-content .page-title {
        font-size: 32px;
    }
    
    .blog-area {
        padding: 80px 0;
    }
    
    .archive-post {
        flex-direction: column;
    }
    
    .archive-post .post-thumbnail {
        flex: 0 0 auto;
        height: 250px;
    }
    
    .blog-sidebar {
        margin-top: 50px;
        position: static;
    }
}

@media only screen and (max-width: 767px) {
    .breadcrumbs {
        padding: 50px 0;
    }
    
    .breadcrumbs-content .page-icon {
        width: 60px;
        height: 60px;
    }
    
    .breadcrumbs-content .page-icon i {
        font-size: 24px;
    }
    
    .breadcrumbs-content .page-title {
        font-size: 28px;
    }
    
    .archive-description {
        font-size: 14px;
    }
    
    .breadcrumb-nav li {
        font-size: 14px;
    }
    
    .blog-area {
        padding: 60px 0;
    }
    
    .archive-post .post-title {
        font-size: 20px;
    }
    
    .archive-post .post-meta {
        font-size: 12px;
    }
    
    .pagination .page-numbers .page-numbers,
    .pagination .prev-page a,
    .pagination .next-page a {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
    
    .widget {
        padding: 20px;
    }
    
    .widget-title {
        font-size: 18px;
    }
}

@media only screen and (max-width: 575px) {
    .breadcrumbs {
        padding: 40px 0;
    }
    
    .breadcrumbs-content .page-title {
        font-size: 24px;
    }
    
    .blog-area {
        padding: 50px 0;
    }
    
    .archive-post .post-thumbnail {
        height: 200px;
    }
    
    .archive-post .post-content {
        padding: 20px;
    }
    
    .archive-post .post-meta > div:not(:last-child)::after {
        display: none;
    }
    
    .archive-post .read-more a {
        padding: 8px 15px;
        font-size: 13px;
    }
}
