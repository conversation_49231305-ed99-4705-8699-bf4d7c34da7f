<?php
/**
 * Template part for displaying a message that posts cannot be found
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Zajel_Arabic
 */
?>

<section class="no-results not-found">
    <div class="page-content">
        <div class="no-posts-found wow fadeInUp" data-wow-delay=".2s">
            <div class="icon">
                <i class="lni lni-search"></i>
            </div>

            <?php if (is_search()) : ?>
                <h2 class="page-title"><?php esc_html_e('No Results Found', 'zajel'); ?></h2>
                <p><?php esc_html_e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'zajel'); ?></p>

                <div class="search-form-container">
                    <?php get_search_form(); ?>
                </div>

                <div class="suggestions">
                    <h3><?php esc_html_e('Popular Topics', 'zajel'); ?></h3>
                    <div class="suggestion-tags">
                        <?php
                        $tags = get_tags(array('orderby' => 'count', 'order' => 'DESC', 'number' => 6));
                        if ($tags) :
                            foreach ($tags as $tag) :
                        ?>
                            <a href="<?php echo esc_url(get_tag_link($tag->term_id)); ?>" class="suggestion-tag">
                                <?php echo esc_html($tag->name); ?>
                            </a>
                        <?php
                            endforeach;
                        endif;
                        ?>
                    </div>
                </div>
            <?php else : ?>
                <h2 class="page-title"><?php esc_html_e('Nothing Found', 'zajel'); ?></h2>
                <p><?php esc_html_e('It seems we can\'t find what you\'re looking for.', 'zajel'); ?></p>

                <?php if (current_user_can('publish_posts')) : ?>
                    <p>
                        <?php
                        printf(
                            wp_kses(
                                /* translators: 1: link to WP admin new post page. */
                                __('Ready to publish your first post? <a href="%1$s">Get started here</a>.', 'zajel'),
                                array(
                                    'a' => array(
                                        'href' => array(),
                                    ),
                                )
                            ),
                            esc_url(admin_url('post-new.php'))
                        );
                        ?>
                    </p>
                <?php else : ?>
                    <p><?php esc_html_e('Perhaps searching can help you find what you\'re looking for.', 'zajel'); ?></p>

                    <div class="search-form-container">
                        <?php get_search_form(); ?>
                    </div>

                    <div class="browse-categories">
                        <h3><?php esc_html_e('Browse Categories', 'zajel'); ?></h3>
                        <ul class="category-list">
                            <?php
                            $categories = get_categories(array('orderby' => 'count', 'order' => 'DESC', 'number' => 6));
                            foreach ($categories as $category) :
                            ?>
                                <li>
                                    <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>">
                                        <i class="lni lni-chevron-right"></i> <?php echo esc_html($category->name); ?>
                                        <span class="count">(<?php echo esc_html($category->count); ?>)</span>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <div class="back-to-home">
                <a href="<?php echo esc_url(home_url('/')); ?>" class="btn">
                    <i class="lni lni-home"></i> <?php esc_html_e('Back to Homepage', 'zajel'); ?>
                </a>
            </div>
        </div>
    </div><!-- .page-content -->
</section><!-- .no-results -->
