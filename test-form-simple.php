<!DOCTYPE html>
<html>
<head>
    <title>Test Forms</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        input, textarea, select { width: 300px; padding: 8px; margin: 5px 0; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>

<h1>Test Forms - Simple Version</h1>

<?php
// تحميل WordPress
require_once('../../../wp-load.php');

$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("=== SIMPLE TEST FORM ===");
    error_log("POST data: " . print_r($_POST, true));
    
    if (isset($_POST['form_type']) && $_POST['form_type'] === 'contact') {
        // Test Contact Form
        if (!empty($_POST['name']) && !empty($_POST['email']) && !empty($_POST['message'])) {
            global $wpdb;
            $table_name = $wpdb->prefix . 'contact';
            
            $result = $wpdb->insert(
                $table_name,
                array(
                    'name' => sanitize_text_field($_POST['name']),
                    'subject' => sanitize_text_field($_POST['subject']),
                    'email' => sanitize_email($_POST['email']),
                    'phone' => sanitize_text_field($_POST['phone']),
                    'message' => sanitize_textarea_field($_POST['message']),
                    'created_at' => current_time('mysql'),
                ),
                array('%s', '%s', '%s', '%s', '%s', '%s')
            );
            
            if ($result !== false) {
                $message = '<div class="alert alert-success">✅ Contact form SUCCESS! Data saved to database.</div>';
                error_log("Contact test: SUCCESS");
            } else {
                $message = '<div class="alert alert-danger">❌ Contact form FAILED: ' . $wpdb->last_error . '</div>';
                error_log("Contact test: FAILED - " . $wpdb->last_error);
            }
        } else {
            $message = '<div class="alert alert-danger">❌ Missing required fields</div>';
        }
    }
    
    if (isset($_POST['form_type']) && $_POST['form_type'] === 'trial') {
        // Test Trial Form
        if (!empty($_POST['full_name']) && !empty($_POST['email']) && !empty($_POST['phone']) && !empty($_POST['course'])) {
            global $wpdb;
            $table_name = $wpdb->prefix . 'trial_bookings';
            
            // Create table if not exists
            if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
                $charset_collate = $wpdb->get_charset_collate();
                $sql = "CREATE TABLE $table_name (
                    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                    full_name VARCHAR(255) NOT NULL,
                    email VARCHAR(255) NOT NULL,
                    phone VARCHAR(20) NOT NULL,
                    course VARCHAR(100) NOT NULL,
                    additional_notes TEXT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id)
                ) $charset_collate;";
                
                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                dbDelta($sql);
                error_log("Trial table created");
            }
            
            $result = $wpdb->insert(
                $table_name,
                array(
                    'full_name' => sanitize_text_field($_POST['full_name']),
                    'email' => sanitize_email($_POST['email']),
                    'phone' => sanitize_text_field($_POST['phone']),
                    'course' => sanitize_text_field($_POST['course']),
                    'additional_notes' => sanitize_textarea_field($_POST['additional_notes']),
                    'ip_address' => $_SERVER['REMOTE_ADDR'],
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'],
                    'created_at' => current_time('mysql'),
                ),
                array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
            );
            
            if ($result !== false) {
                $message = '<div class="alert alert-success">✅ Trial form SUCCESS! Data saved to database.</div>';
                error_log("Trial test: SUCCESS");
            } else {
                $message = '<div class="alert alert-danger">❌ Trial form FAILED: ' . $wpdb->last_error . '</div>';
                error_log("Trial test: FAILED - " . $wpdb->last_error);
            }
        } else {
            $message = '<div class="alert alert-danger">❌ Missing required fields</div>';
        }
    }
}

echo $message;
?>

<h2>Test Contact Form</h2>
<form method="post" action="">
    <input type="hidden" name="form_type" value="contact">
    <div class="form-group">
        <label>Name *:</label><br>
        <input type="text" name="name" required>
    </div>
    <div class="form-group">
        <label>Email *:</label><br>
        <input type="email" name="email" required>
    </div>
    <div class="form-group">
        <label>Subject:</label><br>
        <input type="text" name="subject">
    </div>
    <div class="form-group">
        <label>Phone:</label><br>
        <input type="text" name="phone">
    </div>
    <div class="form-group">
        <label>Message *:</label><br>
        <textarea name="message" required></textarea>
    </div>
    <button type="submit">Test Contact Form</button>
</form>

<h2>Test Trial Form</h2>
<form method="post" action="">
    <input type="hidden" name="form_type" value="trial">
    <div class="form-group">
        <label>Full Name *:</label><br>
        <input type="text" name="full_name" required>
    </div>
    <div class="form-group">
        <label>Email *:</label><br>
        <input type="email" name="email" required>
    </div>
    <div class="form-group">
        <label>Phone *:</label><br>
        <input type="text" name="phone" required>
    </div>
    <div class="form-group">
        <label>Course *:</label><br>
        <select name="course" required>
            <option value="">Select Course</option>
            <option value="quran">Quran</option>
            <option value="tajweed">Tajweed</option>
            <option value="arabic">Arabic</option>
            <option value="islamic">Islamic</option>
        </select>
    </div>
    <div class="form-group">
        <label>Additional Notes:</label><br>
        <textarea name="additional_notes"></textarea>
    </div>
    <button type="submit">Test Trial Form</button>
</form>

<h2>Quick Links</h2>
<p><a href="debug-logs.php">View Debug Logs</a></p>
<p><a href="<?php echo admin_url('admin.php?page=zajel-contact-messages'); ?>">Contact Messages Dashboard</a></p>
<p><a href="<?php echo admin_url('admin.php?page=zajel-trial-bookings'); ?>">Trial Bookings Dashboard</a></p>

</body>
</html>
