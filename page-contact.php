<?php
/*
Template Name: Contact Page
*/

// بدء الجلسة للحماية من الإرسال المتكرر
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

get_header();

// Enqueue CSS and JS for Contact page
wp_enqueue_style('contact-styles', get_template_directory_uri() . '/assets/css/contact.css', array(), '1.0.0');
wp_enqueue_script('page-animations', get_template_directory_uri() . '/assets/js/page-animations.js', array('jquery'), '1.0.0', true);

// معالجة الفورم مباشرة مع logs مفصلة
$success_message = '';
$error_message = '';

// إضافة logs مفصلة
error_log("=== Contact Form Debug Start ===");
error_log("Request Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Request URI: " . $_SERVER['REQUEST_URI']);
error_log("POST data exists: " . (empty($_POST) ? 'NO' : 'YES'));
if (!empty($_POST)) {
    error_log("POST keys: " . implode(', ', array_keys($_POST)));
    error_log("Full POST data: " . print_r($_POST, true));
}
error_log("Contact nonce exists: " . (isset($_POST['contact_nonce']) ? 'YES' : 'NO'));

// إضافة debugging مرئي
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo '<div style="background: yellow; padding: 10px; margin: 10px; border: 2px solid red;">';
    echo '<h3>DEBUG: POST Request Detected!</h3>';
    echo '<p>Method: ' . $_SERVER['REQUEST_METHOD'] . '</p>';
    echo '<p>URI: ' . $_SERVER['REQUEST_URI'] . '</p>';
    echo '<p>POST Data: ' . (empty($_POST) ? 'EMPTY' : 'EXISTS') . '</p>';
    if (!empty($_POST)) {
        echo '<pre>' . print_r($_POST, true) . '</pre>';
    }
    echo '</div>';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['contact_nonce'])) {
    error_log("Processing contact form...");

    // التحقق من nonce
    if (wp_verify_nonce($_POST['contact_nonce'], 'contact_form_action')) {

        // التحقق من الحقول المطلوبة
        if (!empty($_POST['name']) && !empty($_POST['email']) && !empty($_POST['message'])) {

            // تنظيف البيانات
            $name = sanitize_text_field($_POST['name']);
            $subject = sanitize_text_field($_POST['subject']);
            $email = sanitize_email($_POST['email']);
            $phone = sanitize_text_field($_POST['phone']);
            $message = sanitize_textarea_field($_POST['message']);

            // التحقق من صحة الإيميل
            if (is_email($email)) {

                // حفظ في قاعدة البيانات
                global $wpdb;
                $table_name = $wpdb->prefix . 'contact';

                $result = $wpdb->insert(
                    $table_name,
                    array(
                        'name' => $name,
                        'subject' => $subject,
                        'email' => $email,
                        'phone' => $phone,
                        'message' => $message,
                        'created_at' => current_time('mysql'),
                    ),
                    array('%s', '%s', '%s', '%s', '%s', '%s')
                );

                if ($result !== false) {
                    $success_message = '<div class="alert alert-success">Your message has been sent successfully! We will get back to you soon.</div>';
                    error_log("Contact form: SUCCESS - Data saved to database");
                } else {
                    $error_message = '<div class="alert alert-danger">Database error occurred. Please try again.</div>';
                    error_log("Contact form: ERROR - Database insert failed: " . $wpdb->last_error);
                }
            } else {
                $error_message = '<div class="alert alert-danger">Please enter a valid email address.</div>';
                error_log("Contact form: ERROR - Invalid email: " . $email);
            }
        } else {
            $error_message = '<div class="alert alert-danger">Please fill in all required fields.</div>';
            error_log("Contact form: ERROR - Missing required fields");
        }
    } else {
        $error_message = '<div class="alert alert-danger">Security check failed. Please try again.</div>';
        error_log("Contact form: ERROR - Nonce verification failed");
    }
} else {
    error_log("Contact form: No POST request or missing nonce");
}

error_log("=== Contact Form Debug End ===");
?>
<!-- Start Breadcrumbs -->
<div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <div class="page-icon wow zoomIn" data-wow-delay=".2s">
                        <i class="lni lni-information"></i>
                    </div>
                    <h1 class="page-title wow fadeInUp" data-wow-delay=".4s"><?php the_title(); ?></h1>
                    <p class="wow fadeInUp" data-wow-delay=".6s">Have any questions? Feel free to reach out to Zajel Arabic, and we'll get back to you as soon as possible.</p>
                </div>
                <ul class="breadcrumb-nav wow fadeInUp" data-wow-delay=".8s">
                    <li><a href="<?php echo esc_url(home_url('/')); ?>"><i class="lni lni-home"></i> Home</a></li>
                    <li><i class="lni lni-chevron-right"></i></li>
                    <li><?php the_title(); ?></li>
                </ul>
            </div>
        </div>

        <!-- Animated Elements -->
        <div class="animated-circle circle-1 wow fadeIn" data-wow-delay=".3s"></div>
        <div class="animated-circle circle-2 wow fadeIn" data-wow-delay=".5s"></div>
        <div class="animated-square square-1 wow fadeIn" data-wow-delay=".7s"></div>
        <div class="animated-square square-2 wow fadeIn" data-wow-delay=".9s"></div>
        <div class="animated-circle circle-3 pulse" data-wow-delay="1.1s"></div>
    </div>
</div>
<!-- End Breadcrumbs -->


<!-- Start Contact Info Section -->
<section class="contact-info-section">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="fade-in">Get in Touch</h2>
                <p class="fade-in">We're here to help and answer any question you might have. We look forward to hearing from you.</p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4 col-md-6 col-12">
                <div class="contact-info-card fade-in">
                    <div class="contact-info-icon">
                        <i class="lni lni-map-marker"></i>
                    </div>
                    <h3 class="contact-info-title">Visit Our Office</h3>
                    <p class="contact-info-text">123 Zajel Street, Al Azhar District<br> Cairo, Egypt</p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 col-12">
                <div class="contact-info-card fade-in">
                    <div class="contact-info-icon">
                        <i class="lni lni-phone"></i>
                    </div>
                    <h3 class="contact-info-title">Let's Talk</h3>
                    <p class="contact-info-text">Phone: (+20) 123 456 789<br> Fax: (+20) 123 456 789</p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 col-12">
                <div class="contact-info-card fade-in">
                    <div class="contact-info-icon">
                        <i class="lni lni-envelope"></i>
                    </div>
                    <h3 class="contact-info-title">E-mail Us</h3>
                    <p class="contact-info-text"><a href="mailto:<EMAIL>"><EMAIL></a><br> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Contact Info Section -->

<!-- Start Contact Form Section -->
<section class="contact-form-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 offset-lg-2 col-md-10 offset-md-1 col-12">
                <div class="contact-form-container">
                    <div class="contact-form-box">
                        <h3>Send Us a Message</h3>
                        <p>Fill out the form below, and we'll get back to you as soon as possible.</p>

                        <?php
                        // عرض رسائل النجاح أو الخطأ
                        if ($success_message) {
                            echo $success_message;
                        }
                        if ($error_message) {
                            echo $error_message;
                        }
                        ?>

                        <form class="contact-form" method="post" action="">
                            <?php wp_nonce_field('contact_form_action', 'contact_nonce'); ?>
                            <div class="row">
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label class="form-label">Your Name <span class="text-danger">*</span></label>
                                        <input name="name" type="text" class="form-control" placeholder="Enter your full name" required maxlength="100">
                                    </div>
                                </div>

                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label class="form-label">Your Subject <span class="text-danger">*</span></label>
                                        <input name="subject" type="text" class="form-control" placeholder="Enter your subject" required maxlength="200">
                                    </div>
                                </div>

                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label class="form-label">Email Address <span class="text-danger">*</span></label>
                                        <input name="email" type="email" class="form-control" placeholder="Enter your email address" required maxlength="255">
                                    </div>
                                </div>

                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label class="form-label">Phone Number</label>
                                        <input name="phone" type="tel" class="form-control" placeholder="Enter your phone number" maxlength="20">
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label">Your Message <span class="text-danger">*</span></label>
                                        <textarea name="message" class="form-control" placeholder="Type your message here..." required maxlength="1000"></textarea>
                                    </div>
                                </div>

                                <!-- حقل Honeypot للحماية من السبام -->
                                <div style="display:none;">
                                    <input type="text" name="honeypot" class="honeypot">
                                </div>

                                <div class="col-12">
                                    <button type="submit" class="btn-submit">Send Message</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Contact Form Section -->

<!-- Start Map Section -->
<section class="contact-map-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="map-container">
                    <iframe src="https://maps.google.com/maps?q=Cairo,%20Egypt&t=&z=13&ie=UTF8&iwloc=&output=embed" frameborder="0" scrolling="no" marginheight="0" marginwidth="0"></iframe>
                    <div class="map-overlay"></div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Map Section -->

<!-- Start Social Media Section -->
<section class="contact-social-section">
    <div class="container">
        <div class="social-container">
            <h2 class="social-title fade-in">Connect With Us</h2>
            <p class="social-description fade-in">Follow us on social media to stay updated with our latest news, events, and educational content.</p>

            <div class="social-icons">
                <a href="#" class="social-icon fade-in"><i class="lni lni-facebook-filled"></i></a>
                <a href="#" class="social-icon fade-in"><i class="lni lni-twitter-original"></i></a>
                <a href="#" class="social-icon fade-in"><i class="lni lni-instagram-original"></i></a>
                <a href="#" class="social-icon fade-in"><i class="lni lni-youtube"></i></a>
                <a href="#" class="social-icon fade-in"><i class="lni lni-whatsapp"></i></a>
            </div>
        </div>
    </div>
</section>
<!-- End Social Media Section -->

<?php get_footer(); ?>