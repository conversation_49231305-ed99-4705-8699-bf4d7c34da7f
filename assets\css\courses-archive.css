/* Courses Archive Page Styles */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Breadcrumbs Section */
.breadcrumbs.overlay {
    position: relative;
    padding: 100px 0 80px;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/teachers-bg.svg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    text-align: center;
    color: #fff;
}

.breadcrumbs.overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231a5f8d' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 0.2;
    z-index: 0;
}

.breadcrumbs .container {
    position: relative;
    z-index: 1;
}

.page-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    border-radius: 50%;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(26, 95, 141, 0.3);
}

.page-icon i {
    font-size: 36px;
    color: #fff;
}

.breadcrumbs-content h1 {
    font-size: 42px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #fff;
}

.breadcrumbs-content p {
    font-size: 18px;
    max-width: 700px;
    margin: 0 auto 20px;
    color: rgba(255, 255, 255, 0.9);
}

.breadcrumb-nav {
    display: flex;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-nav li {
    display: inline-flex;
    align-items: center;
    margin: 0 5px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

.breadcrumb-nav li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb-nav li a:hover {
    color: #fff;
}

.breadcrumb-nav li i {
    margin-right: 5px;
    font-size: 14px;
}

/* Filters Section */
.course-filters {
    margin-bottom: 50px;
}

.filters-wrapper {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 25px;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
}

.filters-wrapper:hover {
    box-shadow: var(--shadow-medium);
}

.filter-item {
    margin-bottom: 0;
}

.filter-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--gold-dark);
    font-size: 14px;
}

.filter-item label i {
    margin-right: 5px;
    color: var(--gold-medium);
}

.filter-item .form-control {
    border: 1px solid rgba(26, 95, 141, 0.2);
    border-radius: 5px;
    padding: 10px 15px;
    font-size: 14px;
    transition: var(--transition-fast);
}

.filter-item .form-control:focus {
    border-color: var(--gold-medium);
    box-shadow: 0 0 0 0.2rem rgba(26, 95, 141, 0.15);
}

/* Active Filters */
.active-filters {
    margin-bottom: 30px;
}

.active-filters-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 15px 20px;
    background-color: rgba(26, 95, 141, 0.05);
    border-radius: 8px;
}

.active-filters-title {
    font-weight: 600;
    color: var(--gold-dark);
    margin-right: 15px;
    font-size: 14px;
}

.active-filters-tags {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    background-color: rgba(26, 95, 141, 0.1);
    color: var(--gold-dark);
    padding: 5px 12px;
    border-radius: 20px;
    margin: 5px;
    font-size: 13px;
    transition: var(--transition-fast);
}

.filter-tag i {
    margin-left: 5px;
    font-size: 12px;
    cursor: pointer;
}

.filter-tag:hover {
    background-color: rgba(26, 95, 141, 0.2);
}

.clear-all-filters {
    background: none;
    border: none;
    color: var(--gold-medium);
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    padding: 5px 10px;
}

.clear-all-filters:hover {
    color: var(--gold-dark);
}

.clear-all-filters i {
    margin-right: 5px;
    font-size: 12px;
}

/* Courses Grid */
.courses-grid {
    margin-bottom: 70px;
}

.single-course {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.single-course:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.course-image {
    position: relative;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: var(--transition-medium);
}

.single-course:hover .course-image img {
    transform: scale(1.05);
}

.course-level {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    z-index: 1;
    box-shadow: 0 3px 10px rgba(26, 95, 141, 0.3);
}

.course-content {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.course-categories {
    margin-bottom: 15px;
    display: flex;
    flex-wrap: wrap;
}

.course-categories .category {
    display: inline-block;
    padding: 3px 10px;
    background-color: rgba(26, 95, 141, 0.1);
    color: var(--gold-dark);
    border-radius: 15px;
    font-size: 12px;
    margin-right: 8px;
    margin-bottom: 5px;
    text-decoration: none;
    transition: var(--transition-fast);
}

.course-categories .category:hover {
    background-color: var(--gold-dark);
    color: #fff;
}

.more-categories {
    display: inline-block;
    padding: 3px 10px;
    background-color: rgba(26, 95, 141, 0.05);
    color: var(--gold-medium);
    border-radius: 15px;
    font-size: 12px;
}

.course-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 15px;
    line-height: 1.4;
}

.course-title a {
    color: #333;
    text-decoration: none;
    transition: var(--transition-fast);
}

.course-title a:hover {
    color: var(--gold-dark);
}

.course-meta {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.meta-item {
    display: flex;
    align-items: center;
    margin-right: 15px;
    margin-bottom: 8px;
    font-size: 13px;
    color: #666;
}

.meta-item i {
    margin-right: 5px;
    color: var(--gold-medium);
    font-size: 14px;
}

.course-excerpt {
    margin-bottom: 20px;
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    flex: 1;
}

.course-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid rgba(26, 95, 141, 0.1);
}

.course-price .price {
    font-size: 18px;
    font-weight: 700;
    color: var(--gold-dark);
}

.course-price .price.free {
    color: #1a5f8d;
}

.course-button .btn {
    padding: 8px 15px;
    font-size: 13px;
    font-weight: 600;
    border-radius: 5px;
    transition: var(--transition-fast);
}

.course-button .btn-outline {
    border: 1px solid var(--gold-dark);
    color: var(--gold-dark);
    background: transparent;
}

.course-button .btn-outline:hover {
    background-color: var(--gold-dark);
    color: #fff;
}

/* No Courses Found */
.no-courses-found {
    padding: 50px 20px;
    background-color: #f9f9f9;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 30px;
}

.no-courses-found .icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: rgba(26, 95, 141, 0.1);
    border-radius: 50%;
    margin-bottom: 20px;
}

.no-courses-found .icon i {
    font-size: 36px;
    color: var(--gold-dark);
}

.no-courses-found h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
}

.no-courses-found p {
    font-size: 16px;
    color: #666;
    max-width: 500px;
    margin: 0 auto;
}

.no-courses-found .btn {
    margin-top: 20px;
    padding: 10px 25px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 5px;
    background-color: var(--gold-dark);
    color: #fff;
    border: none;
    transition: var(--transition-fast);
}

.no-courses-found .btn:hover {
    background-color: var(--gold-medium);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.2);
}

/* Pagination */
.pagination-area {
    margin-top: 50px;
    text-align: center;
}

.pagination {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #f9f9f9;
    border-radius: 50px;
    padding: 5px;
    box-shadow: var(--shadow-small);
}

.pagination .page-numbers {
    display: flex;
    align-items: center;
}

.pagination .page-numbers a,
.pagination .page-numbers span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin: 0 5px;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 600;
    color: #666;
    text-decoration: none;
    transition: var(--transition-fast);
}

.pagination .page-numbers a:hover {
    background-color: rgba(26, 95, 141, 0.1);
    color: var(--gold-dark);
}

.pagination .page-numbers span.current {
    background-color: var(--gold-dark);
    color: #fff;
}

.pagination .prev-page a,
.pagination .next-page a {
    background-color: rgba(26, 95, 141, 0.1);
    color: var(--gold-dark);
    width: auto;
    padding: 0 20px;
    border-radius: 20px;
}

.pagination .prev-page a:hover,
.pagination .next-page a:hover {
    background-color: var(--gold-dark);
    color: #fff;
}

/* Call to Action */
.call-action {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.call-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 0.2;
    z-index: 0;
}

.call-content {
    position: relative;
    z-index: 1;
    text-align: center;
    color: #fff;
}

.call-content span {
    display: inline-block;
    padding: 5px 15px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 20px;
}

.call-content h2 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #fff;
}

.call-content p {
    font-size: 18px;
    max-width: 700px;
    margin: 0 auto 30px;
    color: rgba(255, 255, 255, 0.9);
}

.call-content .btn {
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 30px;
    background-color: #fff;
    color: var(--gold-dark);
    border: none;
    transition: var(--transition-fast);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.call-content .btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Responsive Styles */
@media (max-width: 991px) {
    .breadcrumbs-content h1 {
        font-size: 36px;
    }

    .breadcrumbs-content p {
        font-size: 16px;
    }

    .call-content h2 {
        font-size: 30px;
    }
}

@media (max-width: 767px) {
    .breadcrumbs.overlay {
        padding: 80px 0 60px;
    }

    .page-icon {
        width: 60px;
        height: 60px;
    }

    .page-icon i {
        font-size: 28px;
    }

    .breadcrumbs-content h1 {
        font-size: 28px;
    }

    .breadcrumbs-content p {
        font-size: 14px;
    }

    .active-filters-wrapper {
        flex-direction: column;
        align-items: flex-start;
    }

    .active-filters-title {
        margin-bottom: 10px;
    }

    .clear-all-filters {
        margin-top: 10px;
    }

    .call-content h2 {
        font-size: 24px;
    }

    .call-content p {
        font-size: 16px;
    }
}

@media (max-width: 575px) {
    .breadcrumbs.overlay {
        padding: 60px 0 40px;
    }

    .breadcrumbs-content h1 {
        font-size: 24px;
    }

    .course-title {
        font-size: 18px;
    }

    .course-meta {
        flex-direction: column;
    }

    .meta-item {
        margin-right: 0;
    }

    .course-footer {
        flex-direction: column;
    }

    .course-price {
        margin-bottom: 15px;
    }

    .course-button {
        width: 100%;
    }

    .course-button .btn {
        width: 100%;
    }

    .call-content h2 {
        font-size: 20px;
    }

    .call-content p {
        font-size: 14px;
    }
}
