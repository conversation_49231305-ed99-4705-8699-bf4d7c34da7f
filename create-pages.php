<?php
// إنشاء الصفحات المطلوبة
require_once('../../../wp-load.php');

echo "<h1>Create Missing Pages</h1>";

// التحقق من الصلاحيات
if (!current_user_can('manage_options')) {
    echo "<p style='color: red;'>❌ You need admin privileges to create pages.</p>";
    echo "<p><a href='/wp-admin/'>Login to WordPress Admin</a></p>";
    exit;
}

$created_pages = array();
$errors = array();

// إنشاء صفحة Contact
$contact_page = get_page_by_path('contact');
if (!$contact_page) {
    $contact_page_data = array(
        'post_title'    => 'Contact',
        'post_content'  => 'Contact us for any questions or inquiries.',
        'post_status'   => 'publish',
        'post_type'     => 'page',
        'post_name'     => 'contact',
        'page_template' => 'page-contact.php'
    );
    
    $contact_id = wp_insert_post($contact_page_data);
    
    if ($contact_id && !is_wp_error($contact_id)) {
        // تعيين الـ template
        update_post_meta($contact_id, '_wp_page_template', 'page-contact.php');
        $created_pages[] = "Contact page created with ID: $contact_id";
    } else {
        $errors[] = "Failed to create Contact page: " . (is_wp_error($contact_id) ? $contact_id->get_error_message() : 'Unknown error');
    }
} else {
    echo "<p>✅ Contact page already exists (ID: {$contact_page->ID})</p>";
    
    // التحقق من الـ template
    $current_template = get_page_template_slug($contact_page->ID);
    if ($current_template !== 'page-contact.php') {
        update_post_meta($contact_page->ID, '_wp_page_template', 'page-contact.php');
        echo "<p>✅ Updated Contact page template to page-contact.php</p>";
    }
}

// إنشاء صفحة Trial Class
$trial_page = get_page_by_path('trial-class');
if (!$trial_page) {
    $trial_page_data = array(
        'post_title'    => 'Trial Class',
        'post_content'  => 'Book your free trial class today!',
        'post_status'   => 'publish',
        'post_type'     => 'page',
        'post_name'     => 'trial-class',
        'page_template' => 'page-trial-class.php'
    );
    
    $trial_id = wp_insert_post($trial_page_data);
    
    if ($trial_id && !is_wp_error($trial_id)) {
        // تعيين الـ template
        update_post_meta($trial_id, '_wp_page_template', 'page-trial-class.php');
        $created_pages[] = "Trial Class page created with ID: $trial_id";
    } else {
        $errors[] = "Failed to create Trial Class page: " . (is_wp_error($trial_id) ? $trial_id->get_error_message() : 'Unknown error');
    }
} else {
    echo "<p>✅ Trial Class page already exists (ID: {$trial_page->ID})</p>";
    
    // التحقق من الـ template
    $current_template = get_page_template_slug($trial_page->ID);
    if ($current_template !== 'page-trial-class.php') {
        update_post_meta($trial_page->ID, '_wp_page_template', 'page-trial-class.php');
        echo "<p>✅ Updated Trial Class page template to page-trial-class.php</p>";
    }
}

// عرض النتائج
if (!empty($created_pages)) {
    echo "<div style='background: #d4edda; color: #155724; padding: 10px; margin: 10px 0; border: 1px solid #c3e6cb;'>";
    echo "<h3>✅ Pages Created Successfully:</h3>";
    foreach ($created_pages as $message) {
        echo "<p>$message</p>";
    }
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0; border: 1px solid #f5c6cb;'>";
    echo "<h3>❌ Errors:</h3>";
    foreach ($errors as $error) {
        echo "<p>$error</p>";
    }
    echo "</div>";
}

// Flush rewrite rules
flush_rewrite_rules();
echo "<p>✅ Rewrite rules flushed!</p>";

// عرض الروابط
echo "<h2>Test the Pages:</h2>";
$contact_url = home_url('/contact/');
$trial_url = home_url('/trial-class/');

echo "<p><a href='$contact_url' target='_blank' style='background: #007cba; color: white; padding: 10px; text-decoration: none; margin: 5px;'>Test Contact Page</a></p>";
echo "<p><a href='$trial_url' target='_blank' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px;'>Test Trial Class Page</a></p>";

echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li>Click the test links above</li>";
echo "<li>If they work, try submitting the forms</li>";
echo "<li>If they still show 404, check the <a href='check-pages.php'>pages status</a></li>";
echo "<li>You might need to go to <a href='/wp-admin/options-permalink.php' target='_blank'>WordPress Admin → Settings → Permalinks</a> and click 'Save Changes'</li>";
echo "</ol>";
?>
