/**
 * Teachers Page JavaScript for Zajel Arabic Theme
 * Handles animations and interactive elements for the Teachers page
 */

(function($) {
    'use strict';

    // Check if jQuery is available
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded. Teachers page functionality requires jQuery.');
        return;
    }

    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize animations
        initAnimations();

        // Initialize counters
        initCounters();

        // Initialize filter
        initFilter();
    });

    /**
     * Initialize animations for page elements
     */
    function initAnimations() {
        // Add animation classes to elements as they appear in viewport
        $('.fade-in, .fade-in-up, .fade-in-down, .fade-in-left, .fade-in-right').each(function() {
            var $element = $(this);

            if(isElementInViewport($element)) {
                $element.addClass('animated');
            }

            $(window).on('scroll', function() {
                if(isElementInViewport($element)) {
                    $element.addClass('animated');
                }
            });
        });

        // Add animation to icons
        $('.feature-icon, .stats-icon').each(function() {
            var $icon = $(this);

            if(isElementInViewport($icon)) {
                setTimeout(function() {
                    $icon.addClass('pulse');
                }, 500);
            }

            $(window).on('scroll', function() {
                if(isElementInViewport($icon) && !$icon.hasClass('pulse')) {
                    setTimeout(function() {
                        $icon.addClass('pulse');
                    }, 500);
                }
            });
        });

        // Add rotation animation to specific icons
        $('.fas.fa-certificate, .fas.fa-graduation-cap, .fas.fa-globe').parent().each(function() {
            var $icon = $(this);

            if(isElementInViewport($icon)) {
                setTimeout(function() {
                    $icon.addClass('rotate');
                }, 1000);
            }

            $(window).on('scroll', function() {
                if(isElementInViewport($icon) && !$icon.hasClass('rotate')) {
                    setTimeout(function() {
                        $icon.addClass('rotate');
                    }, 1000);
                }
            });
        });

        // Add hover animations
        $('.teacher-card, .feature-box, .stats-item').hover(
            function() {
                $(this).addClass('hover');
            },
            function() {
                $(this).removeClass('hover');
            }
        );

        // Add smooth scroll for section links
        $('a[href^="#"]').on('click', function(e) {
            var target = $(this.hash);
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 70
                }, 800);
            }
        });

        // Add animation to section titles
        $('.section-title').each(function() {
            var $title = $(this);
            if(isElementInViewport($title)) {
                $title.addClass('animated');
            }

            $(window).on('scroll', function() {
                if(isElementInViewport($title)) {
                    $title.addClass('animated');
                }
            });
        });
    }

    /**
     * Initialize counters for stats section
     */
    function initCounters() {
        $('.stats-number').each(function() {
            var $this = $(this);
            var countTo = $this.attr('data-count');

            // Only start counter when element is in viewport
            if(isElementInViewport($this)) {
                startCounter($this, countTo);
            }

            // Start counter when scrolled into view
            $(window).on('scroll', function() {
                if(isElementInViewport($this) && !$this.hasClass('counted')) {
                    startCounter($this, countTo);
                }
            });
        });
    }

    /**
     * Start counter animation
     * @param {jQuery} $element - The element to animate
     * @param {number} countTo - The number to count to
     */
    function startCounter($element, countTo) {
        $element.addClass('counted');

        $({ countNum: 0 }).animate({
            countNum: countTo
        }, {
            duration: 2000,
            easing: 'swing',
            step: function() {
                $element.text(Math.floor(this.countNum));
            },
            complete: function() {
                $element.text(this.countNum);
            }
        });
    }

    /**
     * Initialize filter for teachers grid
     */
    function initFilter() {
        $('.filter-btn').on('click', function() {
            var filterValue = $(this).attr('data-filter');

            $('.filter-btn').removeClass('active');
            $(this).addClass('active');

            // Find matching teachers
            var $visibleTeachers;
            if (filterValue === 'all') {
                $visibleTeachers = $('.teacher-card');
            } else {
                // Find teachers that match the selected category
                $visibleTeachers = $('.teacher-card').filter(function() {
                    var dataCategory = $(this).attr('data-category');
                    if (dataCategory) {
                        var categories = dataCategory.split(' ');
                        return categories.indexOf(filterValue) !== -1;
                    }
                    return false;
                });
            }

            // Store the filter value as a data attribute on the body for the slider to use
            $('body').attr('data-current-filter', filterValue);

            // If we're using the slider
            if ($('.teachers-slider-wrapper').length > 0) {
                // Reset any previous styles first
                $('.teacher-card').parent().css({
                    'position': '',
                    'left': '',
                    'right': '',
                    'opacity': '',
                    'transform': '',
                    'transition': '',
                    'display': 'none'
                });

                // Mark visible teachers with a class
                $('.teacher-card').removeClass('filtered-visible');
                $visibleTeachers.addClass('filtered-visible');

                // Reset the slider to the first visible teacher
                if ($visibleTeachers.length > 0) {
                    // Find the index of the first visible teacher
                    var firstVisibleIndex = $('.teacher-card').index($visibleTeachers.first());

                    // Trigger a custom event that the slider will listen for
                    $(window).trigger('teacher-filter-changed', [firstVisibleIndex, $visibleTeachers]);
                } else {
                    // No teachers match the filter
                    if ($('.no-teachers-message').length === 0) {
                        $('.teachers-slider-wrapper').append('<div class="col-12 text-center no-teachers-message"><p>No teachers found for this category.</p></div>');
                    } else {
                        $('.no-teachers-message').show();
                    }
                }
            } else {
                // For non-slider layout
                // First hide all teacher cards
                $('.teacher-card').hide();

                // Show the matching teachers with animation
                $visibleTeachers.fadeIn(400);

                // Reorganize the visible teachers to avoid empty spaces
                reorganizeTeachers($visibleTeachers);
            }

            return false;
        });
    }

    /**
     * Reorganize teachers to avoid empty spaces after filtering
     * @param {jQuery} $visibleTeachers - The visible teacher elements
     */
    function reorganizeTeachers($visibleTeachers) {
        // If we're using the slider, let it handle the display
        if ($('.teachers-slider-wrapper').length > 0) {
            // Trigger a resize event to make the slider update
            $(window).trigger('resize');
            return;
        }

        // For regular grid layout without slider
        var $teachersRow = $('.teachers-slider-row');
        var $teacherCols = $teachersRow.children('.col-lg-4');

        // If no visible teachers, show the 'no teachers found' message
        if ($visibleTeachers.length === 0) {
            $teacherCols.hide();
            if ($('.no-teachers-message').length === 0) {
                $teachersRow.append('<div class="col-12 text-center no-teachers-message"><p>No teachers found for this category.</p></div>');
            } else {
                $('.no-teachers-message').show();
            }
            return;
        }

        // Hide the 'no teachers found' message if it exists
        $('.no-teachers-message').hide();

        // Hide all columns first
        $teacherCols.hide();

        // Show only columns that contain visible teachers
        $visibleTeachers.each(function() {
            $(this).closest('.col-lg-4').show();
        });

        // Apply improved layout for mobile view
        var $visibleCols = $teacherCols.filter(':visible');
        var isMobile = window.innerWidth < 768;

        // Reset any previous inline styles
        $visibleCols.css({
            'position': '',
            'left': '',
            'top': '',
            'width': '',
            'margin-left': '',
            'margin-right': ''
        });

        // Apply flex layout to parent row to avoid gaps
        $teachersRow.css({
            'display': 'flex',
            'flex-wrap': 'wrap',
            'justify-content': 'flex-start',
            'align-items': 'stretch'
        });

        // For mobile view, ensure teachers take full width and have proper spacing
        if (isMobile) {
            $visibleCols.css({
                'float': 'none',
                'display': 'block',
                'width': '100%',
                'margin-left': 'auto',
                'margin-right': 'auto',
                'margin-bottom': '20px'
            });

            // Add swipe hint for mobile if more than one teacher
            if ($visibleCols.length > 1 && $('.mobile-swipe-hint').length === 0) {
                $teachersRow.append('<div class="mobile-swipe-hint text-center"><p><i class="fas fa-arrow-left"></i> Swipe <i class="fas fa-arrow-right"></i></p></div>');

                // Hide the hint after a few seconds
                setTimeout(function() {
                    $('.mobile-swipe-hint').fadeOut();
                }, 3000);
            }
        } else {
            // For desktop view, use flex layout
            $visibleCols.css({
                'float': 'left',
                'display': 'block',
                'flex': '0 0 33.333333%',
                'max-width': '33.333333%'
            });
        }

        // Add touch swipe support for mobile view
        if (isMobile) {
            // Remove any existing handlers to prevent duplicates
            $teachersRow.off('touchstart touchmove touchend');

            var touchStartX = 0;
            var touchEndX = 0;
            var currentTeacherIndex = 0;

            $teachersRow.on('touchstart', function(e) {
                touchStartX = e.originalEvent.touches[0].clientX;
            });

            $teachersRow.on('touchend', function(e) {
                touchEndX = e.originalEvent.changedTouches[0].clientX;
                handleMobileSwipe();
            });

            function handleMobileSwipe() {
                var swipeThreshold = 50;
                var $visibleTeachers = $visibleCols;

                if (touchEndX < touchStartX - swipeThreshold) {
                    // Swipe left - next teacher
                    if (currentTeacherIndex < $visibleTeachers.length - 1) {
                        currentTeacherIndex++;
                        showCurrentTeacher();
                    }
                } else if (touchEndX > touchStartX + swipeThreshold) {
                    // Swipe right - previous teacher
                    if (currentTeacherIndex > 0) {
                        currentTeacherIndex--;
                        showCurrentTeacher();
                    }
                }
            }

            function showCurrentTeacher() {
                // Hide all teachers
                $visibleCols.hide();

                // Show only the current teacher
                $visibleCols.eq(currentTeacherIndex).fadeIn(300);
            }

            // Show the first teacher initially
            if ($visibleCols.length > 0) {
                showCurrentTeacher();
            }
        }
    }

    /**
     * Check if element is in viewport
     * @param {jQuery} $element - The element to check
     * @return {boolean} - True if element is in viewport
     */
    function isElementInViewport($element) {
        var elementTop = $element.offset().top;
        var elementBottom = elementTop + $element.outerHeight();
        var viewportTop = $(window).scrollTop();
        var viewportBottom = viewportTop + $(window).height();

        return elementBottom > viewportTop && elementTop < viewportBottom;
    }

})(jQuery);
