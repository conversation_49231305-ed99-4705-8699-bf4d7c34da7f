/**
 * Enhanced Sections JavaScript for Zajel Arabic Theme
 * Handles slider functionality and animations for Testimonials and Teachers sections
 */

(function($) {
    'use strict';

    // Check if jQuery is available
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded. Enhanced sections functionality requires jQuery.');
        return;
    }

    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize Testimonials Slider
        initTestimonialsSlider();

        // Initialize Teachers Slider
        initTeachersSlider();

        // Initialize animations
        initAnimations();
    });

    /**
     * Initialize Testimonials Slider
     */
    function initTestimonialsSlider() {
        // Variables
        var $slider = $('.testimonial-slider');
        var $container = $('.testimonial-slider-container');
        var $items = $('.single-testimonial');
        var $dots = $('.testimonial-dot');
        var $prev = $('.testimonial-prev');
        var $next = $('.testimonial-next');
        var itemWidth = $items.outerWidth(true);
        var visibleItems = getVisibleItems();
        var totalItems = $items.length;
        var currentIndex = 0;

        // Set container width
        updateContainerWidth();

        // Handle window resize
        $(window).on('resize', function() {
            visibleItems = getVisibleItems();
            itemWidth = $items.outerWidth(true);
            updateContainerWidth();
            goToSlide(currentIndex);
        });

        // Handle dot click
        $dots.on('click', function() {
            var index = $(this).index();
            goToSlide(index);
        });

        // Handle prev click
        $prev.on('click', function() {
            goToSlide(currentIndex - 1);
        });

        // Handle next click
        $next.on('click', function() {
            goToSlide(currentIndex + 1);
        });

        // Handle swipe
        $slider.on('touchstart', handleTouchStart);
        $slider.on('touchmove', handleTouchMove);

        // Touch variables
        var xDown = null;
        var yDown = null;

        // Update container width
        function updateContainerWidth() {
            $container.css('width', (totalItems * itemWidth) + 'px');
        }

        // Get number of visible items based on screen width
        function getVisibleItems() {
            if ($(window).width() < 768) {
                return 1;
            } else if ($(window).width() < 1200) {
                return 2;
            } else {
                return 3;
            }
        }

        // Go to slide
        function goToSlide(index) {
            // Handle edge cases
            if (index < 0) {
                index = 0;
            } else if (index > totalItems - visibleItems) {
                index = totalItems - visibleItems;
            }

            // Update current index
            currentIndex = index;

            // Update transform
            $container.css('transform', 'translateX(' + (-index * itemWidth) + 'px)');

            // Update dots
            $dots.removeClass('active');
            $dots.eq(index).addClass('active');

            // Update arrows
            $prev.toggleClass('disabled', index === 0);
            $next.toggleClass('disabled', index === totalItems - visibleItems);
        }

        // Handle touch start
        function handleTouchStart(evt) {
            xDown = evt.originalEvent.touches[0].clientX;
            yDown = evt.originalEvent.touches[0].clientY;
        }

        // Handle touch move
        function handleTouchMove(evt) {
            if (!xDown || !yDown) {
                return;
            }

            var xUp = evt.originalEvent.touches[0].clientX;
            var yUp = evt.originalEvent.touches[0].clientY;

            var xDiff = xDown - xUp;
            var yDiff = yDown - yUp;

            if (Math.abs(xDiff) > Math.abs(yDiff)) {
                if (xDiff > 0) {
                    // Swipe left
                    goToSlide(currentIndex + 1);
                } else {
                    // Swipe right
                    goToSlide(currentIndex - 1);
                }
            }

            xDown = null;
            yDown = null;
        }
    }

    /**
     * Initialize Teachers Slider
     */
    function initTeachersSlider() {
        // Variables
        var $slider = $('.teachers-slider');
        var $container = $('.teachers-slider-container');
        var $items = $('.single-team');
        var $dots = $('.teachers-dot');
        var $prev = $('.teachers-prev');
        var $next = $('.teachers-next');
        var itemWidth = $items.outerWidth(true);
        var visibleItems = getVisibleItems();
        var totalItems = $items.length;
        var currentIndex = 0;

        // Set container width
        updateContainerWidth();

        // Handle window resize
        $(window).on('resize', function() {
            visibleItems = getVisibleItems();
            itemWidth = $items.outerWidth(true);
            updateContainerWidth();
            goToSlide(currentIndex);
        });

        // Handle dot click
        $dots.on('click', function() {
            var index = $(this).index();
            goToSlide(index);
        });

        // Handle prev click
        $prev.on('click', function() {
            goToSlide(currentIndex - 1);
        });

        // Handle next click
        $next.on('click', function() {
            goToSlide(currentIndex + 1);
        });

        // Handle swipe
        $slider.on('touchstart', handleTouchStart);
        $slider.on('touchmove', handleTouchMove);

        // Touch variables
        var xDown = null;
        var yDown = null;

        // Update container width
        function updateContainerWidth() {
            $container.css('width', (totalItems * itemWidth) + 'px');
        }

        // Get number of visible items based on screen width
        function getVisibleItems() {
            if ($(window).width() < 992) {
                return 1;
            } else {
                return 2;
            }
        }

        // Go to slide
        function goToSlide(index) {
            // Handle edge cases
            if (index < 0) {
                index = 0;
            } else if (index > totalItems - visibleItems) {
                index = totalItems - visibleItems;
            }

            // Update current index
            currentIndex = index;

            // Update transform
            $container.css('transform', 'translateX(' + (-index * itemWidth) + 'px)');

            // Update dots
            $dots.removeClass('active');
            $dots.eq(index).addClass('active');

            // Update arrows
            $prev.toggleClass('disabled', index === 0);
            $next.toggleClass('disabled', index === totalItems - visibleItems);
        }

        // Handle touch start
        function handleTouchStart(evt) {
            xDown = evt.originalEvent.touches[0].clientX;
            yDown = evt.originalEvent.touches[0].clientY;
        }

        // Handle touch move
        function handleTouchMove(evt) {
            if (!xDown || !yDown) {
                return;
            }

            var xUp = evt.originalEvent.touches[0].clientX;
            var yUp = evt.originalEvent.touches[0].clientY;

            var xDiff = xDown - xUp;
            var yDiff = yDown - yUp;

            if (Math.abs(xDiff) > Math.abs(yDiff)) {
                if (xDiff > 0) {
                    // Swipe left
                    goToSlide(currentIndex + 1);
                } else {
                    // Swipe right
                    goToSlide(currentIndex - 1);
                }
            }

            xDown = null;
            yDown = null;
        }
    }

    /**
     * Initialize animations for page elements
     */
    function initAnimations() {
        // Add animation classes to elements as they appear in viewport
        $('.fade-in, .fade-in-up, .fade-in-down, .fade-in-left, .fade-in-right').each(function() {
            var $element = $(this);

            if(isElementInViewport($element)) {
                $element.addClass('animated');
            }

            $(window).on('scroll', function() {
                if(isElementInViewport($element)) {
                    $element.addClass('animated');
                }
            });
        });

        // Add hover animations
        $('.single-testimonial, .single-team').hover(
            function() {
                $(this).addClass('hover');
            },
            function() {
                $(this).removeClass('hover');
            }
        );

        // Add smooth scroll for section links
        $('a[href^="#"]').on('click', function(e) {
            var target = $(this.hash);
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 70
                }, 800);
            }
        });

        // Add animation to section titles
        $('.section-title').each(function() {
            var $title = $(this);
            if(isElementInViewport($title)) {
                $title.addClass('animated');
            }

            $(window).on('scroll', function() {
                if(isElementInViewport($title)) {
                    $title.addClass('animated');
                }
            });
        });
    }

    /**
     * Check if element is in viewport
     * @param {jQuery} $element - The element to check
     * @return {boolean} - True if element is in viewport
     */
    function isElementInViewport($element) {
        var elementTop = $element.offset().top;
        var elementBottom = elementTop + $element.outerHeight();
        var viewportTop = $(window).scrollTop();
        var viewportBottom = viewportTop + $(window).height();

        return elementBottom > viewportTop && elementTop < viewportBottom;
    }

})(jQuery);
