<?php
/**
 * Template Name: Blog Page
 * Template Post Type: page
 * 
 * The template for displaying the blog page
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Zajel_Arabic
 */

get_header();
?>

<!-- Start Hero Area -->
<section class="hero-area style2">
    <div class="hero-inner">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-12">
                    <div class="breadcrumbs-content text-center">
                        <div class="page-icon wow zoomIn" data-wow-delay=".2s">
                            <i class="lni lni-book"></i>
                        </div>
                        <h1 class="page-title wow fadeInUp" data-wow-delay=".4s">
                            <?php echo esc_html__('Our Blog', 'zajel'); ?>
                        </h1>
                        <p class="wow fadeInUp" data-wow-delay=".6s"><?php echo esc_html__('Explore our latest articles and insights on Arabic language, Islamic studies, and more', 'zajel'); ?></p>
                        <ul class="breadcrumb-nav wow fadeInUp" data-wow-delay=".8s">
                            <li><a href="<?php echo esc_url(home_url('/')); ?>"><i class="lni lni-home"></i> <?php echo esc_html__('Home', 'zajel'); ?></a></li>
                            <li><i class="lni lni-chevron-right"></i></li>
                            <li><?php echo esc_html__('Blog', 'zajel'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Hero Area -->

<!-- Start Blog Area -->
<section class="blog-area section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-md-12 col-12">
                <div class="blog-posts">
                    <?php
                    // Get the latest posts
                    $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
                    $args = array(
                        'post_type' => 'post',
                        'posts_per_page' => get_option('posts_per_page'),
                        'paged' => $paged
                    );
                    
                    $blog_query = new WP_Query($args);
                    
                    if ($blog_query->have_posts()) :
                        // Check if we have a sticky post to feature
                        $sticky_posts = get_option('sticky_posts');
                        if (!empty($sticky_posts) && $paged == 1) :
                            $sticky_args = array(
                                'post__in' => $sticky_posts,
                                'posts_per_page' => 1,
                                'ignore_sticky_posts' => 1
                            );
                            $sticky_query = new WP_Query($sticky_args);
                            
                            if ($sticky_query->have_posts()) :
                                while ($sticky_query->have_posts()) : $sticky_query->the_post();
                    ?>
                                <div class="featured-post wow fadeInUp" data-wow-delay=".2s">
                                    <article id="post-<?php the_ID(); ?>" <?php post_class('featured-post-content'); ?>>
                                        <div class="row">
                                            <div class="col-lg-6 col-md-6 col-12">
                                                <div class="post-thumbnail">
                                                    <a href="<?php the_permalink(); ?>">
                                                        <?php if (has_post_thumbnail()) : ?>
                                                            <?php the_post_thumbnail('large', array('alt' => get_the_title())); ?>
                                                        <?php else : ?>
                                                            <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/blog-placeholder.svg'); ?>" alt="<?php the_title_attribute(); ?>">
                                                        <?php endif; ?>
                                                    </a>
                                                    <div class="featured-badge">
                                                        <i class="lni lni-star-filled"></i> <?php esc_html_e('Featured', 'zajel'); ?>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-lg-6 col-md-6 col-12">
                                                <div class="post-content">
                                                    <div class="post-meta">
                                                        <div class="post-date">
                                                            <i class="lni lni-calendar"></i>
                                                            <a href="<?php echo esc_url(get_day_link(get_post_time('Y'), get_post_time('m'), get_post_time('j'))); ?>">
                                                                <?php echo get_the_date(); ?>
                                                            </a>
                                                        </div>

                                                        <?php
                                                        $categories = get_the_category();
                                                        if (!empty($categories)) :
                                                        ?>
                                                            <div class="post-category">
                                                                <i class="lni lni-folder"></i>
                                                                <a href="<?php echo esc_url(get_category_link($categories[0]->term_id)); ?>">
                                                                    <?php echo esc_html($categories[0]->name); ?>
                                                                </a>
                                                            </div>
                                                        <?php endif; ?>

                                                        <div class="post-comments">
                                                            <i class="lni lni-comments"></i>
                                                            <a href="<?php comments_link(); ?>">
                                                                <?php 
                                                                $comment_count = get_comments_number();
                                                                if ($comment_count == 0) {
                                                                    esc_html_e('No Comments', 'zajel');
                                                                } elseif ($comment_count == 1) {
                                                                    esc_html_e('1 Comment', 'zajel');
                                                                } else {
                                                                    echo sprintf(esc_html__('%d Comments', 'zajel'), $comment_count);
                                                                }
                                                                ?>
                                                            </a>
                                                        </div>
                                                    </div>

                                                    <h2 class="post-title">
                                                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                                    </h2>

                                                    <div class="post-excerpt">
                                                        <?php echo wp_trim_words(get_the_excerpt(), 30); ?>
                                                    </div>

                                                    <div class="read-more">
                                                        <a href="<?php the_permalink(); ?>">
                                                            <?php esc_html_e('Read Full Article', 'zajel'); ?> <i class="lni lni-arrow-right"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </article>
                                </div>
                    <?php
                                endwhile;
                                wp_reset_postdata();
                            endif;
                        endif;
                    ?>
                        
                        <div class="row">
                            <?php
                            /* Start the Loop */
                            $delay = 0.2;
                            $post_counter = 0;
                            
                            // If we displayed a sticky post, exclude it from the main query
                            if (!empty($sticky_posts) && isset($sticky_query) && $sticky_query->have_posts() && $paged == 1) {
                                // Skip the sticky post if it's in the main loop
                                while ($blog_query->have_posts()) :
                                    $blog_query->the_post();
                                    if (in_array(get_the_ID(), $sticky_posts)) {
                                        continue;
                                    }
                                    $post_counter++;
                            ?>
                                    <div class="col-lg-6 col-md-6 col-12 wow fadeInUp" data-wow-delay="<?php echo esc_attr($delay); ?>s">
                                        <article id="post-<?php the_ID(); ?>" <?php post_class('archive-post'); ?>>
                                            <div class="post-thumbnail">
                                                <a href="<?php the_permalink(); ?>">
                                                    <?php if (has_post_thumbnail()) : ?>
                                                        <?php the_post_thumbnail('medium_large', array('alt' => get_the_title())); ?>
                                                    <?php else : ?>
                                                        <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/blog-placeholder.svg'); ?>" alt="<?php the_title_attribute(); ?>">
                                                    <?php endif; ?>
                                                </a>
                                            </div>
                                            <div class="post-content">
                                                <div class="post-meta">
                                                    <div class="post-date">
                                                        <i class="lni lni-calendar"></i>
                                                        <a href="<?php echo esc_url(get_day_link(get_post_time('Y'), get_post_time('m'), get_post_time('j'))); ?>">
                                                            <?php echo get_the_date(); ?>
                                                        </a>
                                                    </div>

                                                    <?php
                                                    $categories = get_the_category();
                                                    if (!empty($categories)) :
                                                    ?>
                                                        <div class="post-category">
                                                            <i class="lni lni-folder"></i>
                                                            <a href="<?php echo esc_url(get_category_link($categories[0]->term_id)); ?>">
                                                                <?php echo esc_html($categories[0]->name); ?>
                                                            </a>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>

                                                <h2 class="post-title">
                                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                                </h2>

                                                <div class="post-excerpt">
                                                    <?php echo wp_trim_words(get_the_excerpt(), 20); ?>
                                                </div>

                                                <div class="read-more">
                                                    <a href="<?php the_permalink(); ?>">
                                                        <?php esc_html_e('Read More', 'zajel'); ?> <i class="lni lni-arrow-right"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </article>
                                    </div>
                            <?php
                                    $delay += 0.2;
                                    if ($delay > 0.8) $delay = 0.2;
                                endwhile;
                            } else {
                                // No sticky post was displayed, show all posts
                                while ($blog_query->have_posts()) :
                                    $blog_query->the_post();
                                    $post_counter++;
                            ?>
                                    <div class="col-lg-6 col-md-6 col-12 wow fadeInUp" data-wow-delay="<?php echo esc_attr($delay); ?>s">
                                        <article id="post-<?php the_ID(); ?>" <?php post_class('archive-post'); ?>>
                                            <div class="post-thumbnail">
                                                <a href="<?php the_permalink(); ?>">
                                                    <?php if (has_post_thumbnail()) : ?>
                                                        <?php the_post_thumbnail('medium_large', array('alt' => get_the_title())); ?>
                                                    <?php else : ?>
                                                        <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/blog-placeholder.svg'); ?>" alt="<?php the_title_attribute(); ?>">
                                                    <?php endif; ?>
                                                </a>
                                            </div>
                                            <div class="post-content">
                                                <div class="post-meta">
                                                    <div class="post-date">
                                                        <i class="lni lni-calendar"></i>
                                                        <a href="<?php echo esc_url(get_day_link(get_post_time('Y'), get_post_time('m'), get_post_time('j'))); ?>">
                                                            <?php echo get_the_date(); ?>
                                                        </a>
                                                    </div>

                                                    <?php
                                                    $categories = get_the_category();
                                                    if (!empty($categories)) :
                                                    ?>
                                                        <div class="post-category">
                                                            <i class="lni lni-folder"></i>
                                                            <a href="<?php echo esc_url(get_category_link($categories[0]->term_id)); ?>">
                                                                <?php echo esc_html($categories[0]->name); ?>
                                                            </a>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>

                                                <h2 class="post-title">
                                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                                </h2>

                                                <div class="post-excerpt">
                                                    <?php echo wp_trim_words(get_the_excerpt(), 20); ?>
                                                </div>

                                                <div class="read-more">
                                                    <a href="<?php the_permalink(); ?>">
                                                        <?php esc_html_e('Read More', 'zajel'); ?> <i class="lni lni-arrow-right"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </article>
                                    </div>
                            <?php
                                    $delay += 0.2;
                                    if ($delay > 0.8) $delay = 0.2;
                                endwhile;
                            }
                            ?>
                        </div>
                        
                        <?php if ($post_counter > 0) : ?>
                            <div class="pagination-area wow fadeInUp" data-wow-delay=".2s">
                                <?php
                                $big = 999999999; // need an unlikely integer
                                echo paginate_links(array(
                                    'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                                    'format' => '?paged=%#%',
                                    'current' => max(1, get_query_var('paged')),
                                    'total' => $blog_query->max_num_pages,
                                    'prev_text' => '<i class="lni lni-arrow-left"></i>',
                                    'next_text' => '<i class="lni lni-arrow-right"></i>',
                                ));
                                ?>
                            </div>
                        <?php endif; ?>
                        
                    <?php else : ?>
                        
                        <?php get_template_part('template-parts/content', 'none'); ?>
                        
                    <?php 
                    endif;
                    wp_reset_postdata();
                    ?>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-12 col-12">
                <div class="blog-sidebar wow fadeInUp" data-wow-delay=".4s">
                    <?php get_sidebar(); ?>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Blog Area -->

<?php
get_footer();
