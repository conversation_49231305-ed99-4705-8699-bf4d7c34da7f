<?php
/**
 * The template for displaying the footer
 */
?>
<!-- Start Footer Area -->
<footer class="footer style2">
    <!-- Start Middle Top -->
    <div class="footer-middle">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-4 col-12">
                    <div class="f-about single-footer">
                        <div class="logo">
                            <a href="<?php echo esc_url(home_url('/')); ?>">
                                <?php
                                if (has_custom_logo()) {
                                    the_custom_logo();
                                } else {
                                    ?>
                                    <span class="default-logo">
                                        <i class="fa-solid fa-dove"></i>
                                        Zajel Arabic
                                    </span>
                                    <?php
                                }
                                ?>
                            </a>
                        </div>
                        <p><?php echo esc_html(get_theme_mod('zajel_footer_about', 'Zajel Arabic Institute is dedicated to teaching Arabic language, Quran, and Islamic studies to non-Arabic speakers worldwide through interactive online classes with qualified native Arabic-speaking teachers.')); ?></p>
                        <div class="footer-social">
                            <ul>
                                <?php
                                $facebook_url = get_theme_mod('zajel_facebook_url', '#');
                                $twitter_url = get_theme_mod('zajel_twitter_url', '#');
                                $instagram_url = get_theme_mod('zajel_instagram_url', '#');
                                $youtube_url = get_theme_mod('zajel_youtube_url', '#');
                                $whatsapp_url = get_theme_mod('zajel_whatsapp_url', 'https://wa.me/1234567890');
                                ?>
                                <?php if ($facebook_url) : ?>
                                    <li><a href="<?php echo esc_url($facebook_url); ?>"><i class="lni lni-facebook-filled"></i></a></li>
                                <?php endif; ?>
                                <?php if ($twitter_url) : ?>
                                    <li><a href="<?php echo esc_url($twitter_url); ?>"><i class="lni lni-twitter-original"></i></a></li>
                                <?php endif; ?>
                                <?php if ($instagram_url) : ?>
                                    <li><a href="<?php echo esc_url($instagram_url); ?>"><i class="lni lni-instagram-original"></i></a></li>
                                <?php endif; ?>
                                <?php if ($youtube_url) : ?>
                                    <li><a href="<?php echo esc_url($youtube_url); ?>"><i class="lni lni-youtube"></i></a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-8 col-md-8 col-12">
                    <div class="row">
                        <div class="col-lg-4 col-md-6 col-12">
                            <div class="single-footer sm-custom-border f-link">
                                <h3><?php echo esc_html(get_theme_mod('zajel_footer_pages_title', 'Quick Links')); ?></h3>
                                <?php
                                wp_nav_menu(array(
                                    'theme_location' => 'footer-pages',
                                    'menu_class' => '',
                                    'container' => false,
                                    'fallback_cb' => 'zajel_default_footer_pages_menu',
                                ));

                                // Fallback menu if no menu is assigned
                                function zajel_default_footer_pages_menu() {
                                    echo '<ul>';
                                    echo '<li><a href="' . esc_url(home_url('/')) . '">Home</a></li>';
                                    echo '<li><a href="' . esc_url(home_url('/about-us')) . '">About Us</a></li>';
                                    echo '<li><a href="' . esc_url(home_url('/trial-class')) . '">Trial Class</a></li>';
                                    echo '<li><a href="' . esc_url(home_url('/contact')) . '">Contact Us</a></li>';
                                    echo '</ul>';
                                }
                                ?>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 col-12">
                            <div class="single-footer sm-custom-border f-link">
                                <h3><?php echo esc_html(get_theme_mod('zajel_footer_courses_title', 'Our Courses')); ?></h3>
                                <?php
                                // Get all courses
                                $courses = get_posts(array(
                                    'post_type' => 'course',
                                    'posts_per_page' => 4, // Limit to 4 courses in footer
                                    'orderby' => 'title',
                                    'order' => 'ASC',
                                ));

                                if (!empty($courses)) {
                                    echo '<ul>';

                                    // Display each course as a menu item
                                    foreach ($courses as $course) {
                                        $course_url = str_replace('/courses/', '/course/', get_permalink($course->ID));
                                        echo '<li><a href="' . esc_url($course_url) . '">' . esc_html($course->post_title) . '</a></li>';
                                    }

                                    // Add a "View All Courses" link if there are more than 4 courses
                                    $total_courses = wp_count_posts('course')->publish;
                                    if ($total_courses > 4) {
                                        echo '<li><a href="' . esc_url(home_url('/courses/')) . '">View All Courses</a></li>';
                                    }

                                    echo '</ul>';
                                } else {
                                    // Fallback if no courses are found
                                    echo '<ul>';
                                    echo '<li><a href="#">Quran Memorization</a></li>';
                                    echo '<li><a href="#">Tajweed Rules</a></li>';
                                    echo '<li><a href="#">Arabic Language</a></li>';
                                    echo '<li><a href="#">Islamic Studies</a></li>';
                                    echo '</ul>';
                                }
                                ?>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-12 col-12">
                            <div class="single-footer footer-newsletter">
                                <h3><?php echo esc_html(get_theme_mod('zajel_footer_newsletter_title', 'Newsletter')); ?></h3>
                                <p><?php echo esc_html(get_theme_mod('zajel_footer_newsletter_description', 'Subscribe to our newsletter to receive updates on new courses, special offers, and educational content.')); ?></p>
                                <?php
                                $newsletter_form_shortcode = get_theme_mod('zajel_newsletter_form_shortcode', '[contact-form-7 id="124" title="Newsletter Form"]');
                                echo do_shortcode($newsletter_form_shortcode);
                                ?>
                                <!-- Fallback newsletter form if shortcode doesn't work -->
                                <?php if (!has_shortcode($newsletter_form_shortcode, 'contact-form-7')) : ?>
                                <form class="newsletter-form" action="#" method="post">
                                    <input type="email" name="email" placeholder="Your email address" required>
                                    <button type="submit"><i class="lni lni-envelope"></i></button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Footer Middle -->
    <!-- Start Footer Bottom -->
    <div class="footer-bottom">
        <div class="container">
            <div class="inner">
                <div class="row">
                    <div class="col-12">
                        <div class="left">
                            <p>&copy; <?php echo date('Y'); ?> Zajel Arabic Institute. All Rights Reserved. </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Footer Bottom -->
</footer>
<!--/ End Footer Area -->

<!-- WhatsApp Floating Button -->
<a href="<?php echo esc_url(get_theme_mod('zajel_whatsapp_url', 'https://wa.me/201220479882')); ?>" class="whatsapp-float" target="_blank" title="Contact us on WhatsApp">
    <i class="lni lni-whatsapp"></i>
</a>

<!-- Scroll Top -->
<a href="#" class="scroll-top">
    <i class="lni lni-chevron-up"></i>
</a>

<!-- Footer Scripts are loaded from footer-scripts.js -->

<?php wp_footer(); ?>
</body>
</html>