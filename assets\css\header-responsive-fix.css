/* Header Responsive Fix for Zajel Arabic Theme */
/* Ensures desktop header remains unchanged while mobile gets sidebar menu */

:root {
    /* Site Colors */
    --primary-blue: #1a5f8d;
    --primary-blue-light: #2980b9;
    --primary-blue-dark: #03355c;
    --accent-gold: #f39c12;
    --white: #ffffff;
    --dark-gray: #343a40;
}

/*======================================
    Desktop Header Styles (Unchanged)
========================================*/
@media (min-width: 992px) {
    /* Ensure header background is white */
    .header.navbar-area {
        background-color: var(--white) !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    /* Logo styling */
    .navbar-brand {
        padding: 15px 0;
    }
    
    .default-logo {
        display: flex;
        align-items: center;
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--dark-gray);
    }
    
    .default-logo i {
        color: var(--accent-gold);
        margin-right: 10px;
        font-size: 1.8rem;
    }
    
    /* Desktop Navigation */
    .navbar-nav {
        display: flex;
        align-items: center;
        margin: 0;
        padding: 0;
        list-style: none;
    }
    
    .navbar-nav .nav-item {
        margin-left: 30px;
        position: relative;
    }
    
    .navbar-nav .nav-item:first-child {
        margin-left: 0;
    }
    
    .navbar-nav .nav-item a {
        padding: 30px 0;
        display: block;
        color: var(--dark-gray);
        font-weight: 600;
        text-decoration: none;
        position: relative;
        transition: all 0.3s ease;
    }
    
    .navbar-nav .nav-item a:hover,
    .navbar-nav .nav-item a.active {
        color: var(--primary-blue);
    }
    
    /* Hover underline effect */
    .navbar-nav .nav-item a::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 2px;
        background: var(--primary-blue);
        transition: width 0.3s ease;
    }
    
    .navbar-nav .nav-item a:hover::after,
    .navbar-nav .nav-item a.active::after {
        width: 100%;
    }
    
    /* Desktop Dropdown Menus */
    .navbar-nav .nav-item.dropdown {
        position: relative;
    }
    
    .navbar-nav .nav-item .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        background: var(--white);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border: none;
        border-radius: 8px;
        padding: 10px 0;
        min-width: 200px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(10px);
        transition: all 0.3s ease;
        z-index: 1000;
    }
    
    .navbar-nav .nav-item:hover .dropdown-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    
    .navbar-nav .nav-item .dropdown-menu a {
        padding: 10px 20px;
        color: var(--dark-gray);
        font-size: 14px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .navbar-nav .nav-item .dropdown-menu a:last-child {
        border-bottom: none;
    }
    
    .navbar-nav .nav-item .dropdown-menu a:hover {
        background: #f8f9fa;
        color: var(--primary-blue);
    }
    
    /* Trial Class Button */
    .trial-class-button {
        margin-left: 30px;
    }
    
    .trial-class-btn {
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-light));
        color: var(--white);
        padding: 12px 25px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        display: inline-block;
        box-shadow: 0 2px 10px rgba(26, 95, 141, 0.2);
    }
    
    .trial-class-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(26, 95, 141, 0.3);
        color: var(--white);
    }
    
    /* Hide mobile elements on desktop */
    .mobile-menu-btn,
    .mobile-menu-overlay,
    .mobile-sidebar-menu {
        display: none !important;
    }
    
    /* Ensure desktop navigation is visible */
    .navbar-collapse.sub-menu-bar {
        display: flex !important;
        position: static !important;
        width: auto !important;
        height: auto !important;
        background: transparent !important;
        box-shadow: none !important;
        padding: 0 !important;
        margin: 0 !important;
        overflow: visible !important;
    }
}

/*======================================
    Mobile Styles (Show Sidebar Menu)
========================================*/
@media (max-width: 991px) {
    /* Show mobile menu button */
    .mobile-menu-btn {
        display: flex !important;
    }
    
    /* Hide desktop navigation */
    .navbar-collapse.sub-menu-bar {
        display: none !important;
    }
    
    /* Adjust header padding for mobile */
    .nav-inner {
        padding: 15px 0;
    }
    
    /* Mobile logo adjustments */
    .navbar-brand {
        padding: 10px 0;
    }
    
    .default-logo {
        font-size: 1.3rem;
    }
    
    .default-logo i {
        font-size: 1.6rem;
    }
}

/*======================================
    Tablet Specific Adjustments
========================================*/
@media (max-width: 991px) and (min-width: 768px) {
    .mobile-sidebar-menu {
        width: 350px;
        left: -350px;
    }
    
    .mobile-menu-header {
        padding: 25px;
    }
    
    .mobile-logo {
        font-size: 20px;
    }
    
    .departments-btn {
        padding: 18px 25px;
        font-size: 17px;
    }
    
    .mobile-nav-link {
        padding: 18px 25px;
        font-size: 16px;
    }
}

/*======================================
    Small Mobile Adjustments
========================================*/
@media (max-width: 575px) {
    .nav-inner {
        padding: 12px 0;
    }
    
    .navbar-brand {
        padding: 8px 0;
    }
    
    .default-logo {
        font-size: 1.2rem;
    }
    
    .default-logo i {
        font-size: 1.4rem;
        margin-right: 8px;
    }
    
    .mobile-menu-btn .toggler-icon {
        width: 22px;
        height: 2px;
    }
}

/*======================================
    Header Sticky Behavior
========================================*/
.header.navbar-area.sticky {
    background-color: var(--white) !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.15);
}

.header.navbar-area.sticky .navbar-brand img {
    max-height: 45px;
}

/*======================================
    Z-Index Management
========================================*/
.header.navbar-area {
    z-index: 999;
}

.mobile-sidebar-menu {
    z-index: 1000;
}

.mobile-menu-overlay {
    z-index: 999;
}

.mobile-menu-btn {
    z-index: 1001;
}

/*======================================
    Print Styles
========================================*/
@media print {
    .header.navbar-area {
        display: none !important;
    }
    
    .mobile-sidebar-menu,
    .mobile-menu-overlay {
        display: none !important;
    }
}

/*======================================
    Accessibility Improvements
========================================*/
/* Focus styles for keyboard navigation */
.navbar-nav .nav-item a:focus,
.trial-class-btn:focus,
.mobile-menu-btn:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .header.navbar-area {
        border-bottom: 2px solid var(--dark-gray);
    }
    
    .navbar-nav .nav-item a {
        border: 1px solid transparent;
    }
    
    .navbar-nav .nav-item a:focus {
        border-color: var(--primary-blue);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .navbar-nav .nav-item a,
    .trial-class-btn,
    .navbar-nav .nav-item a::after {
        transition: none !important;
    }
}

/*======================================
    Browser Compatibility
========================================*/
/* Safari specific fixes */
@supports (-webkit-appearance: none) {
    .header.navbar-area {
        -webkit-backdrop-filter: blur(10px);
    }
}

/* Firefox specific fixes */
@-moz-document url-prefix() {
    .navbar-nav .nav-item a {
        -moz-transition: all 0.3s ease;
    }
}

/*======================================
    Performance Optimizations
========================================*/
.header.navbar-area {
    will-change: transform;
    contain: layout style paint;
}

.navbar-nav .nav-item a {
    will-change: color;
}

.trial-class-btn {
    will-change: transform;
}
