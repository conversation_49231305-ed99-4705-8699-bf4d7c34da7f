jQuery(document).ready(function($) {
    // Initialize variables
    var testimonialContainer = $('.testimonial-container');
    var testimonialItems = $('.testimonial-container .col-lg-4');
    var totalItems = testimonialItems.length;
    var currentIndex = 0;

    // Function to determine items per view based on screen size
    function getItemsPerView() {
        if (window.innerWidth < 768) {
            return 1; // Show 1 item on mobile
        } else if (window.innerWidth < 992) {
            return 2; // Show 2 items on tablets
        } else {
            return 3; // Show 3 items on desktop
        }
    }

    // Get initial items per view
    var itemsPerView = getItemsPerView();

    // Only setup if we have more than itemsPerView testimonials
    if (totalItems > 1) {
        // Create navigation dots
        var dotsHtml = '';
        for (var i = 0; i <= totalItems - itemsPerView; i++) {
            var activeClass = (i === 0) ? 'active' : '';
            dotsHtml += '<span class="testimonial-dot ' + activeClass + '" data-index="' + i + '" style="width: ' + (i === 0 ? '12px' : '10px') + '; height: ' + (i === 0 ? '12px' : '10px') + '; background-color: ' + (i === 0 ? '#1a5f8d' : 'rgba(3, 53, 92, 0.3)') + '; border-radius: 50%; cursor: pointer; transition: all 0.3s ease; margin: 0 4px;"></span>';
        }

        // Create navigation HTML
        var navigationHtml = '<div class="testimonial-navigation" style="width: 100%; display: flex; justify-content: center; align-items: center; margin-top: 20px;">';
        navigationHtml += '<div class="testimonial-arrow prev" style="width: 40px; height: 40px; background-color: #fff; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; cursor: pointer; box-shadow: 0 3px 10px rgba(0,0,0,0.1); transition: all 0.3s ease;"><i class="lni lni-chevron-left" style="color: #1a5f8d;"></i></div>';
        navigationHtml += '<div class="testimonial-dots" style="display: flex; gap: 8px; align-items: center;">' + dotsHtml + '</div>';
        navigationHtml += '<div class="testimonial-arrow next" style="width: 40px; height: 40px; background-color: #fff; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 15px; cursor: pointer; box-shadow: 0 3px 10px rgba(0,0,0,0.1); transition: all 0.3s ease;"><i class="lni lni-chevron-right" style="color: #1a5f8d;"></i></div>';
        navigationHtml += '</div>';

        // Append navigation to container
        testimonialContainer.append(navigationHtml);

        // Initial setup - show first set of testimonials
        updateVisibleTestimonials();

        // Add touch swipe functionality - SIMPLIFIED VERSION
        var touchStartX = 0;
        var touchEndX = 0;

        // Add direct event listeners to the testimonial container
        testimonialContainer.on('touchstart', function(e) {
            touchStartX = e.originalEvent.touches[0].clientX;
            console.log('Testimonial touchstart detected, X:', touchStartX);
        });

        testimonialContainer.on('touchend', function(e) {
            touchEndX = e.originalEvent.changedTouches[0].clientX;
            console.log('Testimonial touchend detected, X:', touchEndX);
            handleSwipe();
        });

        // Also add direct event listeners to each testimonial item
        testimonialItems.on('touchstart', function(e) {
            touchStartX = e.originalEvent.touches[0].clientX;
            console.log('Testimonial item touchstart detected, X:', touchStartX);
            e.stopPropagation();
        });

        testimonialItems.on('touchend', function(e) {
            touchEndX = e.originalEvent.changedTouches[0].clientX;
            console.log('Testimonial item touchend detected, X:', touchEndX);
            handleSwipe();
            e.stopPropagation();
        });

        // Add mouse events for desktop testing
        testimonialContainer.on('mousedown', function(e) {
            touchStartX = e.pageX;
            console.log('Testimonial mousedown detected, X:', touchStartX);
        });

        testimonialContainer.on('mouseup', function(e) {
            touchEndX = e.pageX;
            console.log('Testimonial mouseup detected, X:', touchEndX);
            handleSwipe();
        });

        function handleSwipe() {
            var swipeThreshold = 50; // Minimum distance to be considered a swipe
            var swipeDistance = touchEndX - touchStartX;

            console.log('Swipe distance:', swipeDistance);

            if (swipeDistance < -swipeThreshold) {
                // Swipe left - next
                console.log('Swipe LEFT detected');
                if (currentIndex < totalItems - itemsPerView) {
                    currentIndex++;
                    updateVisibleTestimonials();
                }
            } else if (swipeDistance > swipeThreshold) {
                // Swipe right - previous
                console.log('Swipe RIGHT detected');
                if (currentIndex > 0) {
                    currentIndex--;
                    updateVisibleTestimonials();
                }
            }
        }

        // No swipe hint needed

        // Function to update which testimonials are visible
        function updateVisibleTestimonials() {
            // Hide all testimonials
            testimonialItems.hide();

            // Show the current set of testimonials
            for (var i = 0; i < itemsPerView; i++) {
                if (currentIndex + i < totalItems) {
                    testimonialItems.eq(currentIndex + i).show();
                }
            }

            // Update dots
            $('.testimonial-dot').removeClass('active').css({
                'width': '10px',
                'height': '10px',
                'background-color': 'rgba(3, 53, 92, 0.3)'
            });

            $('.testimonial-dot[data-index="' + currentIndex + '"]').addClass('active').css({
                'width': '12px',
                'height': '12px',
                'background-color': '#1a5f8d'
            });

            // Update arrow states
            if (currentIndex === 0) {
                $('.testimonial-arrow.prev').css('opacity', '0.5');
            } else {
                $('.testimonial-arrow.prev').css('opacity', '1');
            }

            if (currentIndex >= totalItems - itemsPerView) {
                $('.testimonial-arrow.next').css('opacity', '0.5');
            } else {
                $('.testimonial-arrow.next').css('opacity', '1');
            }
        }

        // Handle dot click
        $(document).on('click', '.testimonial-dot', function() {
            currentIndex = $(this).data('index');
            updateVisibleTestimonials();
        });

        // Handle arrow clicks - move one testimonial at a time
        $(document).on('click', '.testimonial-arrow.prev', function() {
            if (currentIndex > 0) {
                currentIndex--;
                updateVisibleTestimonials();
            }
        });

        $(document).on('click', '.testimonial-arrow.next', function() {
            if (currentIndex < totalItems - itemsPerView) {
                currentIndex++;
                updateVisibleTestimonials();
            }
        });

        // Handle window resize
        $(window).on('resize', function() {
            var newItemsPerView = getItemsPerView();

            // Only update if items per view has changed
            if (newItemsPerView !== itemsPerView) {
                // Remove existing navigation
                $('.testimonial-navigation').remove();

                // Update variables
                itemsPerView = newItemsPerView;
                currentIndex = 0; // Reset to first item

                // Recreate dots
                dotsHtml = '';
                for (var i = 0; i <= totalItems - itemsPerView; i++) {
                    var activeClass = (i === 0) ? 'active' : '';
                    dotsHtml += '<span class="testimonial-dot ' + activeClass + '" data-index="' + i + '" style="width: ' + (i === 0 ? '12px' : '10px') + '; height: ' + (i === 0 ? '12px' : '10px') + '; background-color: ' + (i === 0 ? '#1a5f8d' : 'rgba(3, 53, 92, 0.3)') + '; border-radius: 50%; cursor: pointer; transition: all 0.3s ease; margin: 0 4px;"></span>';
                }

                // Recreate navigation
                navigationHtml = '<div class="testimonial-navigation" style="width: 100%; display: flex; justify-content: center; align-items: center; margin-top: 20px;">';
                navigationHtml += '<div class="testimonial-arrow prev" style="width: 40px; height: 40px; background-color: #fff; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; cursor: pointer; box-shadow: 0 3px 10px rgba(0,0,0,0.1); transition: all 0.3s ease;"><i class="lni lni-chevron-left" style="color: #1a5f8d;"></i></div>';
                navigationHtml += '<div class="testimonial-dots" style="display: flex; gap: 8px; align-items: center;">' + dotsHtml + '</div>';
                navigationHtml += '<div class="testimonial-arrow next" style="width: 40px; height: 40px; background-color: #fff; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-left: 15px; cursor: pointer; box-shadow: 0 3px 10px rgba(0,0,0,0.1); transition: all 0.3s ease;"><i class="lni lni-chevron-right" style="color: #1a5f8d;"></i></div>';
                navigationHtml += '</div>';

                // Append navigation to container
                testimonialContainer.append(navigationHtml);

                // Update visible testimonials
                updateVisibleTestimonials();
            }
        });
    }
});
