<?php
/**
 * Template Name: Teachers Page
 *
 * The template for displaying the Teachers page
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Zajola
 */

get_header();
?>

<!-- Start Breadcrumbs -->
<div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <div class="page-icon wow zoomIn" data-wow-delay=".2s">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <h1 class="page-title wow fadeInUp" data-wow-delay=".4s"><?php the_title(); ?></h1>
                    <p class="wow fadeInUp" data-wow-delay=".6s">Learn from our qualified and experienced teachers who are dedicated to helping you achieve your Arabic language and Quranic education goals.</p>
                </div>
                <ul class="breadcrumb-nav wow fadeInUp" data-wow-delay=".8s">
                    <li><a href="<?php echo esc_url(home_url('/')); ?>"><i class="fas fa-home"></i> Home</a></li>
                    <li><i class="fas fa-angle-right"></i></li>
                    <li><?php the_title(); ?></li>
                </ul>
            </div>
        </div>

        <!-- Animated Elements -->
        <div class="animated-circle circle-1 wow fadeIn" data-wow-delay=".3s"></div>
        <div class="animated-circle circle-2 wow fadeIn" data-wow-delay=".5s"></div>
        <div class="animated-square square-1 wow fadeIn" data-wow-delay=".7s"></div>
        <div class="animated-square square-2 wow fadeIn" data-wow-delay=".9s"></div>
        <div class="animated-circle circle-3 pulse" data-wow-delay="1.1s"></div>
    </div>
</div>
<!-- End Breadcrumbs -->

<!-- Start Teachers Stats Section -->
<section class="teachers-stats">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 col-12">
                <div class="stats-item fade-in-up">
                    <div class="stats-icon">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <h3 class="stats-number" data-count="<?php echo esc_attr(get_theme_mod('zajel_teachers_count', '20')); ?>">0</h3>
                    <p class="stats-text"><?php echo esc_html__('Expert Teachers', 'zajel'); ?></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
                <div class="stats-item fade-in-up delay-1">
                    <div class="stats-icon">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <h3 class="stats-number" data-count="<?php echo esc_attr(get_theme_mod('zajel_students_count', '500')); ?>">0</h3>
                    <p class="stats-text"><?php echo esc_html__('Happy Students', 'zajel'); ?></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
                <div class="stats-item fade-in-up delay-2">
                    <div class="stats-icon">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <h3 class="stats-number" data-count="<?php echo esc_attr(get_theme_mod('zajel_courses_count', '30')); ?>">0</h3>
                    <p class="stats-text"><?php echo esc_html__('Courses Available', 'zajel'); ?></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-12">
                <div class="stats-item fade-in-up delay-3">
                    <div class="stats-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <h3 class="stats-number" data-count="<?php echo esc_attr(get_theme_mod('zajel_experience_years', '10')); ?>">0</h3>
                    <p class="stats-text"><?php echo esc_html__('Years Experience', 'zajel'); ?></p>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Teachers Stats Section -->

<!-- Start Teachers Grid Section -->
<section id="teachers-grid" class="teachers-grid">
    <div class="container">
        <div class="section-title">
            <span class="fade-in"><?php echo esc_html__('Our Teachers', 'zajel'); ?></span>
            <h2 class="fade-in-up delay-1"><?php echo esc_html__('Meet Our Expert Instructors', 'zajel'); ?></h2>
            <p class="fade-in-up delay-2"><?php echo esc_html__('Learn from our qualified and experienced teachers who are dedicated to helping you achieve your Arabic language and Quranic education goals.', 'zajel'); ?></p>
        </div>

        <div class="teachers-filter text-center mb-5 wow fadeInUp" data-wow-delay=".6s">
            <button class="filter-btn active mx-2 mb-2" data-filter="all"><i class="fas fa-users"></i> <?php echo esc_html__('All Teachers', 'zajel'); ?></button>

            <?php
            // Get all teacher posts to extract unique courses
            $all_teachers = get_posts(array(
                'post_type' => 'teacher',
                'posts_per_page' => -1,
            ));

            $all_courses = array();

            // Extract all courses from teachers
            foreach ($all_teachers as $teacher) {
                $courses = get_post_meta($teacher->ID, 'teacher_courses', true);
                if (!empty($courses)) {
                    $courses_array = array_map('trim', explode(',', $courses));
                    foreach ($courses_array as $course) {
                        if (!empty($course) && !in_array($course, $all_courses)) {
                            $all_courses[] = $course;
                        }
                    }
                }
            }

            // Display filter buttons for each unique course
            foreach ($all_courses as $course) :
                $course_slug = strtolower(str_replace(' ', '-', $course));
                $icon_class = 'fas fa-book';

                // Set specific icons for common courses
                if ($course_slug == 'arabic') {
                    $icon_class = 'fas fa-language';
                } elseif ($course_slug == 'quran') {
                    $icon_class = 'fas fa-book-open';
                } elseif ($course_slug == 'tajweed') {
                    $icon_class = 'fas fa-microphone-alt';
                } elseif ($course_slug == 'islamic-studies') {
                    $icon_class = 'fas fa-mosque';
                } elseif ($course_slug == 'qeraat') {
                    $icon_class = 'fas fa-book-reader';
                }
            ?>
            <button class="filter-btn mx-2 mb-2" data-filter="<?php echo esc_attr($course_slug); ?>"><i class="<?php echo esc_attr($icon_class); ?>"></i> <?php echo esc_html($course); ?></button>
            <?php endforeach; ?>
        </div>

        <div class="row teachers-slider-row">
            <?php
            // Check if ACF is active
            $using_acf = function_exists('get_field');

            $teachers_query = new WP_Query(array(
                'post_type' => 'teacher',
                'posts_per_page' => -1,
            ));

            $delay = 0;
            if ($teachers_query->have_posts()) :
                while ($teachers_query->have_posts()) : $teachers_query->the_post();
                    // Get teacher image
                    if ($using_acf) {
                        $teacher_image = get_field('teacher_image');
                        $teacher_category = get_field('teacher_category') ?: 'arabic';
                    } else {
                        $teacher_image = '';
                        $teacher_category = get_post_meta(get_the_ID(), 'teacher_category', true) ?: 'arabic';
                    }

                    // Fallback to featured image
                    if (empty($teacher_image) && has_post_thumbnail()) {
                        $teacher_image = get_the_post_thumbnail_url(get_the_ID(), 'large');
                    }

                    // Final fallback - custom avatar that matches the site colors
                    if (empty($teacher_image)) {
                        $teacher_image = get_template_directory_uri() . '/assets/images/teacher-avatar.svg';
                    }

                    // Get designation
                    if ($using_acf) {
                        $teacher_designation = get_field('teacher_designation');
                    } else {
                        $teacher_designation = get_post_meta(get_the_ID(), 'teacher_designation', true);
                    }

                    if (empty($teacher_designation)) {
                        // Get the first course as designation if available
                        if (!empty($teacher_courses)) {
                            $courses_array = array_map('trim', explode(',', $teacher_courses));
                            if (!empty($courses_array[0])) {
                                $teacher_designation = $courses_array[0] . ' Teacher';
                            } else {
                                $teacher_designation = 'Teacher';
                            }
                        } else {
                            $teacher_designation = 'Teacher';
                        }
                    }

                    // Get description
                    if ($using_acf) {
                        $teacher_description = get_field('teacher_description');
                    } else {
                        $teacher_description = get_post_meta(get_the_ID(), 'teacher_description', true);
                    }

                    if (empty($teacher_description)) {
                        $teacher_description = 'Experienced instructor dedicated to helping students master Arabic language and Islamic studies.';
                    }

                    // Get social links
                    if ($using_acf) {
                        $teacher_facebook = get_field('teacher_facebook');
                        $teacher_twitter = get_field('teacher_twitter');
                        $teacher_linkedin = get_field('teacher_linkedin');
                        $teacher_instagram = get_field('teacher_instagram');
                    } else {
                        $teacher_facebook = get_post_meta(get_the_ID(), 'teacher_facebook', true);
                        $teacher_twitter = get_post_meta(get_the_ID(), 'teacher_twitter', true);
                        $teacher_linkedin = get_post_meta(get_the_ID(), 'teacher_linkedin', true);
                        $teacher_instagram = get_post_meta(get_the_ID(), 'teacher_instagram', true);
                    }

                    $teacher_facebook = !empty($teacher_facebook) ? $teacher_facebook : '#';
                    $teacher_twitter = !empty($teacher_twitter) ? $teacher_twitter : '#';
                    $teacher_linkedin = !empty($teacher_linkedin) ? $teacher_linkedin : '#';
                    $teacher_instagram = !empty($teacher_instagram) ? $teacher_instagram : '#';

                    // Set delay for animation
                    $delay++;
                    if ($delay > 4) $delay = 1;
            ?>
                <div class="col-lg-4 col-md-6 col-12">
                    <?php
                    // Get teacher experience and courses
                    $teacher_experience = get_post_meta(get_the_ID(), 'teacher_experience', true);
                    $teacher_courses = get_post_meta(get_the_ID(), 'teacher_courses', true);

                    // Create data categories from courses for filtering
                    $data_categories = array();

                    // Use only courses for filtering, no default category
                    if (!empty($teacher_courses)) {
                        $courses_array = array_map('trim', explode(',', $teacher_courses));
                        foreach ($courses_array as $course) {
                            if (!empty($course)) {
                                $data_categories[] = strtolower(str_replace(' ', '-', $course));
                            }
                        }
                    }

                    // If no courses specified, use designation as category
                    if (empty($data_categories)) {
                        if (!empty($teacher_designation)) {
                            // Extract main category from designation (e.g. "Quran Teacher" -> "quran")
                            $designation_parts = explode(' ', strtolower($teacher_designation));
                            $designation_category = $designation_parts[0];
                            $data_categories[] = $designation_category;
                        } else {
                            // Fallback to a generic category if no designation
                            $data_categories[] = 'teacher';
                        }
                    }

                    // Create data-category attribute
                    $data_category_attr = implode(' ', $data_categories);
                    ?>
                    <div class="teacher-card fade-in-up delay-<?php echo esc_attr($delay); ?>" data-category="<?php echo esc_attr($data_category_attr); ?>">
                        <div class="teacher-image">
                            <img src="<?php echo esc_url($teacher_image); ?>" alt="<?php the_title(); ?>">
                            <div class="teacher-overlay"></div>
                            <div class="teacher-social">
                                <a href="<?php echo esc_url($teacher_facebook); ?>" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                                <a href="<?php echo esc_url($teacher_twitter); ?>" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                                <a href="<?php echo esc_url($teacher_linkedin); ?>" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                                <a href="<?php echo esc_url($teacher_instagram); ?>" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                            </div>
                            <?php if (!empty($teacher_experience)) : ?>
                            <div class="experience-badge">
                                <span><?php echo esc_html($teacher_experience); ?>+</span>
                                <small>Years</small>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="teacher-info">
                            <h3 class="teacher-name"><?php the_title(); ?></h3>
                            <span class="teacher-designation"><?php echo esc_html($teacher_designation); ?></span>
                            <?php if (!empty($teacher_courses)) : ?>
                            <div class="teacher-courses">
                                <?php
                                $courses_array = array_map('trim', explode(',', $teacher_courses));
                                foreach ($courses_array as $course) :
                                    if (!empty($course)) :
                                        $course_class = strtolower(str_replace(' ', '-', $course));
                                ?>
                                <span class="course-tag <?php echo esc_attr($course_class); ?>"><?php echo esc_html($course); ?></span>
                                <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                            <?php
                            // Get teacher summary from custom field, excerpt, or content
                            $teacher_summary = '';
                            $teacher_summary_field = get_post_meta(get_the_ID(), 'teacher_summary', true);

                            if (!empty($teacher_summary_field)) {
                                // Use custom summary field if available
                                $teacher_summary = $teacher_summary_field;
                            } elseif (has_excerpt()) {
                                // Use excerpt if available
                                $teacher_summary = get_the_excerpt();
                            } else {
                                // Use trimmed content as fallback
                                $teacher_summary = wp_trim_words(get_the_content(), 25);
                            }
                            ?>
                            <?php if (!empty($teacher_summary)) : ?>
                            <p class="teacher-description"><?php echo wp_trim_words(esc_html($teacher_summary), 15, '...'); ?></p>
                            <?php endif; ?>
                            <a href="<?php the_permalink(); ?>" class="teacher-link"><?php echo esc_html__('View Profile', 'zajel'); ?></a>
                        </div>
                    </div>
                </div>
            <?php
                endwhile;
                wp_reset_postdata();
            else :
            ?>
                <div class="col-12 text-center">
                    <p><?php esc_html_e('No teachers found.', 'zajel'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>
<!-- End Teachers Grid Section -->

<!-- Start Teachers Features Section -->
<section class="teachers-features">
    <div class="container">
        <div class="section-title">
            <span class="fade-in"><?php echo esc_html__('Why Choose Our Teachers', 'zajel'); ?></span>
            <h2 class="fade-in-up delay-1"><?php echo esc_html__('Benefits of Learning with Our Teachers', 'zajel'); ?></h2>
            <p class="fade-in-up delay-2"><?php echo esc_html__('Discover the advantages of learning Arabic language and Islamic studies with our expert teachers.', 'zajel'); ?></p>
        </div>

        <div class="row">
            <div class="col-lg-4 col-md-6 col-12">
                <div class="feature-box fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <h3 class="feature-title"><?php echo esc_html__('Certified Teachers', 'zajel'); ?></h3>
                    <p class="feature-text"><?php echo esc_html__('All our teachers are certified with degrees from prestigious Islamic universities and have years of teaching experience.', 'zajel'); ?></p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 col-12">
                <div class="feature-box fade-in-up delay-1">
                    <div class="feature-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3 class="feature-title"><?php echo esc_html__('Personalized Learning', 'zajel'); ?></h3>
                    <p class="feature-text"><?php echo esc_html__('Our teachers provide personalized attention to each student, adapting their teaching methods to individual learning styles.', 'zajel'); ?></p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 col-12">
                <div class="feature-box fade-in-up delay-2">
                    <div class="feature-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3 class="feature-title"><?php echo esc_html__('Native Arabic Speakers', 'zajel'); ?></h3>
                    <p class="feature-text"><?php echo esc_html__('Learn Arabic from native speakers who understand the nuances of the language and can provide authentic pronunciation guidance.', 'zajel'); ?></p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 col-12">
                <div class="feature-box fade-in-up delay-3">
                    <div class="feature-icon">
                        <i class="fas fa-book-reader"></i>
                    </div>
                    <h3 class="feature-title"><?php echo esc_html__('Comprehensive Curriculum', 'zajel'); ?></h3>
                    <p class="feature-text"><?php echo esc_html__('Our teachers follow a well-structured curriculum that covers all aspects of Arabic language and Islamic studies.', 'zajel'); ?></p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 col-12">
                <div class="feature-box fade-in-up delay-4">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="feature-title"><?php echo esc_html__('Flexible Schedule', 'zajel'); ?></h3>
                    <p class="feature-text"><?php echo esc_html__('Our teachers offer flexible scheduling options to accommodate students from different time zones around the world.', 'zajel'); ?></p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 col-12">
                <div class="feature-box fade-in-up delay-5">
                    <div class="feature-icon">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <h3 class="feature-title"><?php echo esc_html__('Continuous Support', 'zajel'); ?></h3>
                    <p class="feature-text"><?php echo esc_html__('Our teachers provide continuous support and guidance to students even outside of class hours to ensure their success.', 'zajel'); ?></p>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Teachers Features Section -->






<?php get_footer(); ?>
