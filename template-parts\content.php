<?php
/**
 * Template part for displaying posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Zajel_Arabic
 */
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('archive-post wow fadeInUp'); ?> data-wow-delay=".2s">
    <div class="post-thumbnail">
        <a href="<?php the_permalink(); ?>">
            <?php if (has_post_thumbnail()) : ?>
                <?php the_post_thumbnail('large', array('alt' => get_the_title())); ?>
            <?php else : ?>
                <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/blog-placeholder.svg'); ?>" alt="<?php the_title_attribute(); ?>">
            <?php endif; ?>
        </a>
    </div>

    <div class="post-content">
        <div class="post-meta">
            <div class="post-date">
                <i class="lni lni-calendar"></i>
                <a href="<?php echo esc_url(get_day_link(get_post_time('Y'), get_post_time('m'), get_post_time('j'))); ?>">
                    <?php echo get_the_date(); ?>
                </a>
            </div>

            <?php
            $categories = get_the_category();
            if (!empty($categories)) :
            ?>
                <div class="post-category">
                    <i class="lni lni-folder"></i>
                    <a href="<?php echo esc_url(get_category_link($categories[0]->term_id)); ?>">
                        <?php echo esc_html($categories[0]->name); ?>
                    </a>
                </div>
            <?php endif; ?>

            <div class="post-comments">
                <i class="lni lni-comments"></i>
                <a href="<?php comments_link(); ?>">
                    <?php
                    $comment_count = get_comments_number();
                    if ($comment_count == 0) {
                        esc_html_e('No Comments', 'zajel');
                    } elseif ($comment_count == 1) {
                        esc_html_e('1 Comment', 'zajel');
                    } else {
                        echo sprintf(esc_html__('%d Comments', 'zajel'), $comment_count);
                    }
                    ?>
                </a>
            </div>
        </div>

        <h2 class="post-title">
            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
        </h2>

        <div class="post-excerpt">
            <?php the_excerpt(); ?>
        </div>

        <div class="read-more">
            <a href="<?php the_permalink(); ?>">
                <?php esc_html_e('Read More', 'zajel'); ?> <i class="lni lni-arrow-right"></i>
            </a>
        </div>
    </div>
</article><!-- #post-<?php the_ID(); ?> -->
