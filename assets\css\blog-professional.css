/**
 * Professional Blog Styles for Zajel Arabic Theme
 */

/* Hero Area Styles */
.hero-area.blog-hero {
    background: linear-gradient(135deg, #03355c, #1a5f8d);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
    margin-bottom: 0;
}

.hero-area.blog-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 1;
}

.hero-area.blog-hero .hero-inner {
    position: relative;
    z-index: 2;
}

.hero-area.blog-hero .page-title {
    color: #fff;
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.hero-area.blog-hero .page-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #00989f, #03777d);
    border-radius: 2px;
}

.hero-area.blog-hero p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    max-width: 700px;
    margin: 25px auto 25px;
    line-height: 1.6;
}

.hero-area.blog-hero .breadcrumb-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
    list-style: none;
    background-color: rgba(255, 255, 255, 0.1);
    display: inline-flex;
    padding: 8px 20px;
    border-radius: 30px;
}

.hero-area.blog-hero .breadcrumb-nav li {
    display: inline-block;
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 500;
    margin-right: 8px;
}

.hero-area.blog-hero .breadcrumb-nav li a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all 0.3s ease;
}

.hero-area.blog-hero .breadcrumb-nav li a:hover {
    color: #fff;
}

.hero-area.blog-hero .breadcrumb-nav li i {
    font-size: 12px;
    margin: 0 5px;
}

/* Blog Area Styles */
.blog-area {
    padding: 80px 0;
    background-color: #f9f9f9;
}

.professional-blog {
    margin-bottom: 40px;
}

/* Featured Post Styles */
.professional-blog .featured-post {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
    margin-bottom: 40px;
    transition: all 0.4s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.professional-blog .featured-post:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.professional-blog .featured-post-content {
    padding: 0;
}

.professional-blog .featured-post .post-thumbnail {
    position: relative;
    height: 100%;
    overflow: hidden;
    border-radius: 10px 0 0 10px;
}

.professional-blog .featured-post .post-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.professional-blog .featured-post:hover .post-thumbnail img {
    transform: scale(1.05);
}

.professional-blog .featured-post .featured-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, #03777d, #00989f);
    color: #fff;
    padding: 5px 15px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.professional-blog .featured-post .featured-badge i {
    margin-right: 5px;
    color: #fff;
}

.professional-blog .featured-post .post-content {
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.professional-blog .featured-post .post-meta {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.professional-blog .featured-post .post-meta > div {
    margin-right: 20px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #777;
    display: flex;
    align-items: center;
}

.professional-blog .featured-post .post-meta i {
    margin-right: 5px;
    color: #03777d;
}

.professional-blog .featured-post .post-meta a {
    color: #777;
    text-decoration: none;
    transition: all 0.3s ease;
}

.professional-blog .featured-post .post-meta a:hover {
    color: #03777d;
}

.professional-blog .featured-post .post-title {
    font-size: 24px;
    font-weight: 700;
    line-height: 1.4;
    margin-bottom: 15px;
}

.professional-blog .featured-post .post-title a {
    color: #222;
    text-decoration: none;
    transition: all 0.3s ease;
}

.professional-blog .featured-post .post-title a:hover {
    color: #03777d;
}

.professional-blog .featured-post .post-excerpt {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

.professional-blog .featured-post .read-more a {
    display: inline-flex;
    align-items: center;
    color: #fff;
    background: linear-gradient(135deg, #03355c, #1a5f8d);
    padding: 10px 20px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(26, 95, 141, 0.2);
}

.professional-blog .featured-post .read-more a i {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.professional-blog .featured-post .read-more a:hover {
    background: linear-gradient(135deg, #1a5f8d, #03355c);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(26, 95, 141, 0.3);
}

.professional-blog .featured-post .read-more a:hover i {
    transform: translateX(5px);
}

/* Archive Post Styles */
.professional-blog .archive-post {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    height: 100%;
    transition: all 0.4s ease;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.professional-blog .archive-post:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.professional-blog .archive-post .post-thumbnail {
    position: relative;
    overflow: hidden;
}

.professional-blog .archive-post .post-thumbnail img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.professional-blog .archive-post:hover .post-thumbnail img {
    transform: scale(1.05);
}

.professional-blog .archive-post .post-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.professional-blog .archive-post .post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 14px;
}

.professional-blog .archive-post .post-meta > div {
    display: flex;
    align-items: center;
}

.professional-blog .archive-post .post-meta i {
    color: #03777d;
    margin-right: 5px;
}

.professional-blog .archive-post .post-meta a {
    color: #777;
    text-decoration: none;
    transition: all 0.3s ease;
}

.professional-blog .archive-post .post-meta a:hover {
    color: #03777d;
}

.professional-blog .archive-post .post-title {
    font-size: 20px;
    font-weight: 700;
    line-height: 1.4;
    margin-bottom: 15px;
}

.professional-blog .archive-post .post-title a {
    color: #222;
    text-decoration: none;
    transition: all 0.3s ease;
}

.professional-blog .archive-post .post-title a:hover {
    color: #03777d;
}

.professional-blog .archive-post .post-excerpt {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
    flex-grow: 1;
}

.professional-blog .archive-post .read-more {
    margin-top: auto;
}

.professional-blog .archive-post .read-more a {
    display: inline-flex;
    align-items: center;
    color: #fff;
    background: linear-gradient(135deg, #03355c, #1a5f8d);
    padding: 8px 18px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(26, 95, 141, 0.2);
}

.professional-blog .archive-post .read-more a i {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.professional-blog .archive-post .read-more a:hover {
    background: linear-gradient(135deg, #1a5f8d, #03355c);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(26, 95, 141, 0.3);
}

.professional-blog .archive-post .read-more a:hover i {
    transform: translateX(5px);
}

/* Pagination Styles */
.professional-blog .pagination-area {
    margin-top: 50px;
    text-align: center;
}

.professional-blog .page-numbers {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #fff;
    color: #333;
    border-radius: 50%;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    margin: 0 5px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.professional-blog .page-numbers:hover {
    background-color: #1a5f8d;
    color: #fff;
    border-color: #1a5f8d;
}

.professional-blog .page-numbers.current {
    background: linear-gradient(135deg, #03777d, #00989f);
    color: #fff;
    box-shadow: 0 4px 10px rgba(3, 119, 125, 0.2);
    border-color: #03777d;
}

.professional-blog .page-numbers.prev,
.professional-blog .page-numbers.next {
    width: auto;
    padding: 0 15px;
    border-radius: 20px;
}

.professional-blog .page-numbers.prev:hover i {
    transform: translateX(-3px);
}

.professional-blog .page-numbers.next:hover i {
    transform: translateX(3px);
}

.professional-blog .page-numbers i {
    transition: all 0.3s ease;
}

/* Blog Sidebar */
.professional-sidebar {
    position: sticky;
    top: 30px;
}

.professional-sidebar .widget {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.professional-sidebar .widget-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(3, 119, 125, 0.1);
    position: relative;
    color: #333;
}

.professional-sidebar .widget-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #03777d, #00989f);
}

/* Search Widget */
.professional-sidebar .widget_search .search-form {
    position: relative;
}

.professional-sidebar .widget_search .search-field {
    width: 100%;
    padding: 12px 20px;
    padding-right: 50px;
    border: 1px solid #eee;
    border-radius: 30px;
    font-size: 15px;
    transition: all 0.3s ease;
}

.professional-sidebar .widget_search .search-field:focus {
    border-color: #03777d;
    outline: none;
    box-shadow: 0 0 0 3px rgba(3, 119, 125, 0.1);
}

.professional-sidebar .widget_search .search-submit {
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
    height: 100%;
    background-color: transparent;
    border: none;
    color: #666;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.professional-sidebar .widget_search .search-submit:hover {
    color: #03777d;
}

/* Categories Widget */
.professional-sidebar .widget_categories ul,
.professional-sidebar .widget_recent_entries ul,
.professional-sidebar .widget_archive ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.professional-sidebar .widget_categories li,
.professional-sidebar .widget_recent_entries li,
.professional-sidebar .widget_archive li {
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.professional-sidebar .widget_categories li:last-child,
.professional-sidebar .widget_recent_entries li:last-child,
.professional-sidebar .widget_archive li:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.professional-sidebar .widget_categories li:first-child,
.professional-sidebar .widget_recent_entries li:first-child,
.professional-sidebar .widget_archive li:first-child {
    padding-top: 0;
}

.professional-sidebar .widget_categories a,
.professional-sidebar .widget_archive a {
    color: #666;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
}

.professional-sidebar .widget_categories a::before,
.professional-sidebar .widget_archive a::before {
    content: '\f105';
    font-family: 'Line Awesome Free';
    font-weight: 900;
    margin-right: 10px;
    color: #03777d;
    transition: all 0.3s ease;
}

.professional-sidebar .widget_categories a:hover,
.professional-sidebar .widget_archive a:hover {
    color: #03777d;
    padding-left: 5px;
}

.professional-sidebar .widget_categories a:hover::before,
.professional-sidebar .widget_archive a:hover::before {
    margin-right: 15px;
}

.professional-sidebar .widget_categories a span {
    background-color: #f0f0f0;
    color: #666;
    padding: 2px 8px;
    border-radius: 20px;
    font-size: 12px;
    transition: all 0.3s ease;
}

.professional-sidebar .widget_categories a:hover span {
    background-color: #03777d;
    color: #fff;
}

/* Recent Posts Widget */
.professional-sidebar .widget_recent_entries li {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.professional-sidebar .widget_recent_entries .post-image {
    width: 70px;
    height: 70px;
    border-radius: 5px;
    overflow: hidden;
    flex-shrink: 0;
}

.professional-sidebar .widget_recent_entries .post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.professional-sidebar .widget_recent_entries li:hover .post-image img {
    transform: scale(1.05);
}

.professional-sidebar .widget_recent_entries .post-content {
    flex-grow: 1;
}

.professional-sidebar .widget_recent_entries .post-title {
    font-size: 15px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 5px;
}

.professional-sidebar .widget_recent_entries .post-title a {
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.professional-sidebar .widget_recent_entries .post-title a:hover {
    color: #03777d;
}

.professional-sidebar .widget_recent_entries .post-date {
    font-size: 12px;
    color: #888;
    display: flex;
    align-items: center;
}

.professional-sidebar .widget_recent_entries .post-date i {
    color: #03777d;
    margin-right: 5px;
}

/* Tag Cloud Widget */
.professional-sidebar .widget_tag_cloud .tagcloud {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.professional-sidebar .widget_tag_cloud .tag-cloud-link {
    display: inline-block;
    padding: 5px 15px;
    background-color: #f0f0f0;
    color: #666;
    border-radius: 20px;
    font-size: 13px !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.professional-sidebar .widget_tag_cloud .tag-cloud-link:hover {
    background: linear-gradient(135deg, #00989f, #03777d);
    color: #fff;
    transform: translateY(-2px);
}

/* No Results */
.professional-blog .no-results {
    text-align: center;
    padding: 50px 0;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.professional-blog .no-results .page-title {
    font-size: 28px;
    margin-bottom: 15px;
    color: #333;
}

.professional-blog .no-results p {
    margin-bottom: 20px;
    color: #666;
}

.professional-blog .no-results .search-form {
    max-width: 500px;
    margin: 0 auto 30px;
}

.professional-blog .no-results .icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: rgba(3, 119, 125, 0.1);
    border-radius: 50%;
    margin-bottom: 20px;
}

.professional-blog .no-results .icon i {
    font-size: 36px;
    color: #03777d;
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .hero-area.blog-hero {
        padding: 80px 0;
    }

    .hero-area.blog-hero .page-title {
        font-size: 36px;
    }

    .blog-area {
        padding: 60px 0;
    }

    .professional-sidebar {
        margin-top: 50px;
        position: static;
    }

    .professional-blog .featured-post .post-content {
        padding: 20px;
    }

    .professional-blog .featured-post .post-title {
        font-size: 22px;
    }
}

@media only screen and (max-width: 767px) {
    .hero-area.blog-hero {
        padding: 60px 0;
    }

    .hero-area.blog-hero .page-title {
        font-size: 30px;
    }

    .hero-area.blog-hero p {
        font-size: 16px;
    }

    .blog-area {
        padding: 50px 0;
    }

    .professional-blog .featured-post .row {
        flex-direction: column;
    }

    .professional-blog .featured-post .post-thumbnail {
        height: 250px;
        border-radius: 10px 10px 0 0;
    }

    .professional-blog .featured-post .post-content {
        padding: 25px;
    }

    .professional-blog .featured-post .post-title {
        font-size: 20px;
    }

    .professional-blog .archive-post .post-title {
        font-size: 18px;
    }

    .professional-blog .archive-post .post-meta {
        font-size: 12px;
    }

    .professional-blog .page-numbers {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .professional-sidebar .widget {
        padding: 20px;
    }

    .professional-sidebar .widget-title {
        font-size: 18px;
    }
}

@media only screen and (max-width: 575px) {
    .hero-area.blog-hero {
        padding: 50px 0;
    }

    .hero-area.blog-hero .page-title {
        font-size: 26px;
    }

    .hero-area.blog-hero p {
        font-size: 15px;
    }

    .blog-area {
        padding: 40px 0;
    }

    .professional-blog .featured-post .post-title {
        font-size: 18px;
    }

    .professional-blog .archive-post .post-thumbnail img {
        height: 200px;
    }

    .professional-blog .archive-post .post-content {
        padding: 20px;
    }

    .professional-blog .archive-post .post-title {
        font-size: 18px;
    }

    .professional-blog .archive-post .read-more a {
        padding: 8px 15px;
        font-size: 13px;
    }
}
