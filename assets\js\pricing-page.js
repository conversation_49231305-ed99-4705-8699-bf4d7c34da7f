(function($) {
    "use strict";

    $(document).ready(function() {
        // Toggle between 30 and 60 minute plans
        $('.toggle-option').on('click', function() {
            const target = $(this).data('target');

            // Update active toggle
            $('.toggle-option').removeClass('active');
            $(this).addClass('active');

            // Show the selected plans
            if (target === 'thirty-min') {
                $('.thirty-min-plans').fadeIn(300);
                $('.sixty-min-plans').hide();
            } else {
                $('.thirty-min-plans').hide();
                $('.sixty-min-plans').fadeIn(300);
            }
        });

        // Show 60 minute plans by default
        $('.toggle-option[data-target="sixty-min"]').trigger('click');

        // FAQ Accordion
        $('.faq-header').on('click', function() {
            const parent = $(this).parent();

            if (parent.hasClass('active')) {
                parent.removeClass('active');
                parent.find('.faq-content').slideUp(300);
            } else {
                $('.faq-item').removeClass('active');
                $('.faq-content').slideUp(300);

                parent.addClass('active');
                parent.find('.faq-content').slideDown(300);
            }
        });

        // Initialize the first FAQ item as open
        $('.faq-item:first-child').addClass('active').find('.faq-content').show();

        // Add hover effects to pricing plans
        $('.single-pricing-plan').hover(
            function() {
                $(this).find('.plan-header').css('background', 'linear-gradient(135deg, #03355c, #1a5f8d)');
            },
            function() {
                $(this).find('.plan-header').css('background', '#03355c');
            }
        );

        // Add animation to feature icons
        $('.feature-icon').each(function(index) {
            const delay = index * 0.1;
            $(this).css('animation', `fadeIn 0.5s ease ${delay}s forwards`);
        });

        // Add scroll animation to pricing plans
        $(window).scroll(function() {
            const windowHeight = $(window).height();
            const scrollTop = $(window).scrollTop();

            $('.single-pricing-plan').each(function() {
                const elementTop = $(this).offset().top;

                if (elementTop < (scrollTop + windowHeight - 100)) {
                    $(this).addClass('animated');
                }
            });
        });

        // Trigger scroll event on page load
        $(window).trigger('scroll');
    });

})(jQuery);
