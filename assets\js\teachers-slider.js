/**
 * Teachers Slider JavaScript for Zajel Arabic Theme
 * Handles slider functionality for the Teachers page with touch support
 */

jQuery(document).ready(function($) {
    // Initialize the teachers slider
    initTeachersSlider();

    /**
     * Initialize Teachers Slider
     */
    function initTeachersSlider() {
        // Variables
        var $teachersGrid = $('.teachers-grid .row');
        var $teacherCards = $('.teacher-card');
        var totalItems = $teacherCards.length;

        // If no teachers found, exit
        if (totalItems === 0) return;

        // Create slider container
        $teachersGrid.addClass('position-relative');
        $teachersGrid.wrap('<div class="teachers-slider-wrapper"></div>');

        // Create slider navigation
        var $sliderNav = $('<div class="teachers-slider-nav"></div>');
        var $sliderPagination = $('<div class="teachers-slider-pagination"></div>');

        // Add navigation arrows
        $sliderNav.append('<button class="teachers-slider-prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>');
        $sliderNav.append('<button class="teachers-slider-next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>');

        // Add to DOM
        $('.teachers-slider-wrapper').append($sliderNav);
        $('.teachers-slider-wrapper').append($sliderPagination);

        // Initialize slider state
        var currentIndex = 0; // Current teacher index
        var itemsPerView = getItemsPerView(); // Number of items visible at once
        var totalSlides = totalItems; // Total number of teachers

        // Create pagination dots
        updatePagination();

        // Show initial items
        updateVisibleItems();

        // Handle window resize
        $(window).on('resize', function() {
            var newItemsPerView = getItemsPerView();

            // Only update if items per view has changed
            if (newItemsPerView !== itemsPerView) {
                itemsPerView = newItemsPerView;

                // Make sure current index is valid
                if (currentIndex >= totalSlides) {
                    currentIndex = totalSlides - 1;
                }

                updatePagination();
                updateVisibleItems();
            }
        });

        // Listen for filter changes
        $(window).on('teacher-filter-changed', function(e, firstVisibleIndex, $visibleTeachers) {
            // Update the current index to the first visible teacher
            currentIndex = firstVisibleIndex;

            // Update the total slides count based on filtered teachers
            totalSlides = $visibleTeachers.length;

            // Update pagination and visible items
            updatePagination();
            updateVisibleItems();

            // Hide the no teachers message if it exists
            $('.no-teachers-message').hide();
        });

        // Handle navigation clicks - move one teacher at a time with smooth animation
        $('.teachers-slider-prev').on('click', function() {
            // Get the current filter value
            var currentFilter = $('body').attr('data-current-filter') || 'all';

            // Get all teacher cards or only filtered ones
            var $relevantTeachers = currentFilter === 'all' ?
                $teacherCards :
                $teacherCards.filter('.filtered-visible');

            if (currentIndex > 0) {
                // Move one teacher at a time
                currentIndex--;
                // Add a smooth transition effect
                $teacherCards.parent().css('transition', 'opacity 0.3s ease-out');
                updateVisibleItems();
                updateActivePagination();
            }
        });

        $('.teachers-slider-next').on('click', function() {
            // Get the current filter value
            var currentFilter = $('body').attr('data-current-filter') || 'all';

            // Get all teacher cards or only filtered ones
            var $relevantTeachers = currentFilter === 'all' ?
                $teacherCards :
                $teacherCards.filter('.filtered-visible');

            var totalRelevantTeachers = $relevantTeachers.length;

            if (currentIndex < totalRelevantTeachers - 1) {
                // Move one teacher at a time
                currentIndex++;
                // Add a smooth transition effect
                $teacherCards.parent().css('transition', 'opacity 0.3s ease-out');
                updateVisibleItems();
                updateActivePagination();
            }
        });

        // Handle pagination clicks with smooth transition
        $(document).on('click', '.teachers-slider-dot', function() {
            // Get the current filter value
            var currentFilter = $('body').attr('data-current-filter') || 'all';

            // Get all teacher cards or only filtered ones
            var $relevantTeachers = currentFilter === 'all' ?
                $teacherCards :
                $teacherCards.filter('.filtered-visible');

            var totalRelevantTeachers = $relevantTeachers.length;
            var clickedIndex = $(this).data('index');

            // Make sure the index is valid
            if (clickedIndex >= 0 && clickedIndex < totalRelevantTeachers) {
                currentIndex = clickedIndex;
                // Add a smooth transition effect
                $teacherCards.parent().css('transition', 'opacity 0.3s ease-out');
                updateVisibleItems();
                updateActivePagination();
            }
        });

        // Handle swipe gestures
        var touchStartX = 0;
        var touchEndX = 0;

        $('.teachers-slider-wrapper').on('touchstart', function(e) {
            touchStartX = e.originalEvent.touches[0].clientX;
        });

        $('.teachers-slider-wrapper').on('touchend', function(e) {
            touchEndX = e.originalEvent.changedTouches[0].clientX;
            handleSwipe();
        });

        // Function to determine items per view based on screen size
        function getItemsPerView() {
            if (window.innerWidth < 768) {
                return 1; // Show 1 item on mobile
            } else if (window.innerWidth < 992) {
                return 2; // Show 2 items on tablets
            } else {
                return 3; // Show 3 items on desktop
            }
        }

        // Function to update pagination dots
        function updatePagination() {
            $sliderPagination.empty();

            // Get the current filter value
            var currentFilter = $('body').attr('data-current-filter') || 'all';

            // Get all teacher cards or only filtered ones
            var $relevantTeachers = currentFilter === 'all' ?
                $teacherCards :
                $teacherCards.filter('.filtered-visible');

            var totalRelevantTeachers = $relevantTeachers.length;

            // Only show pagination if we have more than one teacher
            if (totalRelevantTeachers > 1) {
                for (var i = 0; i < totalRelevantTeachers; i++) {
                    var isActive = i === currentIndex ? ' active' : '';
                    $sliderPagination.append('<button class="teachers-slider-dot' + isActive + '" data-index="' + i + '" aria-label="Teacher ' + (i + 1) + '"></button>');
                }
            }

            // Update navigation buttons state
            updateNavigationState();
        }

        // Function to update active pagination dot
        function updateActivePagination() {
            $('.teachers-slider-dot').removeClass('active');
            $('.teachers-slider-dot[data-index="' + currentIndex + '"]').addClass('active');

            // Update navigation buttons state
            updateNavigationState();
        }

        // Function to update navigation buttons state
        function updateNavigationState() {
            // Get the current filter value
            var currentFilter = $('body').attr('data-current-filter') || 'all';

            // Get all teacher cards or only filtered ones
            var $relevantTeachers = currentFilter === 'all' ?
                $teacherCards :
                $teacherCards.filter('.filtered-visible');

            var totalRelevantTeachers = $relevantTeachers.length;

            // Disable prev button if on first teacher
            if (currentIndex === 0) {
                $('.teachers-slider-prev').addClass('disabled');
            } else {
                $('.teachers-slider-prev').removeClass('disabled');
            }

            // Disable next button if on last teacher
            if (currentIndex >= totalRelevantTeachers - 1) {
                $('.teachers-slider-next').addClass('disabled');
            } else {
                $('.teachers-slider-next').removeClass('disabled');
            }
        }

        // Function to update which teachers are visible
        function updateVisibleItems() {
            // Get the current filter value
            var currentFilter = $('body').attr('data-current-filter') || 'all';

            // Get all teacher cards or only filtered ones
            var $relevantTeachers = currentFilter === 'all' ?
                $teacherCards :
                $teacherCards.filter('.filtered-visible');

            // If no teachers match the filter, show a message
            if ($relevantTeachers.length === 0) {
                $teacherCards.parent().hide();
                if ($('.no-teachers-message').length === 0) {
                    $('.teachers-slider-wrapper').append('<div class="col-12 text-center no-teachers-message"><p>No teachers found for this category.</p></div>');
                } else {
                    $('.no-teachers-message').show();
                }
                return;
            }

            // Hide the no teachers message if it exists
            $('.no-teachers-message').hide();

            // Hide all teachers first
            $teacherCards.parent().hide();

            // For mobile view (1 item per view), show the current item with improved transitions
            if (itemsPerView === 1) {
                // Make sure currentIndex is valid for the filtered teachers
                if (currentIndex >= $relevantTeachers.length) {
                    currentIndex = 0;
                }

                // Find the actual teacher to show based on current index
                var teacherToShow = $relevantTeachers.eq(currentIndex);
                if (teacherToShow.length) {
                    // Reset all teachers first
                    $teacherCards.parent().css({
                        'position': '',
                        'left': '',
                        'right': '',
                        'opacity': '',
                        'transform': '',
                        'transition': '',
                        'display': 'none'
                    });

                    // Show the current teacher with proper styling
                    teacherToShow.parent().css({
                        'position': 'relative',
                        'opacity': '1',
                        'transform': 'none',
                        'display': 'block',
                        'width': '100%',
                        'float': 'none',
                        'margin': '0 auto'
                    });

                    // Add active class
                    $teacherCards.removeClass('active');
                    teacherToShow.addClass('active');

                    // Make sure the parent container has proper height
                    var containerHeight = teacherToShow.parent().outerHeight(true);
                    $('.teachers-slider-wrapper').css('min-height', containerHeight + 'px');
                }
                return;
            }

            // For desktop view (3 items per view), implement smart sliding
            // We want to show the current teacher and maintain context by keeping
            // previously visible teachers when possible

            var visibleTeachers = [];
            var totalRelevantTeachers = $relevantTeachers.length;

            // If we're at the beginning (index 0 or 1)
            if (currentIndex <= 1) {
                // Show first 3 teachers (0, 1, 2)
                for (var i = 0; i < Math.min(3, totalRelevantTeachers); i++) {
                    visibleTeachers.push($relevantTeachers.eq(i));
                }
            }
            // If we're at the end
            else if (currentIndex >= totalRelevantTeachers - 2) {
                // Show last 3 teachers
                for (var i = Math.max(0, totalRelevantTeachers - 3); i < totalRelevantTeachers; i++) {
                    visibleTeachers.push($relevantTeachers.eq(i));
                }
            }
            // If we're in the middle
            else {
                // Show current teacher and one on each side
                visibleTeachers.push($relevantTeachers.eq(currentIndex - 1));
                visibleTeachers.push($relevantTeachers.eq(currentIndex));
                visibleTeachers.push($relevantTeachers.eq(currentIndex + 1));
            }

            // Reset any previous styles
            $teacherCards.parent().css({
                'position': '',
                'left': '',
                'right': '',
                'opacity': '',
                'transform': '',
                'transition': ''
            });

            // Show the visible teachers with animation
            for (var i = 0; i < visibleTeachers.length; i++) {
                if (visibleTeachers[i] && visibleTeachers[i].length) {
                    visibleTeachers[i].parent().fadeIn(400);
                }
            }

            // Add active class to the current teacher
            $teacherCards.removeClass('active');
            $relevantTeachers.eq(currentIndex).addClass('active');
        }

        // Function to handle swipe gestures
        function handleSwipe() {
            var swipeThreshold = 50; // Minimum distance to be considered a swipe

            // Get the current filter value
            var currentFilter = $('body').attr('data-current-filter') || 'all';

            // Get all teacher cards or only filtered ones
            var $relevantTeachers = currentFilter === 'all' ?
                $teacherCards :
                $teacherCards.filter('.filtered-visible');

            var totalRelevantTeachers = $relevantTeachers.length;

            // If no relevant teachers, exit
            if (totalRelevantTeachers === 0) return;

            if (touchEndX < touchStartX - swipeThreshold) {
                // Swipe left - next
                if (currentIndex < totalRelevantTeachers - 1) {
                    // Move one teacher at a time
                    currentIndex++;

                    // Simply update the display - no animations needed
                    updateVisibleItems();
                    updateActivePagination();
                }
            } else if (touchEndX > touchStartX + swipeThreshold) {
                // Swipe right - previous
                if (currentIndex > 0) {
                    // Move one teacher at a time
                    currentIndex--;

                    // Simply update the display - no animations needed
                    updateVisibleItems();
                    updateActivePagination();
                }
            }
        }
    }
});
