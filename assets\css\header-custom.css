/* Custom Header Styles for Zajel Arabic Theme */
:root {
    --gold-light: #FFD700;
    --gold-medium: #DAA520;
    --gold-dark: #B8860B;
}

/* Header Styles */
.header.navbar-area {
    background-color: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 10px 0;
}

.header .nav-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Logo Styles */
.navbar-brand {
    padding: 0;
    margin-right: 30px;
}

.navbar-brand img {
    max-height: 60px;
    width: auto;
}

.default-logo {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--gold-dark);
}

.default-logo i {
    color: var(--gold-dark);
    margin-right: 10px;
    font-size: 2rem;
}

/* Navigation Menu */
.navbar-nav {
    margin-right: auto;
}

.navbar-nav .nav-item {
    margin-right: 20px;
    position: relative;
}

.navbar-nav .nav-item:last-child {
    margin-right: 0;
}

.navbar-nav .nav-item a {
    color: #333;
    font-weight: 600;
    padding: 10px 0;
    position: relative;
    display: block;
}

.navbar-nav .nav-item a:hover,
.navbar-nav .nav-item a.active {
    color: var(--gold-dark);
    text-decoration: none;
}

.navbar-nav .nav-item a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    transition: all 0.3s ease;
}

.navbar-nav .nav-item a:hover::after,
.navbar-nav .nav-item a.active::after {
    width: 100%;
}

/* Dropdown Menu */
.navbar-nav .nav-item .sub-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 220px;
    background-color: #fff;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    padding: 10px 0;
    border-radius: 0 0 5px 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 99;
}

.navbar-nav .nav-item:hover .sub-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.navbar-nav .nav-item .sub-menu li {
    position: relative;
    padding: 0;
}

.navbar-nav .nav-item .sub-menu li a {
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    display: block;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.navbar-nav .nav-item .sub-menu li:last-child a {
    border-bottom: none;
}

.navbar-nav .nav-item .sub-menu li a::after {
    display: none;
}

.navbar-nav .nav-item .sub-menu li a:hover {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    padding-left: 25px;
}

/* Social Icons */
.header-social {
    display: flex;
    margin-right: 20px;
}

.header-social ul {
    display: flex;
    padding: 0;
    margin: 0;
    list-style: none;
}

.header-social ul li {
    margin-right: 10px;
}

.header-social ul li:last-child {
    margin-right: 0;
}

.header-social ul li a {
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    display: block;
    border-radius: 50%;
    color: #333;
    background-color: #f5f5f5;
    font-size: 16px;
    transition: all 0.3s ease;
}

.header-social ul li a:hover {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    transform: translateY(-3px);
}

/* Trial Class Button */
.trial-class-button {
    margin-left: 20px;
}

.trial-class-btn {
    padding: 10px 20px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    border-radius: 30px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    display: inline-block;
}

.trial-class-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(184, 134, 11, 0.3);
    color: #fff;
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    padding: 0;
    border: none;
    background: transparent;
    cursor: pointer;
}

.mobile-menu-btn:focus {
    outline: none;
    box-shadow: none;
}

.mobile-menu-btn .toggler-icon {
    width: 30px;
    height: 2px;
    background-color: #333;
    display: block;
    margin: 6px 0;
    position: relative;
    transition: all 0.3s ease;
}

/* Mobile Menu Styles */
@media only screen and (max-width: 991px) {
    /* Show Mobile Menu Button */
    .mobile-menu-btn {
        display: block;
    }

    /* Hide Desktop Elements */
    .header-social {
        display: none;
    }

    .trial-class-button {
        display: none;
    }

    /* Mobile Menu Container */
    .navbar-collapse {
        position: fixed;
        top: 0;
        left: -280px;
        width: 280px;
        height: 100vh;
        background-color: #fff;
        z-index: 999;
        padding: 0;
        transition: all 0.3s ease;
        overflow-y: auto;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        display: block !important;
    }

    .navbar-collapse.show {
        left: 0;
    }

    /* Mobile Menu Overlay */
    .mobile-menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        z-index: 998;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .mobile-menu-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* Mobile Menu Header */
    .mobile-menu-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .mobile-menu-logo img {
        max-height: 40px;
    }

    .mobile-menu-close {
        width: 30px;
        height: 30px;
        background-color: #f5f5f5;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .mobile-menu-close i {
        font-size: 16px;
    }

    /* Mobile Menu Items */
    .navbar-nav {
        flex-direction: column;
        padding: 15px;
        margin: 0;
        width: 100%;
    }

    .navbar-nav .nav-item {
        margin: 0;
        padding: 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        width: 100%;
    }

    .navbar-nav .nav-item:last-child {
        border-bottom: none;
    }

    .navbar-nav .nav-item a {
        padding: 12px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .navbar-nav .nav-item a::after {
        display: none;
    }

    /* Mobile Dropdown Menu */
    .navbar-nav .nav-item .sub-menu {
        position: static;
        width: 100%;
        opacity: 1;
        visibility: visible;
        box-shadow: none;
        background-color: #f5f5f5;
        padding: 0;
        border-radius: 0;
        max-height: 0;
        overflow: hidden;
        transform: none;
        transition: max-height 0.3s ease;
    }

    .navbar-nav .nav-item .sub-menu.show {
        max-height: 500px;
    }

    .navbar-nav .nav-item .sub-menu li a {
        padding: 10px 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 14px;
    }

    .navbar-nav .nav-item .sub-menu li:last-child a {
        border-bottom: none;
    }

    /* Mobile Social Icons */
    .mobile-social {
        padding: 15px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .mobile-social ul {
        display: flex;
        justify-content: center;
        padding: 0;
        margin: 0;
        list-style: none;
    }

    .mobile-social ul li {
        margin: 0 10px;
    }

    .mobile-social ul li a {
        width: 35px;
        height: 35px;
        line-height: 35px;
        text-align: center;
        display: block;
        border-radius: 50%;
        color: #333;
        background-color: #f5f5f5;
        font-size: 16px;
        transition: all 0.3s ease;
    }

    .mobile-social ul li a:hover {
        background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
        color: #fff;
    }

    /* Mobile Trial Class Button */
    .mobile-trial-button {
        padding: 15px;
        text-align: center;
    }

    .mobile-trial-btn {
        padding: 10px 20px;
        background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
        color: #fff;
        border-radius: 30px;
        font-weight: 600;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        display: inline-block;
        width: 100%;
    }

    .mobile-trial-btn:hover {
        color: #fff;
        opacity: 0.9;
    }
}
