/*======================================
	Experience CSS
========================================*/

.experience {
    .left-content {
        padding: 50px;
        background: $gray;
    }

    .exp-title {
        span {
            font-style: 14px;
            font-weight: 500;
            color: $theme-color;
            display: block;
            margin-bottom: 10px;
        }

        h2 {
            font-weight: 700;
            font-size: 30px;
            display: block;
            margin-bottom: 20px;
            line-height: 42px;
        }

        p {
            margin: 30px 0;
        }
    }

    .image {
        position: relative;

        img {
            width: 100%;
        }

        h2 {
            line-height: auto;
            font-weight: 700;
            font-size: 40px;
            color: #fff;
            background: #0EDC8D;
            position: absolute;
            right: 0;
            bottom: 0;
            padding: 20px 25px;
            border-radius: 30px 0 0 0;

            .year {
                display: inline-block;
                font-size: 14px;
                margin-left: 2px;
                font-weight: 600;
            }

            .work {
                font-weight: 600;
                font-size: 14px;
                display: block;
                margin-top: -3px;
                text-transform: uppercase;
                line-height: 24px;
            }
        }
    }
}