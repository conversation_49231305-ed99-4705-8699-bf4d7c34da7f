/* Menu Fix CSS for Zajel Arabic Theme */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
}

/* Navigation Menu Fixes */
.navbar-nav {
    display: flex;
    align-items: center;
    margin-right: auto;
}

.navbar-nav .nav-item {
    margin: 0 15px;
}

.navbar-nav .nav-item a {
    white-space: nowrap;
}

/* Trial Class Button */
.trial-class-button {
    margin-left: 20px;
}

/* Responsive Fixes */
@media only screen and (max-width: 991px) {
    .navbar-nav .nav-item {
        margin: 0;
        padding: 8px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .navbar-nav .nav-item:last-child {
        border-bottom: none;
    }

    .trial-class-button {
        margin: 15px 0;
        text-align: center;
    }
}
