<?php
/**
 * The template for displaying course archive
 *
 * @package Zajel_Arabic
 */

get_header();

// Get all course categories
$course_categories = get_terms(array(
    'taxonomy' => 'category',
    'hide_empty' => true,
    'object_ids' => get_posts(array(
        'post_type' => 'course',
        'numberposts' => -1,
        'fields' => 'ids',
    )),
));

// Get all course levels
$course_levels = array();
$courses = get_posts(array(
    'post_type' => 'course',
    'numberposts' => -1,
));

foreach ($courses as $course) {
    $level = get_post_meta($course->ID, 'course_level', true);
    if (!empty($level) && !in_array($level, $course_levels)) {
        $course_levels[] = $level;
    }
}
?>

<!-- Start Breadcrumbs -->
<div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content text-center">
                    <div class="page-icon wow zoomIn" data-wow-delay=".2s">
                        <i class="lni lni-graduation"></i>
                    </div>
                    <h1 class="page-title wow fadeInUp" data-wow-delay=".4s">
                        Our Courses
                    </h1>
                    <p class="wow fadeInUp" data-wow-delay=".6s">
                        Discover our comprehensive Arabic language courses designed to enhance your skills with expert guidance.
                    </p>
                </div>
                <ul class="breadcrumb-nav wow fadeInUp" data-wow-delay=".8s">
                    <li><a href="<?php echo esc_url(home_url('/')); ?>"><i class="lni lni-home"></i> Home</a></li>
                    <li><i class="lni lni-chevron-right"></i></li>
                    <li>Courses</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- End Breadcrumbs -->

<!-- Start Courses Area -->
<section class="courses-archive-area section">
    <div class="container">
        <!-- Filters Section -->
        <div class="course-filters mb-50 wow fadeInUp" data-wow-delay=".2s">
            <div class="row">
                <div class="col-12">
                    <div class="filters-wrapper">
                        <div class="row align-items-center">
                            <div class="col-lg-3 col-md-6 col-12 mb-4 mb-lg-0">
                                <div class="filter-item">
                                    <label for="search-courses"><i class="lni lni-search-alt"></i> Search</label>
                                    <input type="text" id="search-courses" class="form-control" placeholder="Search courses...">
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-12 mb-4 mb-lg-0">
                                <div class="filter-item">
                                    <label for="category-filter"><i class="lni lni-folder"></i> Category</label>
                                    <select id="category-filter" class="form-control">
                                        <option value="">All Categories</option>
                                        <?php foreach ($course_categories as $category) : ?>
                                            <option value="<?php echo esc_attr($category->slug); ?>"><?php echo esc_html($category->name); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-12 mb-4 mb-lg-0">
                                <div class="filter-item">
                                    <label for="level-filter"><i class="lni lni-bar-chart"></i> Level</label>
                                    <select id="level-filter" class="form-control">
                                        <option value="">All Levels</option>
                                        <?php foreach ($course_levels as $level) : ?>
                                            <option value="<?php echo esc_attr($level); ?>"><?php echo esc_html($level); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-12">
                                <div class="filter-item">
                                    <label for="sort-filter"><i class="lni lni-sort-alpha-asc"></i> Sort By</label>
                                    <select id="sort-filter" class="form-control">
                                        <option value="newest">Newest First</option>
                                        <option value="oldest">Oldest First</option>
                                        <option value="title-asc">Title (A-Z)</option>
                                        <option value="title-desc">Title (Z-A)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Filters -->
        <div class="active-filters mb-30 wow fadeInUp" data-wow-delay=".3s" style="display: none;">
            <div class="row">
                <div class="col-12">
                    <div class="active-filters-wrapper">
                        <span class="active-filters-title">Active Filters:</span>
                        <div class="active-filters-tags">
                            <!-- Active filter tags will be added here dynamically -->
                        </div>
                        <button class="clear-all-filters"><i class="lni lni-close"></i> Clear All</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Courses Grid -->
        <div class="courses-grid">
            <div class="row" id="courses-container">
                <?php
                // Query courses
                $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
                $args = array(
                    'post_type' => 'course',
                    'posts_per_page' => 9,
                    'paged' => $paged,
                );

                $courses_query = new WP_Query($args);

                if ($courses_query->have_posts()) :
                    $delay = 0.2;
                    while ($courses_query->have_posts()) : $courses_query->the_post();

                        // Get course meta
                        $course_price = get_post_meta(get_the_ID(), 'course_price', true);
                        $course_duration = get_post_meta(get_the_ID(), 'course_duration', true);
                        $course_level = get_post_meta(get_the_ID(), 'course_level', true);
                        $course_students = get_post_meta(get_the_ID(), 'course_students', true);

                        // Get course categories
                        $categories = get_the_category();
                        $category_classes = '';
                        $category_names = array();

                        foreach ($categories as $category) {
                            $category_classes .= ' category-' . $category->slug;
                            $category_names[] = $category->name;
                        }

                        // Get instructor
                        $instructor_id = get_post_meta(get_the_ID(), 'course_instructor', true);
                        $instructor_name = $instructor_id ? get_the_title($instructor_id) : '';
                ?>
                        <div class="col-lg-4 col-md-6 col-12 mb-4 course-item wow fadeInUp"
                             data-wow-delay="<?php echo esc_attr($delay); ?>s"
                             data-categories="<?php echo esc_attr($category_classes); ?>"
                             data-level="<?php echo esc_attr($course_level); ?>"
                             data-title="<?php echo esc_attr(get_the_title()); ?>"
                             data-date="<?php echo esc_attr(get_the_date('U')); ?>">
                            <div class="single-course">
                                <div class="course-image">
                                    <a href="<?php echo esc_url(str_replace('/courses/', '/course/', get_permalink())); ?>" class="thumbnail">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium_large', array('class' => 'img-fluid', 'alt' => get_the_title())); ?>
                                        <?php else : ?>
                                            <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/default-course.svg'); ?>" alt="<?php the_title_attribute(); ?>" class="img-fluid">
                                        <?php endif; ?>
                                    </a>
                                    <?php if (!empty($course_level)) : ?>
                                        <div class="course-level">
                                            <span><?php echo esc_html($course_level); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="course-content">
                                    <?php if (!empty($category_names)) : ?>
                                        <div class="course-categories">
                                            <?php foreach ($category_names as $index => $name) : ?>
                                                <?php if ($index < 2) : // Show max 2 categories ?>
                                                    <a href="<?php echo esc_url(get_category_link($categories[$index]->term_id)); ?>" class="category">
                                                        <?php echo esc_html($name); ?>
                                                    </a>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                            <?php if (count($category_names) > 2) : ?>
                                                <span class="more-categories">+<?php echo count($category_names) - 2; ?></span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    <h3 class="course-title">
                                        <a href="<?php echo esc_url(str_replace('/courses/', '/course/', get_permalink())); ?>"><?php the_title(); ?></a>
                                    </h3>
                                    <div class="course-meta">
                                        <?php if (!empty($instructor_name)) : ?>
                                            <div class="meta-item instructor">
                                                <i class="lni lni-user"></i>
                                                <span><?php echo esc_html($instructor_name); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($course_duration)) : ?>
                                            <div class="meta-item duration">
                                                <i class="lni lni-timer"></i>
                                                <span><?php echo esc_html($course_duration); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($course_students)) : ?>
                                            <div class="meta-item students">
                                                <i class="lni lni-users"></i>
                                                <span><?php echo esc_html($course_students); ?> students</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="course-excerpt">
                                        <?php echo wp_trim_words(get_the_excerpt(), 15, '...'); ?>
                                    </div>
                                    <div class="course-footer">
                                        <div class="course-price">
                                            <?php if (!empty($course_price) && $course_price !== 'Free') : ?>
                                                <span class="price"><?php echo esc_html($course_price); ?></span>
                                            <?php else : ?>
                                                <span class="price free">Free</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="course-button">
                                            <a href="<?php echo esc_url(str_replace('/courses/', '/course/', get_permalink())); ?>" class="btn btn-outline">View Details</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                <?php
                        $delay += 0.2;
                        if ($delay > 0.8) $delay = 0.2;
                    endwhile;
                    wp_reset_postdata();
                else :
                ?>
                    <div class="col-12">
                        <div class="no-courses-found text-center">
                            <div class="icon">
                                <i class="lni lni-search"></i>
                            </div>
                            <h3>No Courses Found</h3>
                            <p>We couldn't find any courses matching your criteria. Please try different filters or check back later.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- No Results Message (Hidden by default) -->
            <div id="no-courses-message" class="no-courses-found text-center" style="display: none;">
                <div class="icon">
                    <i class="lni lni-search"></i>
                </div>
                <h3>No Courses Found</h3>
                <p>We couldn't find any courses matching your criteria. Please try different filters or check back later.</p>
                <button id="reset-filters" class="btn btn-primary mt-3">Reset Filters</button>
            </div>

            <!-- Pagination -->
            <?php if ($courses_query->max_num_pages > 1) : ?>
                <div class="pagination-area wow fadeInUp" data-wow-delay=".2s">
                    <div class="pagination">
                        <?php
                        $big = 999999999; // need an unlikely integer
                        echo paginate_links(array(
                            'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                            'format' => '?paged=%#%',
                            'current' => max(1, get_query_var('paged')),
                            'total' => $courses_query->max_num_pages,
                            'prev_text' => '<i class="lni lni-chevron-left"></i> Previous',
                            'next_text' => 'Next <i class="lni lni-chevron-right"></i>',
                        ));
                        ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>
<!-- End Courses Area -->



<!-- Add Filtering JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get filter elements
    const searchInput = document.getElementById('search-courses');
    const categoryFilter = document.getElementById('category-filter');
    const levelFilter = document.getElementById('level-filter');
    const sortFilter = document.getElementById('sort-filter');
    const resetFiltersBtn = document.getElementById('reset-filters');
    const clearAllFiltersBtn = document.querySelector('.clear-all-filters');
    const activeFiltersContainer = document.querySelector('.active-filters');
    const activeFiltersTags = document.querySelector('.active-filters-tags');
    const coursesContainer = document.getElementById('courses-container');
    const courseItems = document.querySelectorAll('.course-item');
    const noCoursesMessage = document.getElementById('no-courses-message');

    // Filter function
    function filterCourses() {
        const searchTerm = searchInput.value.toLowerCase();
        const categoryValue = categoryFilter.value;
        const levelValue = levelFilter.value;
        const sortValue = sortFilter.value;

        // Clear active filters
        activeFiltersTags.innerHTML = '';
        let hasActiveFilters = false;

        // Add active filter tags
        if (searchTerm) {
            addActiveFilterTag('Search: ' + searchTerm, 'search');
            hasActiveFilters = true;
        }

        if (categoryValue) {
            const categoryText = categoryFilter.options[categoryFilter.selectedIndex].text;
            addActiveFilterTag('Category: ' + categoryText, 'category');
            hasActiveFilters = true;
        }

        if (levelValue) {
            addActiveFilterTag('Level: ' + levelValue, 'level');
            hasActiveFilters = true;
        }

        // Show/hide active filters section
        activeFiltersContainer.style.display = hasActiveFilters ? 'block' : 'none';

        // Filter courses
        let visibleCount = 0;

        // Convert NodeList to Array for sorting
        const courseItemsArray = Array.from(courseItems);

        // Sort courses
        if (sortValue === 'newest') {
            courseItemsArray.sort((a, b) => {
                return parseInt(b.dataset.date) - parseInt(a.dataset.date);
            });
        } else if (sortValue === 'oldest') {
            courseItemsArray.sort((a, b) => {
                return parseInt(a.dataset.date) - parseInt(b.dataset.date);
            });
        } else if (sortValue === 'title-asc') {
            courseItemsArray.sort((a, b) => {
                return a.dataset.title.localeCompare(b.dataset.title);
            });
        } else if (sortValue === 'title-desc') {
            courseItemsArray.sort((a, b) => {
                return b.dataset.title.localeCompare(a.dataset.title);
            });
        }

        // Reorder courses in the DOM
        courseItemsArray.forEach(item => {
            coursesContainer.appendChild(item);
        });

        // Apply filters
        courseItems.forEach(item => {
            const title = item.querySelector('.course-title').textContent.toLowerCase();
            const excerpt = item.querySelector('.course-excerpt').textContent.toLowerCase();
            const matchesSearch = !searchTerm || title.includes(searchTerm) || excerpt.includes(searchTerm);
            const matchesCategory = !categoryValue || item.dataset.categories.includes('category-' + categoryValue);
            const matchesLevel = !levelValue || item.dataset.level === levelValue;

            if (matchesSearch && matchesCategory && matchesLevel) {
                item.style.display = 'block';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });

        // Show/hide no results message
        if (visibleCount === 0) {
            noCoursesMessage.style.display = 'block';
        } else {
            noCoursesMessage.style.display = 'none';
        }
    }

    // Add active filter tag
    function addActiveFilterTag(text, type) {
        const tag = document.createElement('span');
        tag.className = 'filter-tag';
        tag.dataset.type = type;
        tag.innerHTML = text + ' <i class="lni lni-close"></i>';

        tag.querySelector('i').addEventListener('click', function() {
            removeFilter(type);
        });

        activeFiltersTags.appendChild(tag);
    }

    // Remove filter
    function removeFilter(type) {
        if (type === 'search') {
            searchInput.value = '';
        } else if (type === 'category') {
            categoryFilter.value = '';
        } else if (type === 'level') {
            levelFilter.value = '';
        }

        filterCourses();
    }

    // Reset all filters
    function resetAllFilters() {
        searchInput.value = '';
        categoryFilter.value = '';
        levelFilter.value = '';
        sortFilter.value = 'newest';

        filterCourses();
    }

    // Event listeners
    searchInput.addEventListener('input', filterCourses);
    categoryFilter.addEventListener('change', filterCourses);
    levelFilter.addEventListener('change', filterCourses);
    sortFilter.addEventListener('change', filterCourses);
    resetFiltersBtn.addEventListener('click', resetAllFilters);
    clearAllFiltersBtn.addEventListener('click', resetAllFilters);
});
</script>

<?php
get_footer();
