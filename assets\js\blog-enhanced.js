/**
 * Enhanced Blog Section Scripts
 */
jQuery(document).ready(function($) {
    // Hover effect for blog posts
    $('.single-news').hover(
        function() {
            $(this).addClass('active');
        },
        function() {
            $(this).removeClass('active');
        }
    );

    // Add smooth transition when clicking on blog post links
    $('.single-news a').on('click', function() {
        $(this).closest('.single-news').addClass('clicked');
    });

    // Add touch swipe functionality for mobile
    if ($(window).width() < 768) {
        let touchStartX = 0;
        let touchEndX = 0;
        
        $('.blog-posts-container').on('touchstart', function(e) {
            touchStartX = e.originalEvent.touches[0].clientX;
        });
        
        $('.blog-posts-container').on('touchend', function(e) {
            touchEndX = e.originalEvent.changedTouches[0].clientX;
            handleSwipe();
        });
        
        function handleSwipe() {
            if (touchEndX < touchStartX - 50) {
                // Swipe left - show next post
                showNextPost();
            }
            
            if (touchEndX > touchStartX + 50) {
                // Swipe right - show previous post
                showPrevPost();
            }
        }
        
        function showNextPost() {
            const visiblePost = $('.blog-post-item:visible');
            const nextPost = visiblePost.next('.blog-post-item');
            
            if (nextPost.length) {
                visiblePost.hide();
                nextPost.show();
            }
        }
        
        function showPrevPost() {
            const visiblePost = $('.blog-post-item:visible');
            const prevPost = visiblePost.prev('.blog-post-item');
            
            if (prevPost.length) {
                visiblePost.hide();
                prevPost.show();
            }
        }
        
        // Initially show only the first post on mobile
        $('.blog-post-item').hide();
        $('.blog-post-item:first').show();
    }
});
