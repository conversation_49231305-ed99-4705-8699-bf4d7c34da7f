/* Enhanced Sections CSS for Zajel Arabic Theme */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --transition-slow: all 0.8s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 15px 60px rgba(0, 0, 0, 0.2);
}

/*======================================
    Enhanced Testimonials CSS
========================================*/
.testimonials {
    position: relative;
    padding: 100px 0;
    background: #f9f9f9;
    overflow: hidden;
}

.testimonials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23B8860B' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 0.2;
    z-index: 0;
}

.testimonials .container {
    position: relative;
    z-index: 1;
}

/* Testimonial Slider */
.testimonial-slider {
    position: relative;
    padding: 20px 0;
    overflow: hidden;
}

.testimonial-slider-container {
    display: flex;
    transition: transform 0.5s ease;
    margin: 0 -15px;
}

/* Single Testimonial */
.single-testimonial {
    background-color: #fff;
    border-radius: 15px;
    padding: 35px 30px;
    margin: 15px;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
    position: relative;
    border: 1px solid rgba(2, 92, 97, 0.1);
    overflow: hidden;
    flex: 0 0 calc(33.333% - 30px);
    max-width: calc(33.333% - 30px);
    transform: translateY(0);
    min-height: 250px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.single-testimonial:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-10px);
    border-color: rgba(2, 92, 97, 0.3);
}

.single-testimonial::before {
    content: '\201C';
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 80px;
    color: rgba(2, 92, 97, 0.1);
    font-family: Georgia, serif;
    line-height: 1;
    transition: var(--transition-medium);
    z-index: 0;
}

.single-testimonial:hover::before {
    color: rgba(2, 92, 97, 0.2);
    transform: scale(1.1);
}

/* Testimonial Text */
.single-testimonial .text {
    position: relative;
    z-index: 1;
    margin-bottom: 25px;
    flex: 1;
}

.single-testimonial .text p {
    font-size: 16px;
    line-height: 1.8;
    color: #333;
    margin: 0;
    transition: var(--transition-fast);
    font-weight: 400;
}

.single-testimonial:hover .text p {
    color: #000;
}

/* Testimonial Author */
.single-testimonial .author {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
}

.single-testimonial .author img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: var(--shadow-small);
    margin-right: 15px;
    transition: var(--transition-medium);
}

.single-testimonial:hover .author img {
    border-color: var(--gold-light);
    transform: scale(1.05);
}

.single-testimonial .author .name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    transition: var(--transition-fast);
}

.single-testimonial:hover .author .name {
    color: var(--gold-dark);
}

.single-testimonial .author .deg {
    display: block;
    font-size: 14px;
    color: #777;
    margin-top: 5px;
}

/* Testimonial Navigation */
.testimonial-nav {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.testimonial-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 15px;
}

.testimonial-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ddd;
    margin: 0 5px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.testimonial-dot.active {
    width: 14px;
    height: 14px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
}

.testimonial-arrow {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-small);
    transition: var(--transition-fast);
    color: #333;
    font-size: 18px;
    position: relative;
    overflow: hidden;
}

.testimonial-arrow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 152, 159, 0.2), transparent);
    transition: var(--transition-fast);
}

.testimonial-arrow:hover::before {
    left: 100%;
    transition: 0.7s;
}

.testimonial-arrow:hover {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.testimonial-prev {
    margin-right: 10px;
}

.testimonial-next {
    margin-left: 10px;
}

/*======================================
    Enhanced Teachers CSS
========================================*/
.teachers {
    position: relative;
    padding: 100px 0;
    background-color: #fff;
    overflow: hidden;
}

.teachers::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23B8860B' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 0.5;
    z-index: 0;
}

.teachers .container {
    position: relative;
    z-index: 1;
}

/* Teachers Slider */
.teachers-slider {
    position: relative;
    padding: 20px 0;
    overflow: hidden;
}

.teachers-slider-container {
    display: flex;
    transition: transform 0.5s ease;
    margin: 0 -15px;
}

/* Single Teacher */
.single-team {
    background-color: #fff;
    border-radius: 15px;
    margin: 15px;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
    position: relative;
    border: 1px solid rgba(2, 92, 97, 0.1);
    overflow: hidden;
    flex: 0 0 calc(50% - 30px);
    max-width: calc(50% - 30px);
    transform: translateY(0);
}

.single-team:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-10px);
    border-color: rgba(2, 92, 97, 0.3);
}

.single-team::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 0;
    background: linear-gradient(to bottom, var(--gold-light), var(--gold-dark));
    transition: var(--transition-medium);
    z-index: 1;
}

.single-team:hover::before {
    height: 100%;
}

/* Teacher Image */
.single-team .image {
    overflow: hidden;
    padding: 15px;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.single-team .image img {
    width: 100%;
    max-width: 200px;
    height: auto;
    border-radius: 10px;
    transition: var(--transition-medium);
    box-shadow: var(--shadow-small);
}

.single-team:hover .image img {
    transform: scale(1.05);
}

/* Teacher Info */
.single-team .info-head {
    padding: 20px 25px 25px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.single-team .info-head .info-box {
    margin-bottom: 15px;
}

.single-team .info-head .info-box .designation {
    font-size: 14px;
    font-weight: 600;
    color: var(--gold-dark);
    margin-bottom: 5px;
    display: block;
}

.single-team .info-head .info-box .name {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 10px;
    transition: var(--transition-fast);
}

.single-team .info-head .info-box .name a {
    color: #333;
    text-decoration: none;
}

.single-team:hover .info-head .info-box .name a {
    color: var(--gold-dark);
}

.single-team .info-head .info-box p {
    font-size: 15px;
    line-height: 1.7;
    color: #333;
    margin: 0;
    font-weight: 400;
}

/* Teacher Social */
.single-team .info-head .social {
    display: flex;
    padding: 0;
    margin: 0;
    list-style: none;
}

.single-team .info-head .social li {
    margin-right: 10px;
}

.single-team .info-head .social li:last-child {
    margin-right: 0;
}

.single-team .info-head .social li a {
    width: 35px;
    height: 35px;
    background: rgba(2, 92, 97, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gold-dark);
    font-size: 16px;
    transition: var(--transition-fast);
}

.single-team .info-head .social li a:hover {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    transform: translateY(-3px);
}

/* Teachers Navigation */
.teachers-nav {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.teachers-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 15px;
}

.teachers-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ddd;
    margin: 0 5px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.teachers-dot.active {
    width: 14px;
    height: 14px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
}

.teachers-arrow {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-small);
    transition: var(--transition-fast);
    color: #333;
    font-size: 18px;
    position: relative;
    overflow: hidden;
}

.teachers-arrow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 152, 159, 0.2), transparent);
    transition: var(--transition-fast);
}

.teachers-arrow:hover::before {
    left: 100%;
    transition: 0.7s;
}

.teachers-arrow:hover {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.teachers-prev {
    margin-right: 10px;
}

.teachers-next {
    margin-left: 10px;
}

/* Responsive Styles */
@media only screen and (max-width: 1199px) {
    .single-testimonial {
        flex: 0 0 calc(50% - 30px);
        max-width: calc(50% - 30px);
    }
}

@media only screen and (max-width: 991px) {
    .testimonials,
    .teachers {
        padding: 80px 0;
    }

    .single-team {
        flex: 0 0 calc(100% - 30px);
        max-width: calc(100% - 30px);
    }

    .single-team .row {
        display: flex;
        flex-wrap: wrap;
    }

    .single-team .col-lg-5,
    .single-team .col-lg-7 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .single-team .image {
        text-align: center;
        padding: 20px 20px 0;
    }

    .single-team .image img {
        max-width: 250px;
    }

    .single-team .info-head {
        text-align: center;
    }

    .single-team .info-head .social {
        justify-content: center;
    }
}

@media only screen and (max-width: 767px) {
    .testimonials,
    .teachers {
        padding: 60px 0;
    }

    .single-testimonial {
        flex: 0 0 calc(100% - 30px);
        max-width: calc(100% - 30px);
    }

    .testimonial-arrow,
    .teachers-arrow {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
}

@media only screen and (max-width: 575px) {
    .testimonials,
    .teachers {
        padding: 50px 0;
    }

    .single-testimonial {
        padding: 25px 20px;
    }

    .single-testimonial::before {
        font-size: 80px;
    }

    .single-testimonial .author img {
        width: 60px;
        height: 60px;
    }

    .single-testimonial .author .name {
        font-size: 16px;
    }

    .single-testimonial .author .deg {
        font-size: 12px;
    }

    .testimonial-dot,
    .teachers-dot {
        width: 8px;
        height: 8px;
        margin: 0 3px;
    }

    .testimonial-dot.active,
    .teachers-dot.active {
        width: 12px;
        height: 12px;
    }

    .testimonial-arrow,
    .teachers-arrow {
        width: 30px;
        height: 30px;
        font-size: 14px;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(2, 92, 97, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(2, 92, 97, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(2, 92, 97, 0);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.fade-in {
    animation: fadeIn 1s ease forwards;
}

.fade-in-up {
    animation: fadeInUp 1s ease forwards;
}

.fade-in-down {
    animation: fadeInDown 1s ease forwards;
}

.fade-in-left {
    animation: fadeInLeft 1s ease forwards;
}

.fade-in-right {
    animation: fadeInRight 1s ease forwards;
}

.pulse {
    animation: pulse 2s infinite;
}

.float {
    animation: float 6s ease-in-out infinite;
}

.delay-1 {
    animation-delay: 0.2s;
}

.delay-2 {
    animation-delay: 0.4s;
}

.delay-3 {
    animation-delay: 0.6s;
}

.delay-4 {
    animation-delay: 0.8s;
}

.delay-5 {
    animation-delay: 1s;
}

/* Section Title Animation */
.section-title.animated span {
    animation: fadeInDown 0.5s ease forwards;
}

.section-title.animated h2 {
    animation: fadeInUp 0.7s ease forwards;
}

.section-title.animated p {
    animation: fadeIn 0.9s ease forwards;
}


