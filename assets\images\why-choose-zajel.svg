<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Why <PERSON><PERSON> Arabic</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="goldGradient">
            <stop stop-color="#FFD700" offset="0%"></stop>
            <stop stop-color="#DAA520" offset="50%"></stop>
            <stop stop-color="#B8860B" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="0%" id="lightGradient">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F5F5F5" offset="100%"></stop>
        </linearGradient>
        <filter x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" id="shadow">
            <feGaussianBlur stdDeviation="10" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="10" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background -->
        <rect fill="#F9F9F9" x="0" y="0" width="800" height="600" rx="20"></rect>
        
        <!-- Decorative Elements -->
        <circle fill="#F0F0F0" cx="200" cy="100" r="150" opacity="0.5"></circle>
        <circle fill="#F0F0F0" cx="600" cy="500" r="200" opacity="0.5"></circle>
        
        <!-- Arabic Pattern Background -->
        <g transform="translate(50, 50)" opacity="0.1">
            <path d="M0,0 L700,0 L700,500 L0,500 Z" fill="url(#lightGradient)"></path>
            <!-- Arabic Geometric Pattern -->
            <path d="M0,0 L100,0 L100,100 L0,100 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M100,0 L200,0 L200,100 L100,100 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M200,0 L300,0 L300,100 L200,100 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M300,0 L400,0 L400,100 L300,100 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M400,0 L500,0 L500,100 L400,100 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M500,0 L600,0 L600,100 L500,100 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M600,0 L700,0 L700,100 L600,100 Z" fill="#B8860B" opacity="0.1"></path>
            
            <path d="M0,100 L100,100 L100,200 L0,200 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M100,100 L200,100 L200,200 L100,200 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M200,100 L300,100 L300,200 L200,200 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M300,100 L400,100 L400,200 L300,200 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M400,100 L500,100 L500,200 L400,200 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M500,100 L600,100 L600,200 L500,200 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M600,100 L700,100 L700,200 L600,200 Z" fill="#B8860B" opacity="0.1"></path>
            
            <!-- Continue pattern for remaining rows -->
            <path d="M0,200 L100,200 L100,300 L0,300 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M100,200 L200,200 L200,300 L100,300 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M200,200 L300,200 L300,300 L200,300 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M300,200 L400,200 L400,300 L300,300 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M400,200 L500,200 L500,300 L400,300 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M500,200 L600,200 L600,300 L500,300 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M600,200 L700,200 L700,300 L600,300 Z" fill="#B8860B" opacity="0.1"></path>
            
            <path d="M0,300 L100,300 L100,400 L0,400 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M100,300 L200,300 L200,400 L100,400 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M200,300 L300,300 L300,400 L200,400 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M300,300 L400,300 L400,400 L300,400 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M400,300 L500,300 L500,400 L400,400 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M500,300 L600,300 L600,400 L500,400 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M600,300 L700,300 L700,400 L600,400 Z" fill="#B8860B" opacity="0.1"></path>
            
            <path d="M0,400 L100,400 L100,500 L0,500 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M100,400 L200,400 L200,500 L100,500 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M200,400 L300,400 L300,500 L200,500 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M300,400 L400,400 L400,500 L300,500 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M400,400 L500,400 L500,500 L400,500 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M500,400 L600,400 L600,500 L500,500 Z" fill="#B8860B" opacity="0.1"></path>
            <path d="M600,400 L700,400 L700,500 L600,500 Z" fill="#B8860B" opacity="0.1"></path>
        </g>
        
        <!-- Main Content -->
        <g transform="translate(100, 100)">
            <!-- Main Card -->
            <rect fill="#FFFFFF" x="0" y="0" width="600" height="400" rx="20" filter="url(#shadow)"></rect>
            
            <!-- Left Side - Teacher and Students -->
            <g transform="translate(50, 50)">
                <!-- Teacher -->
                <circle fill="#FFFFFF" stroke="#DAA520" stroke-width="3" cx="150" cy="100" r="50"></circle>
                <circle fill="#DAA520" cx="150" cy="80" r="15"></circle>
                <rect fill="#DAA520" x="135" y="95" width="30" height="40" rx="15"></rect>
                
                <!-- Teacher's Robe -->
                <path d="M100,150 C100,200 200,200 200,150 L200,250 L100,250 Z" fill="#FFFFFF" stroke="#DAA520" stroke-width="2"></path>
                
                <!-- Book -->
                <rect fill="url(#goldGradient)" x="120" y="170" width="60" height="10" rx="2"></rect>
                <rect fill="#FFFFFF" stroke="#DAA520" stroke-width="2" x="120" y="180" width="60" height="40" rx="2"></rect>
                <line x1="150" y1="180" x2="150" y2="220" stroke="#DAA520" stroke-width="2"></line>
                
                <!-- Arabic Text Representation -->
                <g transform="translate(125, 190)">
                    <!-- Right Page -->
                    <path d="M0,0 L20,0" stroke="#B8860B" stroke-width="1.5"></path>
                    <path d="M0,10 L20,10" stroke="#B8860B" stroke-width="1.5"></path>
                    <path d="M0,20 L20,20" stroke="#B8860B" stroke-width="1.5"></path>
                    
                    <!-- Left Page -->
                    <path d="M30,0 L50,0" stroke="#B8860B" stroke-width="1.5"></path>
                    <path d="M30,10 L50,10" stroke="#B8860B" stroke-width="1.5"></path>
                    <path d="M30,20 L50,20" stroke="#B8860B" stroke-width="1.5"></path>
                </g>
                
                <!-- Students -->
                <g transform="translate(250, 50)">
                    <!-- Student 1 -->
                    <circle fill="#FFFFFF" stroke="#DAA520" stroke-width="2" cx="50" cy="50" r="30"></circle>
                    <circle fill="#DAA520" cx="50" cy="40" r="10"></circle>
                    <rect fill="#DAA520" x="40" y="50" width="20" height="25" rx="10"></rect>
                    <path d="M20,80 C20,110 80,110 80,80 L80,150 L20,150 Z" fill="#FFFFFF" stroke="#DAA520" stroke-width="1.5"></path>
                    
                    <!-- Student 2 -->
                    <circle fill="#FFFFFF" stroke="#DAA520" stroke-width="2" cx="150" cy="50" r="30"></circle>
                    <circle fill="#DAA520" cx="150" cy="40" r="10"></circle>
                    <rect fill="#DAA520" x="140" y="50" width="20" height="25" rx="10"></rect>
                    <path d="M120,80 C120,110 180,110 180,80 L180,150 L120,150 Z" fill="#FFFFFF" stroke="#DAA520" stroke-width="1.5"></path>
                    
                    <!-- Student 3 -->
                    <circle fill="#FFFFFF" stroke="#DAA520" stroke-width="2" cx="250" cy="50" r="30"></circle>
                    <circle fill="#DAA520" cx="250" cy="40" r="10"></circle>
                    <rect fill="#DAA520" x="240" y="50" width="20" height="25" rx="10"></rect>
                    <path d="M220,80 C220,110 280,110 280,80 L280,150 L220,150 Z" fill="#FFFFFF" stroke="#DAA520" stroke-width="1.5"></path>
                </g>
                
                <!-- Classroom Elements -->
                <g transform="translate(50, 250)">
                    <!-- Desk -->
                    <rect fill="#F5F5F5" stroke="#DAA520" stroke-width="2" x="0" y="0" width="450" height="20" rx="5"></rect>
                    <rect fill="#F5F5F5" stroke="#DAA520" stroke-width="2" x="10" y="20" width="430" height="10"></rect>
                    
                    <!-- Items on Desk -->
                    <rect fill="#FFFFFF" stroke="#DAA520" stroke-width="1" x="50" y="-15" width="30" height="15" rx="2"></rect>
                    <rect fill="#FFFFFF" stroke="#DAA520" stroke-width="1" x="100" y="-10" width="20" height="10" rx="2"></rect>
                    <circle fill="#DAA520" cx="150" cy="-5" r="5"></circle>
                    <rect fill="#FFFFFF" stroke="#DAA520" stroke-width="1" x="200" y="-15" width="30" height="15" rx="2"></rect>
                    <rect fill="#FFFFFF" stroke="#DAA520" stroke-width="1" x="250" y="-10" width="20" height="10" rx="2"></rect>
                    <circle fill="#DAA520" cx="300" cy="-5" r="5"></circle>
                    <rect fill="#FFFFFF" stroke="#DAA520" stroke-width="1" x="350" y="-15" width="30" height="15" rx="2"></rect>
                </g>
                
                <!-- Arabic Calligraphy Board -->
                <g transform="translate(50, 0)">
                    <rect fill="#FFFFFF" stroke="#DAA520" stroke-width="3" x="0" y="0" width="450" height="30" rx="5"></rect>
                    <text font-family="Arial" font-size="18" font-weight="bold" fill="#B8860B" x="150" y="22">Zajel Arabic Institute</text>
                </g>
            </g>
        </g>
        
        <!-- Decorative Elements -->
        <g transform="translate(50, 50)" opacity="0.5">
            <!-- Stars -->
            <path d="M50,50 L55,60 L65,62 L58,70 L60,80 L50,75 L40,80 L42,70 L35,62 L45,60 Z" fill="url(#goldGradient)"></path>
            <path d="M700,100 L705,110 L715,112 L708,120 L710,130 L700,125 L690,130 L692,120 L685,112 L695,110 Z" fill="url(#goldGradient)"></path>
            <path d="M100,500 L105,510 L115,512 L108,520 L110,530 L100,525 L90,530 L92,520 L85,512 L95,510 Z" fill="url(#goldGradient)"></path>
            <path d="M650,450 L655,460 L665,462 L658,470 L660,480 L650,475 L640,480 L642,470 L635,462 L645,460 Z" fill="url(#goldGradient)"></path>
            
            <!-- Circles -->
            <circle fill="url(#goldGradient)" cx="750" cy="50" r="10" opacity="0.7"></circle>
            <circle fill="url(#goldGradient)" cx="700" cy="550" r="15" opacity="0.7"></circle>
            <circle fill="url(#goldGradient)" cx="50" cy="350" r="12" opacity="0.7"></circle>
            <circle fill="url(#goldGradient)" cx="400" cy="550" r="8" opacity="0.7"></circle>
        </g>
    </g>
</svg>
