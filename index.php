<?php get_header(); ?>

<!-- Start Hero Area -->
<section class="hero-area style2">
    <div class="hero-inner">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-12">
                    <div class="hero-text text-center">
                        <h1 class="wow fadeInUp" data-wow-delay=".5s">Welcome to <span>Zajel Arabic</span></h1>
                        <p class="wow fadeInUp" data-wow-delay=".7s">Our mission is to empower students worldwide with Arabic language and Islamic knowledge through accessible online education</p>
                        <div class="button style2 wow fadeInUp" data-wow-delay=".9s">
                            <a href="javascript:void(0)" class="btn"><i class="lni lni-download"></i> Free Trial</a>
                            <a href="<?php echo esc_url(get_permalink(get_page_by_path('courses'))); ?>" class="btn mouse-dir">Our Courses <span class="dir-part"></span></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--/ End Hero Area -->

<!-- Start Meaning of Zajel Section -->
<section class="about-us section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 offset-lg-2 col-12">
                <div class="about-title align-center">
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">The Meaning of Zajel</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">A long time ago, people used to live in distant places that made travelling difficult to visit a friend or to seek knowledge. To overcome this problem, people used a unique species of birds to exchange and convey messages from one city to another. This practice evolved and an interest developed in raising these kinds of birds as exchange messengers between students and their teachers until it had become the only valuable means of communication. That kind of bird is known as "Zajel".</p>
                    <div class="button wow fadeInUp" data-wow-delay=".8s">
                        <a href="<?php echo esc_url(get_permalink(get_page_by_path('about-us'))); ?>" class="btn mouse-dir">Know More <span class="dir-part"></span></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- /End Meaning of Zajel Section -->

<!-- Start Why Zajel Arabic Section -->
<section class="features section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title align-center">
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">Why Zajel Arabic Institute?</h2>
                    <span class="title-line wow zoomIn" data-wow-delay=".6s"></span>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-4 col-md-6 col-12">
                <div class="single-feature wow fadeInUp" data-wow-delay=".2s">
                    <h3>Qualified Teachers</h3>
                    <p>Our teachers hold ijazah from Al-Azhar Islamic University.</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-12">
                <div class="single-feature wow fadeInUp" data-wow-delay=".4s">
                    <h3>One-to-One personal classes</h3>
                    <p>One to one from the comfort of your own home, at a time and pace that you choose.</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-12">
                <div class="single-feature wow fadeInUp" data-wow-delay=".6s">
                    <h3>Comprehensive Curriculum</h3>
                    <p>Our curriculum is designed to develop all language skills: reading, writing, listening, and speaking.</p>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- /End Why Zajel Arabic Section -->

<!-- Start Achievement Area -->
<section class="our-achievement section overlay">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-3 col-12">
                <div class="single-achievement wow fadeInUp" data-wow-delay=".2s">
                    <h3 class="counter"><span class="countup" cup-end="500">500</span>+</h3>
                    <h4>Happy Students</h4>
                </div>
            </div>
            <div class="col-lg-3 col-md-3 col-12">
                <div class="single-achievement wow fadeInUp" data-wow-delay=".4s">
                    <h3 class="counter"><span class="countup" cup-end="50">50</span>+</h3>
                    <h4>Online Courses</h4>
                </div>
            </div>
            <div class="col-lg-3 col-md-3 col-12">
                <div class="single-achievement wow fadeInUp" data-wow-delay=".6s">
                    <h3 class="counter"><span class="countup" cup-end="100">100</span>%</h3>
                    <h4>Satisfaction</h4>
                </div>
            </div>
            <div class="col-lg-3 col-md-3 col-12">
                <div class="single-achievement wow fadeInUp" data-wow-delay=".8s">
                    <h3 class="counter"><span class="countup" cup-end="100">100</span>%</h3>
                    <h4>Support</h4>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Achievement Area -->

<!-- Start Courses Area -->
<section class="courses style2 section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title">
                    <span class="wow zoomIn" data-wow-delay="0.2s">Online Learning</span>
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">Featured Courses</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">Discover our latest courses designed for non-Arabic speakers at Zajel Arabic.</p>
                </div>
            </div>
        </div>
        <div class="single-head">
            <div class="row">
                <?php
                $args = array(
                    'post_type' => 'course',
                    'posts_per_page' => 6,
                );
                $courses = new WP_Query($args);
                $delay = 0.2;
                if ($courses->have_posts()) :
                    while ($courses->have_posts()) : $courses->the_post();
                        $price = get_post_meta(get_the_ID(), 'course_price', true) ?: 'Free';
                        $date = get_the_date('M d, Y');
                        ?>
                        <div class="col-lg-4 col-md-6 col-12">
                            <div class="single-course wow fadeInUp" data-wow-delay="<?php echo esc_attr($delay); ?>s">
                                <div class="course-image">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', ['alt' => get_the_title()]); ?>
                                        <?php else : ?>
                                            <img src="https://via.placeholder.com/370x230" alt="<?php the_title(); ?>">
                                        <?php endif; ?>
                                    </a>
                                </div>
                                <div class="content">
                                    <p class="price"><?php echo esc_html($price); ?></p>
                                    <p class="date"><?php echo esc_html($date); ?></p>
                                    <h3><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                                </div>
                            </div>
                        </div>
                        <?php
                        $delay += 0.2;
                    endwhile;
                    wp_reset_postdata();
                else :
                    ?>
                    <p>No courses available at the moment. Stay tuned for updates!</p>
                <?php endif; ?>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="button">
                        <a href="<?php echo esc_url(get_permalink(get_page_by_path('courses'))); ?>" class="btn">Browse All Courses</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Courses Area -->

<!-- Start Teachers -->
<section id="teachers" class="teachers section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title align-center gray-bg">
                    <span class="wow zoomIn" data-wow-delay="0.2s">Teachers</span>
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">Our Experienced Advisors</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">Meet our qualified teachers who hold ijazah from Al-Azhar Islamic University, guiding you in Arabic and Islamic studies.</p>
                </div>
            </div>
        </div>
        <div class="row">
            <?php
            $args = array(
                'post_type' => 'teacher',
                'posts_per_page' => 4,
            );
            $teachers = new WP_Query($args);
            $delay = 0.2;
            if ($teachers->have_posts()) :
                while ($teachers->have_posts()) : $teachers->the_post();
                    $designation = get_post_meta(get_the_ID(), 'teacher_designation', true);
                    if (empty($designation)) {
                        // Get courses if available
                        $teacher_courses = get_post_meta(get_the_ID(), 'teacher_courses', true);
                        if (!empty($teacher_courses)) {
                            $courses_array = array_map('trim', explode(',', $teacher_courses));
                            if (!empty($courses_array[0])) {
                                $designation = $courses_array[0] . ' Teacher';
                            } else {
                                $designation = 'Teacher';
                            }
                        } else {
                            $designation = 'Teacher';
                        }
                    }
                    ?>
                    <div class="col-lg-6 col-md-6 col-12">
                        <div class="single-team wow fadeInUp" data-wow-delay="<?php echo esc_attr($delay); ?>s">
                            <div class="row">
                                <div class="col-lg-5 col-12">
                                    <div class="image">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', ['alt' => get_the_title()]); ?>
                                        <?php else : ?>
                                            <img src="https://via.placeholder.com/800x1020" alt="<?php the_title(); ?>">
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-lg-7 col-12">
                                    <div class="info-head">
                                        <div class="info-box">
                                            <span class="designation"><?php echo esc_html($designation); ?></span>
                                            <h4 class="name"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                                            <p><?php echo esc_html(get_the_excerpt()); ?></p>
                                        </div>
                                        <ul class="social">
                                            <li><a href="javascript:void(0)"><i class="lni lni-facebook-filled"></i></a></li>
                                            <li><a href="javascript:void(0)"><i class="lni lni-twitter-original"></i></a></li>
                                            <li><a href="javascript:void(0)"><i class="lni lni-linkedin-original"></i></a></li>
                                            <li><a href="javascript:void(0)"><i class="lni lni-youtube"></i></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                    $delay += 0.2;
                endwhile;
                wp_reset_postdata();
            else :
                ?>
                <p>No teachers available at the moment.</p>
            <?php endif; ?>
        </div>
    </div>
</section>
<!--/ End Teachers Area -->

<!-- Start Testimonials Area -->
<section class="testimonials style2 section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title align-center gray-bg">
                    <span class="wow zoomIn" data-wow-delay="0.2s">Testimonials</span>
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">What Our Students Say</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">Hear from our students about their experiences learning Arabic, Islamic studies, and Quran memorization with Zajel Arabic.</p>
                </div>
            </div>
        </div>
        <div class="row testimonial-slider">
            <?php
            $args = array(
                'post_type' => 'testimonial',
                'posts_per_page' => 6,
            );
            $testimonials = new WP_Query($args);
            if ($testimonials->have_posts()) :
                while ($testimonials->have_posts()) : $testimonials->the_post();
                    $designation = get_post_meta(get_the_ID(), 'testimonial_designation', true) ?: 'Student';
                    ?>
                    <div class="col-lg-4 col-md-6 col-12">
                        <div class="single-testimonial">
                            <div class="text">
                                <p><?php echo esc_html(get_the_content()); ?></p>
                            </div>
                            <div class="author">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('thumbnail', ['alt' => get_the_title()]); ?>
                                <?php else : ?>
                                    <img src="https://via.placeholder.com/300x300" alt="<?php the_title(); ?>">
                                <?php endif; ?>
                                <h4 class="name">
                                    <?php the_title(); ?>
                                    <span class="deg"><?php echo esc_html($designation); ?></span>
                                </h4>
                            </div>
                        </div>
                    </div>
                    <?php
                endwhile;
                wp_reset_postdata();
            else :
                ?>
                <div class="col-12">
                    <p>No testimonials available at the moment.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>
<!-- End Testimonial Area -->

<!-- Start Work Process Area -->
<section class="work-process section">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 offset-lg-3 col-12">
                <div class="section-title align-center gray-bg">
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">How It Works?</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">Follow these simple steps to start your journey with Zajel Arabic and achieve your learning goals.</p>
                </div>
            </div>
        </div>
        <div class="list">
            <div class="row">
                <div class="col-lg-4 col-md-4 col-12">
                    <ul class="wow fadeInUp" data-wow-delay=".2s">
                        <li>
                            <span class="serial">1</span>
                            <p class="content">
                                <span>Schedule a Meeting</span>
                                Contact us to schedule an introductory meeting and discuss your learning goals.
                            </p>
                        </li>
                    </ul>
                </div>
                <div class="col-lg-4 col-md-4 col-12">
                    <ul class="wow fadeInUp" data-wow-delay=".4s">
                        <li>
                            <span class="serial">2</span>
                            <p class="content">
                                <span>Choose Your Course</span>
                                Select a course that fits your needs, from Arabic language to Quran memorization.
                            </p>
                        </li>
                    </ul>
                </div>
                <div class="col-lg-4 col-md-4 col-12">
                    <ul class="wow fadeInUp" data-wow-delay=".6s">
                        <li>
                            <span class="serial">3</span>
                            <p class="content">
                                <span>Start Learning</span>
                                Begin your personalized learning journey with our expert instructors.
                            </p>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- /End Work Process Area -->

<!-- Start Newsletter Area -->
<section class="newsletter-area style2 section">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 offset-lg-3 col-md-12 col-12">
                <div class="newsletter-title">
                    <i class="lni lni-envelope"></i>
                    <h2>Get Our Latest Updates</h2>
                    <p>Subscribe to us to always stay in touch with us and get the latest news about our courses and activities!</p>
                </div>
                <div class="subscribe-text wow fadeInUp" data-wow-delay=".2s">
                    <form action="javascript:void(0)" method="post" class="newsletter-inner">
                        <input name="EMAIL" placeholder="Your email address" class="common-input"
                            onfocus="this.placeholder = ''" onblur="this.placeholder = 'Your email address'"
                            required type="email">
                        <div class="button">
                            <button class="btn">Subscribe</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- /End Newsletter Area -->

<!-- Start Call To Action Area -->
<section class="call-action style2 section overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 col-md-8 col-12">
                <div class="call-content">
                    <h2>Ready to Dive In? <br>Start Your Free Trial Today.</h2>
                </div>
            </div>
            <div class="col-lg-4 col-md-4 col-12">
                <div class="call-content">
                    <div class="button">
                        <a href="javascript:void(0)" class="btn">Get Started for Free</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- /End Call To Action Area -->

<!-- Start Latest News Area -->
<div class="latest-news-area style2 section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title">
                    <span class="wow zoomIn" data-wow-delay="0.2s">Blogs</span>
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">Latest News & Blog</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">Stay updated with the latest news and insights on Arabic learning and Islamic studies.</p>
                </div>
            </div>
        </div>
        <div class="single-head">
            <div class="row">
                <div class="col-lg-3 col-md-12 col-12">
                    <?php
                    $args = array(
                        'post_type' => 'post',
                        'posts_per_page' => 2,
                        'offset' => 1,
                    );
                    $posts = new WP_Query($args);
                    if ($posts->have_posts()) :
                        while ($posts->have_posts()) : $posts->the_post();
                            $category = get_the_category();
                            $category_name = !empty($category) ? $category[0]->name : 'Uncategorized';
                            ?>
                            <div class="single-news wow fadeInUp" data-wow-delay=".2s">
                                <div class="image">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('thumbnail', ['class' => 'thumb', 'alt' => get_the_title()]); ?>
                                        <?php else : ?>
                                            <img class="thumb" src="https://via.placeholder.com/270x200" alt="<?php the_title(); ?>">
                                        <?php endif; ?>
                                    </a>
                                </div>
                                <div class="content-body">
                                    <div class="meta-data">
                                        <ul>
                                            <li>
                                                <i class="lni lni-tag"></i>
                                                <a href="<?php echo esc_url(get_category_link($category[0]->term_id)); ?>"><?php echo esc_html($category_name); ?></a>
                                            </li>
                                        </ul>
                                    </div>
                                    <h4 class="title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    else :
                        ?>
                        <p>No recent posts available.</p>
                    <?php endif; ?>
                </div>
                <div class="col-lg-6 col-md-12 col-12">
                    <?php
                    $args = array(
                        'post_type' => 'post',
                        'posts_per_page' => 1,
                    );
                    $featured_post = new WP_Query($args);
                    if ($featured_post->have_posts()) :
                        while ($featured_post->have_posts()) : $featured_post->the_post();
                            $category = get_the_category();
                            $category_name = !empty($category) ? $category[0]->name : 'Uncategorized';
                            ?>
                            <div class="single-news big custom-shadow-hover wow fadeInUp" data-wow-delay=".4s">
                                <div class="image">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', ['class' => 'thumb', 'alt' => get_the_title()]); ?>
                                        <?php else : ?>
                                            <img class="thumb" src="https://via.placeholder.com/500x390" alt="<?php the_title(); ?>">
                                        <?php endif; ?>
                                    </a>
                                </div>
                                <div class="content-body">
                                    <div class="meta-data">
                                        <ul>
                                            <li>
                                                <i class="lni lni-tag"></i>
                                                <a href="<?php echo esc_url(get_category_link($category[0]->term_id)); ?>"><?php echo esc_html($category_name); ?></a>
                                            </li>
                                            <li>
                                                <i class="lni lni-calendar"></i>
                                                <a href="javascript:void(0)"><?php echo esc_html(get_the_date('F j, Y')); ?></a>
                                            </li>
                                        </ul>
                                    </div>
                                    <h4 class="title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                                    <p><?php echo esc_html(wp_trim_words(get_the_excerpt(), 20, '...')); ?></p>
                                    <div class="button">
                                        <a href="<?php the_permalink(); ?>" class="btn">Read More</a>
                                    </div>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    else :
                        ?>
                        <p>No featured post available.</p>
                    <?php endif; ?>
                </div>
                <div class="col-lg-3 col-md-12 col-12">
                    <?php
                    $args = array(
                        'post_type' => 'post',
                        'posts_per_page' => 2,
                        'offset' => 3,
                    );
                    $posts = new WP_Query($args);
                    if ($posts->have_posts()) :
                        while ($posts->have_posts()) : $posts->the_post();
                            $category = get_the_category();
                            $category_name = !empty($category) ? $category[0]->name : 'Uncategorized';
                            ?>
                            <div class="single-news wow fadeInUp" data-wow-delay=".2s">
                                <div class="image">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('thumbnail', ['class' => 'thumb', 'alt' => get_the_title()]); ?>
                                        <?php else : ?>
                                            <img class="thumb" src="https://via.placeholder.com/270x200" alt="<?php the_title(); ?>">
                                        <?php endif; ?>
                                    </a>
                                </div>
                                <div class="content-body">
                                    <div class="meta-data">
                                        <ul>
                                            <li>
                                                <i class="lni lni-tag"></i>
                                                <a href="<?php echo esc_url(get_category_link($category[0]->term_id)); ?>"><?php echo esc_html($category_name); ?></a>
                                            </li>
                                        </ul>
                                    </div>
                                    <h4 class="title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    else :
                        ?>
                        <p>No recent posts available.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Latest News Area -->

<!-- Start Clients Area -->
<div class="client-logo-section">
    <div class="container">
        <div class="client-logo-wrapper">
            <div class="client-logo-carousel d-flex align-items-center justify-content-between">
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="Client Logo">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="Client Logo">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="Client Logo">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="Client Logo">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="Client Logo">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="Client Logo">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="Client Logo">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="Client Logo">
                </div>
                <div class="client-logo">
                    <img src="https://via.placeholder.com/230x95" alt="Client Logo">
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Clients Area -->

<?php get_footer(); ?>