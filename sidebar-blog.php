<?php
/**
 * The sidebar containing the main widget area for the blog
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package Zajel_Arabic
 */

if ( ! is_active_sidebar( 'sidebar-1' ) ) {
    // If no widgets are active, we'll create a custom sidebar
    ?>
    <div class="custom-sidebar">


        <!-- Categories Widget -->
        <div class="widget widget_categories">
            <h2 class="widget-title"><?php esc_html_e( 'Categories', 'zajel' ); ?></h2>
            <ul>
                <?php
                $categories = get_categories( array(
                    'orderby' => 'name',
                    'order'   => 'ASC'
                ) );

                foreach ( $categories as $category ) {
                    printf(
                        '<li><a href="%1$s">%2$s <span>(%3$s)</span></a></li>',
                        esc_url( get_category_link( $category->term_id ) ),
                        esc_html( $category->name ),
                        esc_html( $category->count )
                    );
                }
                ?>
            </ul>
        </div>

        <!-- Recent Posts Widget -->
        <div class="widget widget_recent_entries">
            <h2 class="widget-title"><?php esc_html_e( 'Recent Posts', 'zajel' ); ?></h2>
            <ul>
                <?php
                $recent_posts = wp_get_recent_posts( array(
                    'numberposts' => 5,
                    'post_status' => 'publish'
                ) );

                foreach ( $recent_posts as $post ) {
                    ?>
                    <li>
                        <div class="post-image">
                            <?php if ( has_post_thumbnail( $post['ID'] ) ) : ?>
                                <?php echo get_the_post_thumbnail( $post['ID'], 'thumbnail' ); ?>
                            <?php else : ?>
                                <img src="<?php echo esc_url( get_template_directory_uri() . '/assets/images/blog-placeholder.svg' ); ?>" alt="<?php echo esc_attr( $post['post_title'] ); ?>">
                            <?php endif; ?>
                        </div>
                        <div class="post-content">
                            <h3 class="post-title">
                                <a href="<?php echo esc_url( get_permalink( $post['ID'] ) ); ?>"><?php echo esc_html( $post['post_title'] ); ?></a>
                            </h3>
                            <div class="post-date">
                                <i class="lni lni-calendar"></i> <?php echo esc_html( get_the_date( '', $post['ID'] ) ); ?>
                            </div>
                        </div>
                    </li>
                    <?php
                }
                wp_reset_postdata();
                ?>
            </ul>
        </div>

        <!-- Tags Widget -->
        <div class="widget widget_tag_cloud">
            <h2 class="widget-title"><?php esc_html_e( 'Tags', 'zajel' ); ?></h2>
            <div class="tagcloud">
                <?php
                $tags = get_tags( array(
                    'orderby' => 'count',
                    'order'   => 'DESC',
                    'number'  => 20
                ) );

                if ( $tags ) {
                    foreach ( $tags as $tag ) {
                        printf(
                            '<a href="%1$s" class="tag-cloud-link">%2$s</a>',
                            esc_url( get_tag_link( $tag->term_id ) ),
                            esc_html( $tag->name )
                        );
                    }
                } else {
                    // If no tags exist, show some default ones
                    $default_tags = array(
                        'Arabic Language' => '#',
                        'Islamic Studies' => '#',
                        'Quran' => '#',
                        'Arabic Grammar' => '#',
                        'Arabic Culture' => '#',
                        'Learning Tips' => '#',
                        'Online Education' => '#',
                        'Language Learning' => '#'
                    );

                    foreach ( $default_tags as $name => $link ) {
                        printf(
                            '<a href="%1$s" class="tag-cloud-link">%2$s</a>',
                            esc_url( $link ),
                            esc_html( $name )
                        );
                    }
                }
                ?>
            </div>
        </div>

        <!-- Archives Widget -->
        <div class="widget widget_archive">
            <h2 class="widget-title"><?php esc_html_e( 'Archives', 'zajel' ); ?></h2>
            <ul>
                <?php
                wp_get_archives( array(
                    'type'            => 'monthly',
                    'limit'           => 6,
                    'format'          => 'html',
                    'show_post_count' => true,
                ) );
                ?>
            </ul>
        </div>
    </div>
    <?php
} else {
    // If widgets are active, display them
    dynamic_sidebar( 'sidebar-1' );
}
?>
