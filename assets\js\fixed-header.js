/**
 * Fixed Header JavaScript for Zajel Arabic Theme
 * Handles fixed header and mobile menu functionality
 */

(function($) {
    'use strict';
    
    // Check if jQuery is available
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded. Fixed header functionality requires jQuery.');
        return;
    }

    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize sticky header
        initStickyHeader();
        
        // Initialize mobile menu
        initMobileMenu();
    });

    /**
     * Initialize sticky header
     */
    function initStickyHeader() {
        var header = $('.header.navbar-area');
        
        $(window).on('scroll', function() {
            if ($(this).scrollTop() > 100) {
                header.addClass('sticky');
            } else {
                header.removeClass('sticky');
            }
        });
    }

    /**
     * Initialize mobile menu
     */
    function initMobileMenu() {
        // Variables
        var body = $('body');
        var mobileMenuBtn = $('.navbar-toggler');
        var navbarCollapse = $('.navbar-collapse');
        
        // Create mobile menu overlay
        if ($('.mobile-menu-overlay').length === 0) {
            body.append('<div class="mobile-menu-overlay"></div>');
        }
        
        var mobileMenuOverlay = $('.mobile-menu-overlay');
        
        // Add mobile menu logo if not exists
        if ($('.mobile-menu-logo').length === 0) {
            var logoSrc = '';
            var logoText = '';
            
            if ($('.navbar-brand img').length > 0) {
                logoSrc = $('.navbar-brand img').attr('src');
            } else if ($('.default-logo').length > 0) {
                logoText = $('.default-logo').html();
            }
            
            var mobileMenuLogo = '<div class="mobile-menu-logo">';
            
            if (logoSrc) {
                mobileMenuLogo += '<a href="' + window.location.origin + '"><img src="' + logoSrc + '" alt="Logo"></a>';
            } else if (logoText) {
                mobileMenuLogo += '<a href="' + window.location.origin + '">' + logoText + '</a>';
            }
            
            mobileMenuLogo += '<div class="mobile-menu-close"><i class="fas fa-times"></i></div>';
            mobileMenuLogo += '</div>';
            
            navbarCollapse.prepend(mobileMenuLogo);
        }
        
        // Add mobile social icons if not exists
        if ($('.mobile-social').length === 0) {
            var socialIcons = '';
            
            // Check if there are social icons in the header
            if ($('.header-social').length > 0) {
                socialIcons = $('.header-social').html();
            } else {
                // Create default social icons
                socialIcons = '<ul>' +
                    '<li><a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a></li>' +
                    '<li><a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a></li>' +
                    '<li><a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a></li>' +
                    '<li><a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a></li>' +
                    '</ul>';
            }
            
            navbarCollapse.append('<div class="mobile-social">' + socialIcons + '</div>');
        }
        
        // Toggle mobile menu
        mobileMenuBtn.on('click', function() {
            navbarCollapse.toggleClass('show');
            mobileMenuOverlay.toggleClass('active');
            body.toggleClass('menu-open');
        });
        
        // Close mobile menu
        $('.mobile-menu-close, .mobile-menu-overlay').on('click', function() {
            navbarCollapse.removeClass('show');
            mobileMenuOverlay.removeClass('active');
            body.removeClass('menu-open');
        });
        
        // Toggle submenu
        $('.navbar-nav .nav-item.dropdown > a').on('click', function(e) {
            // Only for mobile view
            if (window.innerWidth <= 991) {
                e.preventDefault();
                $(this).toggleClass('active');
                $(this).next('.dropdown-menu').toggleClass('show');
            }
        });
        
        // Close menu on window resize
        $(window).on('resize', function() {
            if (window.innerWidth > 991) {
                navbarCollapse.removeClass('show');
                mobileMenuOverlay.removeClass('active');
                body.removeClass('menu-open');
            }
        });
    }

})(jQuery);
