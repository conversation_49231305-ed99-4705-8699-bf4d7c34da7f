/**
 * File customizer.js.
 *
 * Theme Customizer enhancements for a better user experience.
 *
 * Contains handlers to make Theme Customizer preview reload changes asynchronously.
 */

( function( $ ) {

    // Site title and description.
    wp.customize( 'blogname', function( value ) {
        value.bind( function( to ) {
            $( '.site-title a' ).text( to );
        } );
    } );
    wp.customize( 'blogdescription', function( value ) {
        value.bind( function( to ) {
            $( '.site-description' ).text( to );
        } );
    } );

    // Header text color.
    wp.customize( 'header_textcolor', function( value ) {
        value.bind( function( to ) {
            if ( 'blank' === to ) {
                $( '.site-title, .site-description' ).css( {
                    'clip': 'rect(1px, 1px, 1px, 1px)',
                    'position': 'absolute'
                } );
            } else {
                $( '.site-title, .site-description' ).css( {
                    'clip': 'auto',
                    'position': 'relative'
                } );
                $( '.site-title a, .site-description' ).css( {
                    'color': to
                } );
            }
        } );
    } );

    // Hero Section
    wp.customize( 'zajel_hero_subtitle', function( value ) {
        value.bind( function( to ) {
            $( '.hero-subtitle' ).text( to );
        } );
    } );

    wp.customize( 'zajel_hero_title_1', function( value ) {
        value.bind( function( to ) {
            $( '.hero-title' ).contents().first().replaceWith( to + ' ' );
        } );
    } );

    wp.customize( 'zajel_hero_title_2', function( value ) {
        value.bind( function( to ) {
            $( '.hero-title span' ).text( to );
        } );
    } );

    wp.customize( 'zajel_hero_description', function( value ) {
        value.bind( function( to ) {
            $( '.hero-description' ).text( to );
        } );
    } );

    wp.customize( 'zajel_hero_button_1_text', function( value ) {
        value.bind( function( to ) {
            $( '.hero-buttons .btn:first-child' ).text( to );
        } );
    } );

    wp.customize( 'zajel_hero_button_2_text', function( value ) {
        value.bind( function( to ) {
            $( '.hero-buttons .btn-outline' ).text( to );
        } );
    } );

    // About Section
    wp.customize( 'zajel_about_title', function( value ) {
        value.bind( function( to ) {
            $( '.content-title' ).text( to );
        } );
    } );

    wp.customize( 'zajel_about_description', function( value ) {
        value.bind( function( to ) {
            $( '.content-description' ).html( to );
        } );
    } );

    wp.customize( 'zajel_about_button_text', function( value ) {
        value.bind( function( to ) {
            $( '.content-text .btn-outline' ).text( to );
        } );
    } );

    // Features Section
    wp.customize( 'zajel_features_title', function( value ) {
        value.bind( function( to ) {
            $( '.features-section .section-title h2' ).text( to );
        } );
    } );

    wp.customize( 'zajel_feature_1_title', function( value ) {
        value.bind( function( to ) {
            $( '.features-container .feature-box:nth-child(1) .feature-title' ).text( to );
        } );
    } );

    wp.customize( 'zajel_feature_1_description', function( value ) {
        value.bind( function( to ) {
            $( '.features-container .feature-box:nth-child(1) p' ).text( to );
        } );
    } );

    wp.customize( 'zajel_feature_2_title', function( value ) {
        value.bind( function( to ) {
            $( '.features-container .feature-box:nth-child(2) .feature-title' ).text( to );
        } );
    } );

    wp.customize( 'zajel_feature_2_description', function( value ) {
        value.bind( function( to ) {
            $( '.features-container .feature-box:nth-child(2) p' ).text( to );
        } );
    } );

    wp.customize( 'zajel_feature_3_title', function( value ) {
        value.bind( function( to ) {
            $( '.features-container .feature-box:nth-child(3) .feature-title' ).text( to );
        } );
    } );

    wp.customize( 'zajel_feature_3_description', function( value ) {
        value.bind( function( to ) {
            $( '.features-container .feature-box:nth-child(3) p' ).text( to );
        } );
    } );

    // CTA Section
    wp.customize( 'zajel_cta_title', function( value ) {
        value.bind( function( to ) {
            $( '.cta-title' ).text( to );
        } );
    } );

    wp.customize( 'zajel_cta_description', function( value ) {
        value.bind( function( to ) {
            $( '.cta-description' ).text( to );
        } );
    } );

    wp.customize( 'zajel_cta_button_text', function( value ) {
        value.bind( function( to ) {
            $( '.cta-section .btn' ).text( to );
        } );
    } );

} )( jQuery );