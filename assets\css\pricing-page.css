/* Pricing Page Styles */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
    --dark-bg: #333333;
    --darker-bg: #222222;
}

/* Hero Section */
.pricing-hero {
    background-color: #f9f9f9;
    padding: 120px 0 60px;
    text-align: center;
    position: relative;
}

.pricing-hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 15px;
}

.pricing-badge {
    display: inline-block;
    background-color: #e6f7f8;
    color: var(--gold-dark);
    font-weight: 600;
    padding: 8px 20px;
    border-radius: 30px;
    margin-bottom: 20px;
    font-size: 16px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.pricing-hero h1 {
    font-size: 42px;
    font-weight: 700;
    margin-bottom: 25px;
    color: var(--dark-bg);
    line-height: 1.3;
    position: relative;
}

.pricing-hero h1:after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, var(--gold-light), var(--gold-dark));
}

.pricing-hero p {
    font-size: 18px;
    color: #666;
    margin-top: 30px;
    line-height: 1.6;
}

/* Section Title */
.pricing-page .section-title {
    text-align: center;
    margin-bottom: 50px;
}

.pricing-page .section-title span {
    color: var(--gold-medium);
    font-weight: 600;
    display: block;
    margin-bottom: 10px;
    font-size: 18px;
}

.pricing-page .section-title h2 {
    font-size: 36px;
    margin-bottom: 20px;
    font-weight: 700;
}

.pricing-page .section-title p {
    max-width: 700px;
    margin: 0 auto;
    color: #666;
}

/* Pricing Toggle */
.pricing-toggle {
    text-align: center;
    margin-bottom: 50px;
}

.pricing-toggle .toggle-wrapper {
    display: inline-flex;
    background: #f5f5f5;
    border-radius: 50px;
    padding: 5px;
    position: relative;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.pricing-toggle .toggle-option {
    padding: 15px 30px;
    cursor: pointer;
    position: relative;
    z-index: 1;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.pricing-toggle .toggle-option.active {
    color: #fff;
}

.pricing-toggle .toggle-option.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    border-radius: 50px;
    z-index: -1;
    transition: all 0.3s ease;
}

.pricing-toggle .toggle-icon {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pricing-toggle .toggle-icon i {
    font-size: 18px;
}

/* Pricing Plans */
.pricing-plans {
    margin-bottom: 70px;
}

.single-pricing-plan {
    background: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.5s ease;
    position: relative;
    margin-bottom: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.single-pricing-plan:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.single-pricing-plan.popular {
    border: 2px solid var(--gold-dark);
    transform: scale(1.03);
}

.single-pricing-plan.popular:hover {
    transform: scale(1.03) translateY(-10px);
}

.popular-tag {
    position: absolute;
    top: 0;
    right: 0;
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    padding: 5px 15px;
    font-size: 14px;
    font-weight: 600;
    transform: rotate(45deg) translateX(20px) translateY(-10px);
    width: 120px;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.plan-header {
    background: var(--gold-dark);
    padding: 20px;
    text-align: center;
    position: relative;
}

.plan-header h3 {
    color: #fff;
    margin: 0;
    font-size: 22px;
    font-weight: 700;
}

.plan-price {
    background: var(--dark-bg);
    padding: 30px 20px;
    text-align: center;
    color: #fff;
    position: relative;
}

.plan-price .currency {
    font-size: 24px;
    font-weight: 600;
    vertical-align: top;
    position: relative;
    top: 10px;
}

.plan-price .amount {
    font-size: 60px;
    font-weight: 700;
    line-height: 1;
}

.plan-price .duration {
    display: block;
    font-size: 14px;
    margin-top: 5px;
    opacity: 0.8;
}

.plan-price .old-price {
    text-decoration: line-through;
    opacity: 0.6;
    font-size: 18px;
    display: block;
    margin-bottom: 5px;
}

.plan-features {
    padding: 30px 20px;
    flex-grow: 1;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.plan-features ul li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}

.plan-features ul li:last-child {
    border-bottom: none;
}

.plan-features ul li i {
    color: var(--gold-dark);
    margin-right: 10px;
    font-size: 16px;
}

.plan-features .discount {
    font-weight: 600;
    color: var(--gold-dark);
}

.plan-button {
    padding: 0 20px 30px;
    text-align: center;
}

.plan-button .btn {
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(3, 53, 92, 0.3);
}

.plan-button .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(3, 53, 92, 0.4);
}

/* Pricing Features */
.pricing-features-wrapper {
    background: #f9f9f9;
    border-radius: 15px;
    padding: 50px 30px;
    margin-bottom: 70px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
}

.pricing-features-wrapper h3 {
    text-align: center;
    margin-bottom: 40px;
    font-size: 28px;
    font-weight: 700;
    color: var(--dark-bg);
    position: relative;
}

.pricing-features-wrapper h3:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--gold-light), var(--gold-dark));
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card {
    position: relative;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-content {
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: flex-start;
    height: 100%;
    position: relative;
    overflow: hidden;
    border-left: 3px solid var(--gold-medium);
}

.feature-icon {
    width: 60px;
    height: 60px;
    min-width: 60px;
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    box-shadow: 0 5px 15px rgba(3, 53, 92, 0.3);
}

.feature-icon i {
    color: #fff;
    font-size: 24px;
}

.feature-text {
    flex: 1;
}

.feature-text h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--dark-bg);
}

.feature-text p {
    font-size: 14px;
    color: #666;
    margin: 0;
    line-height: 1.5;
}

.feature-item h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}

.feature-item p {
    color: #666;
    font-size: 14px;
}

/* FAQ Section */
.pricing-faq {
    margin-bottom: 70px;
}

.faq-item {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.05);
}

.faq-header {
    padding: 20px;
    background: #f5f5f5;
    cursor: pointer;
    position: relative;
}

.faq-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.faq-header h4 i {
    color: var(--gold-dark);
    margin-right: 10px;
    font-size: 20px;
}

.faq-content {
    padding: 20px;
    display: none;
}

.faq-item.active .faq-content {
    display: block;
}

/* Call to Action */
.pricing-cta {
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    border-radius: 15px;
    padding: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 50px;
    box-shadow: 0 10px 30px rgba(3, 53, 92, 0.2);
}

.cta-content {
    color: #fff;
}

.cta-content h3,
.cta-content p {
    color: #fff;
}

.cta-content h3 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
}

.cta-content p {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
}

.cta-button .btn-alt {
    background: #fff;
    color: var(--gold-dark);
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cta-button .btn-alt:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(3, 53, 92, 0.15);
    background: var(--gold-dark);
    color: #fff;
}

/* Responsive Styles */
@media (max-width: 1199px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
        padding: 0 15px;
    }
}

@media (max-width: 991px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .pricing-cta {
        flex-direction: column;
        text-align: center;
    }

    .cta-content {
        margin-bottom: 20px;
    }

    .pricing-features-wrapper {
        padding: 30px 20px;
    }

    .feature-content {
        padding: 15px;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        min-width: 50px;
        font-size: 20px;
        margin-right: 12px;
    }

    .feature-text h4 {
        font-size: 16px;
        margin-bottom: 5px;
    }

    .feature-text p {
        font-size: 13px;
    }
}

@media (max-width: 767px) {
    .pricing-hero {
        padding: 100px 0 40px;
    }

    .pricing-hero h1 {
        font-size: 32px;
    }

    .pricing-hero p {
        font-size: 16px;
    }

    .pricing-hero br {
        display: none;
    }

    .pricing-page .section-title h2 {
        font-size: 28px;
    }

    .pricing-toggle .toggle-option {
        padding: 12px 20px;
    }

    .plan-price .amount {
        font-size: 48px;
    }

    .pricing-features-wrapper {
        padding: 25px 15px;
        margin-bottom: 40px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        max-width: 450px;
    }

    .feature-card {
        max-width: 100%;
    }

    .pricing-cta {
        padding: 30px 20px;
    }

    .pricing-features-wrapper h3 {
        font-size: 24px;
        margin-bottom: 30px;
    }

    .feature-content {
        padding: 15px;
    }

    .feature-icon {
        width: 45px;
        height: 45px;
        min-width: 45px;
        font-size: 18px;
        margin-right: 10px;
    }
}

@media (max-width: 575px) {
    .pricing-hero {
        padding: 90px 0 30px;
    }

    .pricing-badge {
        font-size: 14px;
        padding: 6px 15px;
    }

    .pricing-hero h1 {
        font-size: 26px;
        margin-bottom: 20px;
    }

    .pricing-hero h1:after {
        width: 70px;
        bottom: -10px;
    }

    .pricing-hero p {
        font-size: 14px;
        margin-top: 20px;
    }

    .pricing-toggle .toggle-option {
        padding: 10px 15px;
    }

    .toggle-icon i {
        font-size: 14px;
    }

    .toggle-icon span {
        font-size: 14px;
    }

    .pricing-features-wrapper {
        padding: 20px 10px;
    }

    .pricing-features-wrapper h3 {
        font-size: 22px;
    }

    .features-grid {
        gap: 12px;
    }

    .feature-content {
        padding: 12px;
    }

    .feature-icon {
        width: 40px;
        height: 40px;
        min-width: 40px;
        font-size: 16px;
        margin-right: 10px;
    }

    .feature-text h4 {
        font-size: 15px;
        margin-bottom: 3px;
    }

    .feature-text p {
        font-size: 12px;
        line-height: 1.4;
    }

    .pricing-cta h3 {
        font-size: 22px;
    }

    .pricing-cta p {
        font-size: 14px;
    }

    .cta-button .btn-alt {
        padding: 12px 20px;
        font-size: 14px;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.single-pricing-plan.popular {
    animation: pulse 2s infinite;
}

/* Hover Effects */
.feature-icon {
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    transform: rotateY(180deg);
}

.faq-item:hover {
    transform: translateX(5px);
}

/* Additional Animations for Mobile */
@media (max-width: 767px) {
    .single-pricing-plan {
        max-width: 320px;
        margin-left: auto;
        margin-right: auto;
    }
}
