<?php
// معالج بسيط للفورمز بدون session
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    // معالجة Contact Form
    if (isset($_POST['contact_nonce']) && wp_verify_nonce($_POST['contact_nonce'], 'contact_form_action')) {
        
        if (!empty($_POST['name']) && !empty($_POST['email']) && !empty($_POST['message'])) {
            
            global $wpdb;
            $table_name = $wpdb->prefix . 'contact';
            
            $result = $wpdb->insert(
                $table_name,
                array(
                    'name' => sanitize_text_field($_POST['name']),
                    'subject' => sanitize_text_field($_POST['subject']),
                    'email' => sanitize_email($_POST['email']),
                    'phone' => sanitize_text_field($_POST['phone']),
                    'message' => sanitize_textarea_field($_POST['message']),
                    'created_at' => current_time('mysql'),
                ),
                array('%s', '%s', '%s', '%s', '%s', '%s')
            );
            
            if ($result !== false) {
                echo '<div class="alert alert-success">Message sent successfully!</div>';
            } else {
                echo '<div class="alert alert-danger">Database error: ' . $wpdb->last_error . '</div>';
            }
        } else {
            echo '<div class="alert alert-danger">Please fill all required fields.</div>';
        }
    }
    
    // معالجة Trial Form
    if (isset($_POST['trial_form_nonce']) && wp_verify_nonce($_POST['trial_form_nonce'], 'trial_form_action')) {
        
        if (!empty($_POST['full_name']) && !empty($_POST['email']) && !empty($_POST['phone']) && !empty($_POST['course'])) {
            
            global $wpdb;
            $table_name = $wpdb->prefix . 'trial_bookings';
            
            $result = $wpdb->insert(
                $table_name,
                array(
                    'full_name' => sanitize_text_field($_POST['full_name']),
                    'email' => sanitize_email($_POST['email']),
                    'phone' => sanitize_text_field($_POST['phone']),
                    'course' => sanitize_text_field($_POST['course']),
                    'additional_notes' => sanitize_textarea_field($_POST['additional_notes']),
                    'ip_address' => $_SERVER['REMOTE_ADDR'],
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'],
                    'created_at' => current_time('mysql'),
                ),
                array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
            );
            
            if ($result !== false) {
                echo '<div class="alert alert-success">Trial booking submitted successfully!</div>';
            } else {
                echo '<div class="alert alert-danger">Database error: ' . $wpdb->last_error . '</div>';
            }
        } else {
            echo '<div class="alert alert-danger">Please fill all required fields.</div>';
        }
    }
}
?>
