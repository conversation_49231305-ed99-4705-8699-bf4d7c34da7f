<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>About Zajel Arabic</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="goldGradient">
            <stop stop-color="#FFD700" offset="0%"></stop>
            <stop stop-color="#DAA520" offset="50%"></stop>
            <stop stop-color="#B8860B" offset="100%"></stop>
        </linearGradient>
        <filter x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" id="shadow">
            <feGaussianBlur stdDeviation="10" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="10" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background -->
        <rect fill="#F9F9F9" x="0" y="0" width="800" height="600" rx="20"></rect>
        
        <!-- Decorative Elements -->
        <circle fill="#F0F0F0" cx="200" cy="100" r="150" opacity="0.5"></circle>
        <circle fill="#F0F0F0" cx="600" cy="500" r="200" opacity="0.5"></circle>
        
        <!-- Main Content -->
        <g transform="translate(100, 50)">
            <!-- Building -->
            <rect fill="#FFFFFF" x="0" y="0" width="600" height="400" rx="20" filter="url(#shadow)"></rect>
            
            <!-- Institute Building -->
            <g transform="translate(50, 50)">
                <!-- Main Building -->
                <rect fill="#FFFFFF" stroke="#DAA520" stroke-width="3" x="100" y="50" width="300" height="200" rx="10"></rect>
                
                <!-- Roof -->
                <path d="M50,50 L250,0 L450,50 Z" fill="#DAA520"></path>
                
                <!-- Windows -->
                <rect fill="#F5F5F5" stroke="#DAA520" stroke-width="2" x="130" y="80" width="60" height="80" rx="5"></rect>
                <rect fill="#F5F5F5" stroke="#DAA520" stroke-width="2" x="220" y="80" width="60" height="80" rx="5"></rect>
                <rect fill="#F5F5F5" stroke="#DAA520" stroke-width="2" x="310" y="80" width="60" height="80" rx="5"></rect>
                
                <!-- Door -->
                <rect fill="#DAA520" x="220" y="180" width="60" height="70" rx="5"></rect>
                <rect fill="#FFFFFF" x="245" y="200" width="10" height="30" rx="2"></rect>
                
                <!-- Steps -->
                <rect fill="#F5F5F5" stroke="#DAA520" stroke-width="1" x="210" y="250" width="80" height="10" rx="2"></rect>
                <rect fill="#F5F5F5" stroke="#DAA520" stroke-width="1" x="200" y="260" width="100" height="10" rx="2"></rect>
                
                <!-- Pillars -->
                <rect fill="#DAA520" x="100" y="50" width="10" height="200" rx="2"></rect>
                <rect fill="#DAA520" x="390" y="50" width="10" height="200" rx="2"></rect>
                
                <!-- Flag -->
                <rect fill="#DAA520" x="450" y="50" width="5" height="150"></rect>
                <path d="M455,50 L505,70 L455,90 Z" fill="#FFFFFF" stroke="#DAA520" stroke-width="1"></path>
                
                <!-- Arabic Text on Building -->
                <text font-family="Arial" font-size="24" font-weight="bold" fill="#B8860B" x="160" y="170">Zajel Arabic</text>
                
                <!-- People -->
                <g transform="translate(50, 270)">
                    <!-- Person 1 -->
                    <circle fill="#DAA520" cx="50" cy="30" r="15"></circle>
                    <rect fill="#DAA520" x="40" y="45" width="20" height="30" rx="10"></rect>
                    
                    <!-- Person 2 -->
                    <circle fill="#DAA520" cx="100" cy="30" r="15"></circle>
                    <rect fill="#DAA520" x="90" y="45" width="20" height="30" rx="10"></rect>
                    
                    <!-- Person 3 -->
                    <circle fill="#DAA520" cx="300" cy="30" r="15"></circle>
                    <rect fill="#DAA520" x="290" y="45" width="20" height="30" rx="10"></rect>
                    
                    <!-- Person 4 -->
                    <circle fill="#DAA520" cx="350" cy="30" r="15"></circle>
                    <rect fill="#DAA520" x="340" y="45" width="20" height="30" rx="10"></rect>
                </g>
                
                <!-- Trees -->
                <g transform="translate(0, 200)">
                    <!-- Tree 1 -->
                    <rect fill="#B8860B" x="30" y="50" width="10" height="50"></rect>
                    <circle fill="#DAA520" opacity="0.7" cx="35" cy="30" r="25"></circle>
                    
                    <!-- Tree 2 -->
                    <rect fill="#B8860B" x="460" y="50" width="10" height="50"></rect>
                    <circle fill="#DAA520" opacity="0.7" cx="465" cy="30" r="25"></circle>
                </g>
                
                <!-- Sun -->
                <circle fill="url(#goldGradient)" cx="50" cy="50" r="30" opacity="0.7"></circle>
                <g transform="translate(50, 50)">
                    <line x1="-40" y1="0" x2="-50" y2="0" stroke="#FFD700" stroke-width="3"></line>
                    <line x1="40" y1="0" x2="50" y2="0" stroke="#FFD700" stroke-width="3"></line>
                    <line x1="0" y1="-40" x2="0" y2="-50" stroke="#FFD700" stroke-width="3"></line>
                    <line x1="0" y1="40" x2="0" y2="50" stroke="#FFD700" stroke-width="3"></line>
                    <line x1="-28" y1="-28" x2="-35" y2="-35" stroke="#FFD700" stroke-width="3"></line>
                    <line x1="28" y1="-28" x2="35" y2="-35" stroke="#FFD700" stroke-width="3"></line>
                    <line x1="-28" y1="28" x2="-35" y2="35" stroke="#FFD700" stroke-width="3"></line>
                    <line x1="28" y1="28" x2="35" y2="35" stroke="#FFD700" stroke-width="3"></line>
                </g>
                
                <!-- Clouds -->
                <g transform="translate(350, 30)" opacity="0.7">
                    <circle fill="#FFFFFF" cx="0" cy="0" r="15"></circle>
                    <circle fill="#FFFFFF" cx="15" cy="0" r="20"></circle>
                    <circle fill="#FFFFFF" cx="35" cy="0" r="15"></circle>
                    <circle fill="#FFFFFF" cx="50" cy="0" r="10"></circle>
                </g>
                
                <g transform="translate(150, 20)" opacity="0.7">
                    <circle fill="#FFFFFF" cx="0" cy="0" r="10"></circle>
                    <circle fill="#FFFFFF" cx="15" cy="0" r="15"></circle>
                    <circle fill="#FFFFFF" cx="30" cy="0" r="10"></circle>
                </g>
            </g>
        </g>
        
        <!-- Decorative Elements -->
        <g transform="translate(50, 50)" opacity="0.5">
            <!-- Stars -->
            <path d="M50,50 L55,60 L65,62 L58,70 L60,80 L50,75 L40,80 L42,70 L35,62 L45,60 Z" fill="url(#goldGradient)"></path>
            <path d="M700,100 L705,110 L715,112 L708,120 L710,130 L700,125 L690,130 L692,120 L685,112 L695,110 Z" fill="url(#goldGradient)"></path>
            <path d="M100,500 L105,510 L115,512 L108,520 L110,530 L100,525 L90,530 L92,520 L85,512 L95,510 Z" fill="url(#goldGradient)"></path>
            <path d="M650,450 L655,460 L665,462 L658,470 L660,480 L650,475 L640,480 L642,470 L635,462 L645,460 Z" fill="url(#goldGradient)"></path>
            
            <!-- Circles -->
            <circle fill="url(#goldGradient)" cx="750" cy="50" r="10" opacity="0.7"></circle>
            <circle fill="url(#goldGradient)" cx="700" cy="550" r="15" opacity="0.7"></circle>
            <circle fill="url(#goldGradient)" cx="50" cy="350" r="12" opacity="0.7"></circle>
            <circle fill="url(#goldGradient)" cx="400" cy="550" r="8" opacity="0.7"></circle>
        </g>
        
        <!-- Arabic Calligraphy Decoration -->
        <g transform="translate(400, 500)" opacity="0.3">
            <path d="M0,0 C10,-10 20,-5 30,0 C40,5 50,10 60,0 C70,-10 80,-5 90,0 C100,5 110,10 120,0" stroke="#B8860B" stroke-width="3" fill="none"></path>
            <path d="M0,20 C10,10 20,15 30,20 C40,25 50,30 60,20 C70,10 80,15 90,20 C100,25 110,30 120,20" stroke="#B8860B" stroke-width="3" fill="none"></path>
            <path d="M0,40 C10,30 20,35 30,40 C40,45 50,50 60,40 C70,30 80,35 90,40 C100,45 110,50 120,40" stroke="#B8860B" stroke-width="3" fill="none"></path>
        </g>
        
        <g transform="translate(100, 500)" opacity="0.3">
            <path d="M0,0 C10,-10 20,-5 30,0 C40,5 50,10 60,0 C70,-10 80,-5 90,0 C100,5 110,10 120,0" stroke="#B8860B" stroke-width="3" fill="none"></path>
            <path d="M0,20 C10,10 20,15 30,20 C40,25 50,30 60,20 C70,10 80,15 90,20 C100,25 110,30 120,20" stroke="#B8860B" stroke-width="3" fill="none"></path>
            <path d="M0,40 C10,30 20,35 30,40 C40,45 50,50 60,40 C70,30 80,35 90,40 C100,45 110,50 120,40" stroke="#B8860B" stroke-width="3" fill="none"></path>
        </g>
    </g>
</svg>
