/* Custom Contact Form 7 Styles for Zajel Arabic Theme */

/* Form Container */
.wpcf7 {
    width: 100%;
}

/* Form Fields */
.wpcf7-form input[type="text"],
.wpcf7-form input[type="email"],
.wpcf7-form input[type="tel"],
.wpcf7-form input[type="url"],
.wpcf7-form input[type="date"],
.wpcf7-form input[type="number"],
.wpcf7-form select,
.wpcf7-form textarea {
    width: 100%;
    height: 55px;
    padding: 10px 20px;
    font-size: 16px;
    border: 2px solid #eee;
    border-radius: 8px;
    transition: all 0.3s ease;
    background-color: #f9f9f9;
    margin-bottom: 25px;
}

/* Textarea Height */
.wpcf7-form textarea {
    height: 150px;
    resize: none;
}

/* Focus State */
.wpcf7-form input[type="text"]:focus,
.wpcf7-form input[type="email"]:focus,
.wpcf7-form input[type="tel"]:focus,
.wpcf7-form input[type="url"]:focus,
.wpcf7-form input[type="date"]:focus,
.wpcf7-form input[type="number"]:focus,
.wpcf7-form select:focus,
.wpcf7-form textarea:focus {
    border-color: var(--gold-medium);
    box-shadow: 0 0 0 3px rgba(3, 53, 92, 0.1);
    background-color: #fff;
    outline: none;
}

/* Labels */
.wpcf7-form label {
    font-weight: 600;
    color: #555;
    margin-bottom: 8px;
    display: block;
}

/* Required Field Indicator */
.wpcf7-form .required {
    color: #e74c3c;
}

/* Submit Button */
.wpcf7-form input[type="submit"] {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    border: none;
    height: 55px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    padding: 0 35px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(3, 53, 92, 0.3);
}

.wpcf7-form input[type="submit"]:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(3, 53, 92, 0.4);
}

/* Response Messages */
.wpcf7-response-output {
    margin: 30px 0 0 !important;
    padding: 15px 20px !important;
    border-radius: 8px !important;
    font-size: 15px;
    text-align: center;
}

/* Success Message */
.wpcf7-mail-sent-ok {
    background-color: #d4edda !important;
    border: 1px solid #c3e6cb !important;
    color: #155724 !important;
}

/* Error Message */
.wpcf7-validation-errors,
.wpcf7-mail-sent-ng,
.wpcf7-spam-blocked {
    background-color: #f8d7da !important;
    border: 1px solid #f5c6cb !important;
    color: #721c24 !important;
}

/* Field Error Messages */
.wpcf7-not-valid-tip {
    color: #e74c3c !important;
    font-size: 14px !important;
    margin-top: -20px !important;
    margin-bottom: 15px !important;
    display: block;
}

/* Loading Spinner */
.wpcf7-spinner {
    margin: 0 auto !important;
    display: block !important;
}

/* Checkbox and Radio Buttons */
.wpcf7-checkbox,
.wpcf7-radio {
    display: block;
    margin-bottom: 15px;
}

.wpcf7-list-item {
    margin: 0 0 10px 0 !important;
    display: block;
}

.wpcf7-list-item input[type="checkbox"],
.wpcf7-list-item input[type="radio"] {
    margin-right: 8px;
}

/* Responsive Styles */
@media only screen and (max-width: 767px) {
    .wpcf7-form input[type="text"],
    .wpcf7-form input[type="email"],
    .wpcf7-form input[type="tel"],
    .wpcf7-form input[type="url"],
    .wpcf7-form input[type="date"],
    .wpcf7-form input[type="number"],
    .wpcf7-form select {
        height: 50px;
        font-size: 14px;
    }

    .wpcf7-form textarea {
        height: 120px;
    }

    .wpcf7-form input[type="submit"] {
        height: 50px;
        font-size: 14px;
    }
}

/* Trial Class Form Specific Styles */
.trial-form .wpcf7-form {
    margin-top: 20px;
}

.trial-form .wpcf7-form p {
    margin-bottom: 20px;
}

/* Contact Form Specific Styles */
.contact-form .wpcf7-form {
    margin-top: 20px;
}

.contact-form .wpcf7-form p {
    margin-bottom: 20px;
}
