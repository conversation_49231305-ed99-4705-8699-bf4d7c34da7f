/**
 * Simple Mobile Menu JavaScript for Zajel Arabic Theme
 * Basic functionality for mobile sidebar menu
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Simple mobile menu script loaded');
    
    // Get elements
    const menuToggle = document.getElementById('mobileMenuToggle');
    const menuClose = document.getElementById('mobileMenuClose');
    const sidebarMenu = document.getElementById('mobileSidebarMenu');
    const menuOverlay = document.getElementById('mobileMenuOverlay');
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    
    let isMenuOpen = false;

    // Debug: Check if elements exist
    console.log('Menu Toggle:', menuToggle);
    console.log('Menu Close:', menuClose);
    console.log('Sidebar Menu:', sidebarMenu);
    console.log('Menu Overlay:', menuOverlay);
    console.log('Dropdown Toggles:', dropdownToggles.length);
    
    // Menu toggle functionality
    if (menuToggle) {
        menuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Menu toggle clicked!');
            toggleMenu();
        });
    } else {
        console.error('Menu toggle button not found!');
    }

    // Menu close functionality
    if (menuClose) {
        menuClose.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Menu close clicked!');
            closeMenu();
        });
    }

    // Overlay click to close
    if (menuOverlay) {
        menuOverlay.addEventListener('click', function() {
            console.log('Overlay clicked!');
            closeMenu();
        });
    }

    // Escape key to close menu
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && isMenuOpen) {
            console.log('Escape key pressed!');
            closeMenu();
        }
    });

    // Close menu on window resize to desktop
    window.addEventListener('resize', function() {
        if (window.innerWidth > 991 && isMenuOpen) {
            console.log('Window resized to desktop, closing menu');
            closeMenu();
        }
    });

    // Dropdown functionality - now works on the whole link
    const dropdownLinks = document.querySelectorAll('.mobile-nav-item.has-dropdown .mobile-nav-link');
    dropdownLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Dropdown link clicked:', link);
            toggleDropdown(link);
        });
    });

    // Functions
    function toggleMenu() {
        console.log('Toggle menu called, current state:', isMenuOpen);
        if (isMenuOpen) {
            closeMenu();
        } else {
            openMenu();
        }
    }

    function openMenu() {
        console.log('Opening menu...');
        isMenuOpen = true;
        
        // Add active classes
        if (menuToggle) {
            menuToggle.classList.add('active');
            console.log('Added active class to menu toggle');
        }
        if (sidebarMenu) {
            sidebarMenu.classList.add('active');
            console.log('Added active class to sidebar menu');
        }
        if (menuOverlay) {
            menuOverlay.classList.add('active');
            console.log('Added active class to overlay');
        }
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        
        console.log('Menu opened successfully');
    }

    function closeMenu() {
        console.log('Closing menu...');
        isMenuOpen = false;
        
        // Remove active classes
        if (menuToggle) {
            menuToggle.classList.remove('active');
            console.log('Removed active class from menu toggle');
        }
        if (sidebarMenu) {
            sidebarMenu.classList.remove('active');
            console.log('Removed active class from sidebar menu');
        }
        if (menuOverlay) {
            menuOverlay.classList.remove('active');
            console.log('Removed active class from overlay');
        }
        
        // Restore body scroll
        document.body.style.overflow = '';
        
        // Close all dropdowns
        closeAllDropdowns();
        
        console.log('Menu closed successfully');
    }

    function toggleDropdown(link) {
        const parentItem = link.closest('.mobile-nav-item');
        const isActive = parentItem.classList.contains('active');

        console.log('Toggling dropdown, current state:', isActive);

        // Close all other dropdowns first
        closeAllDropdowns();

        // Toggle current dropdown
        if (!isActive) {
            parentItem.classList.add('active');
            console.log('Opened dropdown');

            // Set max height for animation
            const dropdown = parentItem.querySelector('.mobile-dropdown');
            if (dropdown) {
                dropdown.style.maxHeight = dropdown.scrollHeight + 'px';
                console.log('Set dropdown max height to:', dropdown.scrollHeight + 'px');
            }
        } else {
            console.log('Dropdown was already open, closing it');
        }
    }

    function closeAllDropdowns() {
        const activeDropdowns = document.querySelectorAll('.mobile-nav-item.has-dropdown.active');
        console.log('Closing', activeDropdowns.length, 'active dropdowns');
        
        activeDropdowns.forEach(function(item) {
            item.classList.remove('active');
            
            const dropdown = item.querySelector('.mobile-dropdown');
            if (dropdown) {
                dropdown.style.maxHeight = '0px';
            }
        });
    }

    // Highlight current page
    function highlightCurrentPage() {
        const currentPath = window.location.pathname;
        const menuLinks = document.querySelectorAll('.mobile-nav-link');
        
        console.log('Current path:', currentPath);
        console.log('Found', menuLinks.length, 'menu links');
        
        menuLinks.forEach(function(link) {
            try {
                const linkPath = new URL(link.href).pathname;
                if (linkPath === currentPath) {
                    link.style.color = '#1a5f8d';
                    link.style.backgroundColor = '#f8f9fa';
                    console.log('Highlighted current page link:', link.textContent);
                }
            } catch (e) {
                // Handle relative URLs or invalid URLs
                console.log('Could not process link:', link.href);
            }
        });
    }
    
    // Call highlight function
    highlightCurrentPage();
    
    // Make functions globally available for debugging
    window.mobileMenuDebug = {
        toggleMenu: toggleMenu,
        openMenu: openMenu,
        closeMenu: closeMenu,
        isMenuOpen: function() { return isMenuOpen; },
        elements: {
            menuToggle: menuToggle,
            menuClose: menuClose,
            sidebarMenu: sidebarMenu,
            menuOverlay: menuOverlay
        }
    };
    
    console.log('Mobile menu initialized successfully');
    console.log('Debug functions available at window.mobileMenuDebug');
});
