/**
 * Professional Blog JavaScript for Zajel Arabic Theme
 */

jQuery(document).ready(function($) {
    // Add smooth scrolling to all links
    $(".blog-hero a").on('click', function(event) {
        if (this.hash !== "") {
            event.preventDefault();
            var hash = this.hash;
            $('html, body').animate({
                scrollTop: $(hash).offset().top - 100
            }, 800);
        }
    });

    // Add animation to blog posts on scroll
    function animateOnScroll() {
        $('.archive-post').each(function() {
            var postPosition = $(this).offset().top;
            var topOfWindow = $(window).scrollTop();
            var windowHeight = $(window).height();

            if (postPosition < topOfWindow + windowHeight - 100) {
                $(this).addClass('animated');
            }
        });
    }

    // Run animation on page load
    animateOnScroll();

    // Run animation on scroll
    $(window).scroll(function() {
        animateOnScroll();
    });

    // Add hover effect to blog posts
    $('.archive-post').hover(
        function() {
            $(this).find('.post-thumbnail img').css('transform', 'scale(1.05)');
            $(this).find('.read-more a').css('background', 'linear-gradient(135deg, #1a5f8d, #03355c)');
        },
        function() {
            $(this).find('.post-thumbnail img').css('transform', 'scale(1)');
            $(this).find('.read-more a').css('background', 'linear-gradient(135deg, #03355c, #1a5f8d)');
        }
    );

    // Add active class to current page in sidebar
    var currentUrl = window.location.href;
    $('.professional-sidebar a').each(function() {
        if ($(this).attr('href') === currentUrl) {
            $(this).addClass('active');
        }
    });

    // Enhance search functionality
    $('.professional-sidebar .search-field').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        $(this).parent().removeClass('focused');
    });

    // Add sticky behavior to sidebar
    var sidebar = $('.professional-sidebar');
    var sidebarTop = sidebar.offset().top;
    var stickyOffset = 100;

    $(window).scroll(function() {
        if ($(window).width() > 991) {
            if ($(window).scrollTop() > sidebarTop - stickyOffset) {
                sidebar.addClass('sticky');
                sidebar.css('top', stickyOffset + 'px');
            } else {
                sidebar.removeClass('sticky');
                sidebar.css('top', '0');
            }
        }
    });

    // Add load more functionality for mobile
    var postsPerPage = 4;
    var visiblePosts = postsPerPage;
    var totalPosts = $('.archive-post').length;

    function showPosts() {
        $('.archive-post').hide();
        $('.archive-post').slice(0, visiblePosts).show();

        if (visiblePosts >= totalPosts) {
            $('#load-more').hide();
        } else {
            $('#load-more').show();
        }
    }

    // Only apply load more on mobile
    if ($(window).width() < 768 && totalPosts > postsPerPage) {
        showPosts();

        // Add load more button if it doesn't exist
        if ($('#load-more').length === 0) {
            $('.blog-posts').append('<div class="text-center mt-4"><button id="load-more" class="btn btn-primary">Load More</button></div>');
        }

        // Load more posts on button click
        $(document).on('click', '#load-more', function() {
            visiblePosts += postsPerPage;
            showPosts();
        });
    }

    // Reset on window resize
    $(window).resize(function() {
        if ($(window).width() >= 768) {
            $('.archive-post').show();
            $('#load-more').hide();
        } else if (totalPosts > postsPerPage) {
            visiblePosts = postsPerPage;
            showPosts();
        }
    });
});
