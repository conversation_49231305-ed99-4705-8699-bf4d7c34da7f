/*======================================
	Contact CSS
========================================*/
.contact-us {
    position: relative;
    background-color: $white;

    /* Contact Form */
    .form-main {
        margin-right: 50px;
        padding: 50px;
        box-shadow: 0px 0px 20px #00000014;
        border: 1px solid #eee;

        .title {
            font-size: 35px;
            font-weight: 700;
            margin-bottom: 30px;
            color: $black;

            span {
                font-size: 15px;
                font-weight: 500;
                color: $theme-color;
                display: block;
                margin-bottom: 5px;
            }
        }

        .form-group {
            margin-bottom: 22px;
            display: block;

            label {
                display: block;
                margin-bottom: .5rem;
                color: #081828;
                font-size: 13px;
                font-weight: 500;
            }

            input {
                width: 100%;
                min-height: 52px;
                padding: 3px 20px;
                color: $black;
                border: 1px solid #f5f5f5;
                border-radius: 0;
                outline: 0;
                background-color: #f5f5f5;
                border: 1px solid #eee;
            }

            textarea {
                width: 100%;
                min-height: 200px;
                padding: 20px;
                color: $black;
                border: 1px solid #f5f5f5;
                border-radius: 0;
                outline: 0;
                background-color: #f5f5f5;
                border: 1px solid #eee;
            }
        }

        .button .btn {
            height: 50px;
            border: none;
        }
    }

    /* Contact Info */
    .contact-info {
        .single-info {
            padding: 40px;
            box-shadow: 0px 0px 20px #00000014;
            margin-bottom: 35px;
            border: 1px solid #eee;
            position: relative;
            transition: all 0.4s ease-in-out;

            &::before {
                position: absolute;
                content: "";
                left: 0;
                bottom: 0;
                height: 4px;
                width: 0%;
                background: $theme-color;
                transition: all 0.4s ease-in-out;
            }

            &:hover::before {
                width: 100%;
            }

            &:last-child {
                margin: 0;
            }

            i {
                font-size: 35px;
                color: $theme-color;
                display: block;
                margin-bottom: 20px;
            }

            h4 {
                font-size: 20px;
                font-weight: 700;
                color: $black;
                margin-bottom: 10px;
            }

            p {
                color: #888;
                line-height: 24px;

                a {
                    color: #888;

                    &:hover {
                        color: $theme-color;
                    }
                }
            }
        }
    }

}


.map-section {
    background-color: $white;

    .map-container {
        box-shadow: 0 10px 30px rgba(111, 111, 111, 0.1);
        padding: 20px;
        border-radius: 7px;
    }
}

/*======================================
	End Contact CSS
========================================*/