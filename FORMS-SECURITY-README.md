# Zajel Forms Security System

## نظام الحماية والأمان للفورمز

تم تطوير نظام أمان قوي للحماية من الثغرات والهجمات الإلكترونية للفورمز في موقع زاجل.

## المميزات الأمنية

### 1. حماية من CSRF (Cross-Site Request Forgery)
- استخدام WordPress nonces للتحقق من صحة الطلبات
- كل فورم يتطلب nonce صالح

### 2. حماية من السبام (Spam Protection)
- Honeypot field مخفي للكشف عن البوتات
- Rate limiting (حد أقصى للإرسال كل 30 ثانية)

### 3. تنظيف البيانات (Data Sanitization)
- تنظيف وتأمين جميع البيانات المرسلة
- فلترة البيانات حسب النوع (email, text, textarea)

### 4. التحقق من صحة البيانات (Data Validation)
- التحقق من صحة عناوين البريد الإلكتروني
- التحقق من طول البيانات
- التحقق من الحقول المطلوبة

### 5. تسجيل معلومات إضافية للحماية
- تسجيل IP Address للمستخدم
- تسجيل User Agent للمتصفح
- تسجيل وقت الإرسال

## الجداول في قاعدة البيانات

### جدول Contact Messages (wp_contact)
```sql
- id: معرف فريد
- name: اسم المرسل
- subject: موضوع الرسالة
- email: البريد الإلكتروني
- phone: رقم الهاتف
- message: نص الرسالة
- ip_address: عنوان IP
- user_agent: معلومات المتصفح
- created_at: تاريخ الإرسال
```

### جدول Trial Bookings (wp_trial_bookings)
```sql
- id: معرف فريد
- full_name: الاسم الكامل
- email: البريد الإلكتروني
- phone: رقم الهاتف
- course: نوع الكورس
- additional_notes: ملاحظات إضافية
- ip_address: عنوان IP
- user_agent: معلومات المتصفح
- status: حالة الحجز (pending, confirmed, completed, cancelled)
- created_at: تاريخ الحجز
```

## صفحات الإدارة في Dashboard

### 1. Contact Messages
- عرض جميع الرسائل الواردة
- إمكانية عرض تفاصيل الرسالة
- إمكانية حذف الرسائل
- رابط: `/wp-admin/admin.php?page=zajel-contact-messages`

### 2. Trial Bookings
- عرض جميع حجوزات الكلاسات التجريبية
- إمكانية تحديث حالة الحجز
- إمكانية عرض تفاصيل الحجز
- إمكانية حذف الحجوزات
- رابط: `/wp-admin/admin.php?page=zajel-trial-bookings`

### 3. Dashboard Widget
- عرض إحصائيات سريعة في الداشبورد الرئيسي
- عدد الرسائل والحجوزات

## ميزات الحماية المتقدمة

### 1. عدم استخدام Contact Form 7
- تجنب الاعتماد على ملحقات خارجية
- تحكم كامل في الكود والأمان
- لا توجد ثغرات من ملحقات خارجية

### 2. عدم إرسال الإيميلات تلقائياً
- جميع البيانات تُحفظ في قاعدة البيانات
- عدم التعرض لثغرات إرسال الإيميل
- إمكانية مراجعة الرسائل من الداشبورد

### 3. Session Management
- إدارة الجلسات لمنع الإرسال المتكرر
- تقييد المعدل لمنع الإرسال السريع

## التثبيت والتفعيل

### 1. تلقائي عند تفعيل الثيم
- الجداول تُنشأ تلقائياً عند تفعيل الثيم
- القوائم تُضاف تلقائياً للداشبورد

### 2. يدوي (إذا لزم الأمر)
```php
// تشغيل دالة إنشاء الجداول
zajel_create_contact_and_trial_tables();
```

## الاستخدام

### 1. Contact Form
- الصفحة: `/contact`
- Template: `page-contact.php`
- CSS: `assets/css/contact.css`

### 2. Trial Class Form
- الصفحة: `/trial-class`
- Template: `page-trial-class.php`
- CSS: `assets/css/trial-class.css`

## معالجة الأخطاء

### رسائل الخطأ المختلفة:
- `security_error`: فشل التحقق الأمني
- `missing_fields`: حقول مطلوبة فارغة
- `invalid_email`: بريد إلكتروني غير صالح
- `field_too_long`: حقل يتجاوز الطول المسموح
- `invalid_course`: كورس غير صالح (للتريال)
- `database_error`: خطأ في قاعدة البيانات

### رسائل النجاح:
- `success`: تم الإرسال بنجاح

## الأمان والحماية

### ما تم تجنبه:
1. ✅ ثغرات SQL Injection
2. ✅ ثغرات XSS (Cross-Site Scripting)
3. ✅ ثغرات CSRF (Cross-Site Request Forgery)
4. ✅ ثغرات الـ Spam والبوتات
5. ✅ ثغرات إرسال الإيميل
6. ✅ ثغرات الملفات الخارجية (مثل mail.php)

### الحماية المطبقة:
1. ✅ WordPress nonces
2. ✅ Data sanitization
3. ✅ Data validation
4. ✅ Rate limiting
5. ✅ Honeypot protection
6. ✅ IP tracking
7. ✅ User agent tracking

## المطور
- تم التطوير بواسطة: محمد عبد العزيز
- التاريخ: 2024
- الغرض: حماية موقع زاجل من الثغرات الأمنية

## ملاحظات
- النظام لا يحتاج لـ Contact Form 7 أو أي ملحقات خارجية
- جميع البيانات محفوظة في قاعدة البيانات
- النظام مصمم ليكون آمن ومحدود الصلاحيات
- يمكن إضافة المزيد من الميزات لاحقاً حسب الحاجة