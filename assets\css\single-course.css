/* Single Course CSS for Zajel Arabic Theme */
:root {
    --blue-light: #1a5f8d;
    --blue-medium: #0c4a77;
    --blue-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --transition-slow: all 0.8s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 15px 60px rgba(0, 0, 0, 0.2);
}

/* Course Information Header */
.course-info-header {
    background: var(--blue-dark);
    padding: 20px;
    color: white;
    position: relative;
    overflow: hidden;
}

.course-info-header h3.sidebar-widget-title {
    color: white !important;
    margin: 0;
    padding: 0;
    font-size: 20px;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.course-info-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
    z-index: 0;
}

.sidebar-widget:hover .course-info-header::before {
    left: 100%;
    transition: 0.7s;
}
