<?php
/**
 * Template part for displaying single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Zajel_Arabic
 */
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('single-post-content'); ?>>
    <!-- Schema.org markup for better SEO -->
    <div class="schema-markup" itemscope itemtype="https://schema.org/BlogPosting">
        <meta itemprop="headline" content="<?php echo esc_attr(get_the_title()); ?>">
        <meta itemprop="description" content="<?php echo esc_attr(get_the_excerpt()); ?>">
        <meta itemprop="datePublished" content="<?php echo esc_attr(get_the_date('c')); ?>">
        <meta itemprop="dateModified" content="<?php echo esc_attr(get_the_modified_date('c')); ?>">
        <?php if (has_post_thumbnail()) : ?>
            <meta itemprop="image" content="<?php echo esc_url(get_the_post_thumbnail_url(null, 'full')); ?>">
        <?php endif; ?>
        <div itemprop="publisher" itemscope itemtype="https://schema.org/Organization">
            <meta itemprop="name" content="<?php echo esc_attr(get_bloginfo('name')); ?>">
            <div itemprop="logo" itemscope itemtype="https://schema.org/ImageObject">
                <meta itemprop="url" content="<?php echo esc_url(get_template_directory_uri() . '/assets/images/logo.png'); ?>">
            </div>
        </div>
        <div itemprop="author" itemscope itemtype="https://schema.org/Person">
            <meta itemprop="name" content="<?php echo esc_attr(get_the_author()); ?>">
        </div>
    </div>

    <div class="post-thumbnail wow fadeInUp" data-wow-delay=".2s">
        <?php if (has_post_thumbnail()) : ?>
            <?php the_post_thumbnail('full', array('alt' => get_the_title())); ?>
        <?php else : ?>
            <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/blog-placeholder.svg'); ?>" alt="<?php the_title_attribute(); ?>">
        <?php endif; ?>
    </div>

    <div class="post-meta wow fadeInUp" data-wow-delay=".3s">
        <div class="post-author">
            <i class="lni lni-user"></i>
            <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>">
                <?php the_author(); ?>
            </a>
        </div>
        <div class="post-date">
            <i class="lni lni-calendar"></i>
            <a href="<?php echo esc_url(get_day_link(get_post_time('Y'), get_post_time('m'), get_post_time('j'))); ?>">
                <?php echo get_the_date(); ?>
            </a>
        </div>
        <?php if (has_category()) : ?>
            <div class="post-category">
                <i class="lni lni-folder"></i>
                <?php the_category(', '); ?>
            </div>
        <?php endif; ?>
    </div>

    <div class="post-title wow fadeInUp" data-wow-delay=".3s">
        <h1><?php the_title(); ?></h1>
    </div>

    <div class="post-content wow fadeInUp" data-wow-delay=".4s">
        <?php the_content(); ?>

        <?php
        wp_link_pages(
            array(
                'before' => '<div class="page-links"><span class="page-links-title">' . esc_html__('Pages:', 'zajel') . '</span>',
                'after'  => '</div>',
                'link_before' => '<span>',
                'link_after'  => '</span>',
            )
        );
        ?>
    </div>

    <?php if (has_tag()) : ?>
        <div class="post-tags wow fadeInUp" data-wow-delay=".5s">
            <h4><?php esc_html_e('Tags:', 'zajel'); ?></h4>
            <?php the_tags('<div class="tags">', '', '</div>'); ?>
        </div>
    <?php endif; ?>

    <div class="post-navigation wow fadeInUp" data-wow-delay=".6s">
        <div class="nav-links">
            <?php
            $prev_post = get_previous_post();
            if (!empty($prev_post)) :
            ?>
                <div class="nav-previous">
                    <a href="<?php echo esc_url(get_permalink($prev_post->ID)); ?>">
                        <i class="lni lni-arrow-left"></i>
                        <span class="post-title"><?php echo esc_html(wp_trim_words($prev_post->post_title, 8, '...')); ?></span>
                    </a>
                </div>
            <?php endif; ?>

            <?php
            $next_post = get_next_post();
            if (!empty($next_post)) :
            ?>
                <div class="nav-next">
                    <a href="<?php echo esc_url(get_permalink($next_post->ID)); ?>">
                        <span class="post-title"><?php echo esc_html(wp_trim_words($next_post->post_title, 8, '...')); ?></span>
                        <i class="lni lni-arrow-right"></i>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Author Box -->
    <div class="author-box wow fadeInUp" data-wow-delay=".7s">
        <div class="author-avatar">
            <?php echo get_avatar(get_the_author_meta('ID'), 100); ?>
        </div>
        <div class="author-info">
            <h3 class="author-name"><?php the_author(); ?></h3>
            <p class="author-bio"><?php the_author_meta('description'); ?></p>
            <div class="author-social">
                <?php
                $author_url = get_the_author_meta('url');
                if (!empty($author_url)) :
                ?>
                    <a href="<?php echo esc_url($author_url); ?>" target="_blank" rel="nofollow"><i class="lni lni-world"></i></a>
                <?php endif; ?>

                <?php
                $author_facebook = get_the_author_meta('facebook');
                if (!empty($author_facebook)) :
                ?>
                    <a href="<?php echo esc_url($author_facebook); ?>" target="_blank" rel="nofollow"><i class="lni lni-facebook-filled"></i></a>
                <?php endif; ?>

                <?php
                $author_twitter = get_the_author_meta('twitter');
                if (!empty($author_twitter)) :
                ?>
                    <a href="<?php echo esc_url($author_twitter); ?>" target="_blank" rel="nofollow"><i class="lni lni-twitter-filled"></i></a>
                <?php endif; ?>

                <?php
                $author_linkedin = get_the_author_meta('linkedin');
                if (!empty($author_linkedin)) :
                ?>
                    <a href="<?php echo esc_url($author_linkedin); ?>" target="_blank" rel="nofollow"><i class="lni lni-linkedin-original"></i></a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Related Posts -->
    <?php
    $categories = get_the_category();
    if (!empty($categories)) :
        $category_ids = array();
        foreach ($categories as $category) {
            $category_ids[] = $category->term_id;
        }

        $related_args = array(
            'post_type' => 'post',
            'posts_per_page' => 4,
            'post__not_in' => array(get_the_ID()),
            'category__in' => $category_ids,
            'orderby' => 'rand'
        );

        $related_query = new WP_Query($related_args);

        if ($related_query->have_posts()) :
    ?>
        <div class="related-posts wow fadeInUp" data-wow-delay=".8s">
            <h3 class="related-posts-title"><?php esc_html_e('Related Posts', 'zajel'); ?></h3>
            <div class="related-posts-grid">
                <?php while ($related_query->have_posts()) : $related_query->the_post(); ?>
                    <div class="single-related-post">
                        <div class="image">
                            <a href="<?php the_permalink(); ?>">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('thumbnail', array('alt' => get_the_title())); ?>
                                <?php else : ?>
                                    <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/blog-placeholder.svg'); ?>" alt="<?php the_title_attribute(); ?>">
                                <?php endif; ?>
                            </a>
                        </div>
                        <div class="content">
                            <h4 class="title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                            <div class="date">
                                <i class="lni lni-calendar"></i>
                                <?php echo get_the_date(); ?>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        </div>
    <?php
        endif;
        wp_reset_postdata();
    endif;
    ?>


</article><!-- #post-<?php the_ID(); ?> -->
