<?xml version="1.0" encoding="UTF-8"?>
<svg width="400px" height="400px" viewBox="0 0 400 400" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Team Member Placeholder</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="goldGradient">
            <stop stop-color="#FFD700" offset="0%"></stop>
            <stop stop-color="#DAA520" offset="50%"></stop>
            <stop stop-color="#B8860B" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Background -->
        <rect fill="#F5F5F5" x="0" y="0" width="400" height="400"></rect>
        
        <!-- Person Silhouette -->
        <g transform="translate(100, 50)">
            <!-- Head -->
            <circle fill="#E0E0E0" cx="100" cy="80" r="70"></circle>
            
            <!-- Body -->
            <path d="M30,150 C30,250 170,250 170,150 L170,300 L30,300 Z" fill="#E0E0E0"></path>
            
            <!-- Face Features (minimal) -->
            <circle fill="#CCCCCC" cx="70" cy="70" r="10"></circle>
            <circle fill="#CCCCCC" cx="130" cy="70" r="10"></circle>
            <path d="M70,120 C70,140 130,140 130,120" stroke="#CCCCCC" stroke-width="5" fill="none"></path>
        </g>
        
        <!-- Decorative Elements -->
        <rect fill="url(#goldGradient)" x="0" y="0" width="400" height="10" opacity="0.7"></rect>
        <rect fill="url(#goldGradient)" x="0" y="390" width="400" height="10" opacity="0.7"></rect>
        <rect fill="url(#goldGradient)" x="0" y="0" width="10" height="400" opacity="0.7"></rect>
        <rect fill="url(#goldGradient)" x="390" y="0" width="10" height="400" opacity="0.7"></rect>
        
        <!-- Text -->
        <text font-family="Arial" font-size="24" font-weight="bold" fill="#B8860B" text-anchor="middle" x="200" y="350">Team Member</text>
    </g>
</svg>
