/* Teachers Page CSS for Zajel Arabic Theme */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --transition-slow: all 0.8s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 15px 60px rgba(0, 0, 0, 0.2);
}

/*======================================
    Teachers Hero Section
========================================*/
.teachers-hero {
    position: relative;
    padding: 120px 0;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/teachers-bg.svg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    text-align: center;
    color: #fff;
    overflow: hidden;
}

.teachers-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23B8860B' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 0.2;
    z-index: 0;
}

.teachers-hero .container {
    position: relative;
    z-index: 1;
}

.teachers-hero h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    text-transform: uppercase;
    position: relative;
    display: inline-block;
}

.teachers-hero h1::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    border-radius: 2px;
}

.teachers-hero p {
    font-size: 18px;
    max-width: 800px;
    margin: 30px auto 0;
    line-height: 1.8;
}

.teachers-hero .hero-btn {
    display: inline-block;
    margin-top: 30px;
    padding: 12px 30px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    border-radius: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
    z-index: 1;
    animation: pulse 2s infinite;
}

.teachers-hero .hero-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
    z-index: -1;
}

.teachers-hero .hero-btn:hover::before {
    left: 100%;
    transition: 0.7s;
}

.teachers-hero .hero-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(184, 134, 11, 0.3);
}

/*======================================
    Teachers Stats Section
========================================*/
.teachers-stats {
    padding: 80px 0;
    background-color: #f9f9f9;
    position: relative;
}

.teachers-stats .container {
    position: relative;
    z-index: 1;
}

.stats-item {
    text-align: center;
    padding: 30px 20px;
    background: #fff;
    border-radius: 10px;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.stats-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.stats-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    transition: var(--transition-medium);
    z-index: -1;
}

.stats-item:hover::before {
    height: 100%;
    opacity: 0.1;
}

.stats-icon {
    width: 70px;
    height: 70px;
    line-height: 70px;
    text-align: center;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    border-radius: 50%;
    margin: 0 auto 20px;
    color: #fff;
    font-size: 28px;
    position: relative;
    z-index: 1;
    transition: var(--transition-medium);
}

.stats-item:hover .stats-icon {
    transform: rotateY(360deg);
}

.stats-number {
    font-size: 36px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stats-text {
    font-size: 16px;
    color: #666;
    margin: 0;
}

/*======================================
    Teachers Grid Section
========================================*/
.teachers-grid {
    padding: 100px 0;
    position: relative;
    background-color: #fff;
}

/* Filter Buttons */
.teachers-filter {
    margin-bottom: 40px;
}

.teachers-filter .filter-btn {
    padding: 10px 20px;
    margin: 0 5px 10px;
    background: #fff;
    border: 2px solid rgba(26, 95, 141, 0.2);
    border-radius: 30px;
    color: #333;
    font-weight: 600;
    font-size: 14px;
    transition: var(--transition-fast);
    cursor: pointer;
    outline: none;
}

.teachers-filter .filter-btn i {
    margin-right: 5px;
    color: #1a5f8d;
    transition: var(--transition-fast);
}

/* Filtered Teachers Grid Layout */
.teachers-slider-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
    position: relative;
    min-height: 400px; /* Ensure there's space for the 'no teachers found' message */
}

.teachers-slider-row .col-lg-4 {
    transition: all 0.4s ease;
}

/* Teachers Slider Styles */
.teachers-slider-wrapper {
    position: relative;
    margin-bottom: 60px;
}

.teachers-slider-nav {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    z-index: 5;
    pointer-events: none;
}

.teachers-slider-prev,
.teachers-slider-next {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #1a5f8d, #03355c);
    color: #fff;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(26, 95, 141, 0.2);
    pointer-events: auto;
}

.teachers-slider-prev:hover,
.teachers-slider-next:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.3);
}

.teachers-slider-prev.disabled,
.teachers-slider-next.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.teachers-slider-pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 8px;
}

.teachers-slider-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(26, 95, 141, 0.3);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.teachers-slider-dot.active {
    width: 12px;
    height: 12px;
    background-color: #1a5f8d;
}

.teachers-slider-dot:hover {
    background-color: rgba(26, 95, 141, 0.7);
}

/* Ensure teacher cards are visible when filtered */
.teacher-card.filtered-visible {
    display: block !important;
}

/* No teachers message */
.no-teachers-message {
    width: 100%;
    padding: 30px;
    background-color: rgba(26, 95, 141, 0.05);
    border-radius: 10px;
    margin-top: 20px;
    margin-bottom: 20px;
    text-align: center;
    font-size: 16px;
    color: #1a5f8d;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 80%;
    z-index: 10;
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.1);
}

.teachers-filter .filter-btn:hover {
    background: linear-gradient(45deg, #1a5f8d, #03355c);
    border-color: transparent;
    color: #fff;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.2);
}

.teachers-filter .filter-btn:hover i {
    color: #fff;
}

.teachers-filter .filter-btn.active {
    background: linear-gradient(45deg, #1a5f8d, #03355c);
    border-color: transparent;
    color: #fff;
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.2);
}

.teachers-filter .filter-btn.active i {
    color: #fff;
}

.teachers-grid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23B8860B' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 0.5;
    z-index: 0;
}

.teachers-grid .container {
    position: relative;
    z-index: 1;
}

.section-title {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
}

.section-title span {
    display: inline-block;
    padding: 5px 15px;
    background-color: rgba(184, 134, 11, 0.1);
    color: var(--gold-dark);
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    text-transform: uppercase;
}

.section-title h2 {
    font-size: 36px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
    display: inline-block;
}

.section-title h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    border-radius: 2px;
}

.section-title p {
    max-width: 700px;
    margin: 0 auto;
    font-size: 16px;
    line-height: 1.8;
    color: #666;
}

.teacher-card {
    margin-bottom: 30px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
    position: relative;
    background: #fff;
}

.teacher-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.teacher-image {
    position: relative;
    overflow: hidden;
}

.teacher-image img {
    width: 100%;
    height: 220px; /* Reduced from 280px for a more professional look */
    object-fit: cover;
    transition: var(--transition-medium);
}

.teacher-card:hover .teacher-image img {
    transform: scale(1.1);
}

.teacher-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    opacity: 0;
    transition: var(--transition-medium);
}

.teacher-card:hover .teacher-overlay {
    opacity: 1;
}

.teacher-social {
    position: absolute;
    bottom: -50px;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 15px 0;
    transition: var(--transition-medium);
    z-index: 1;
}

.teacher-card:hover .teacher-social {
    bottom: 0;
}

.teacher-social a {
    width: 35px;
    height: 35px;
    background: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gold-dark);
    margin: 0 5px;
    transition: var(--transition-fast);
}

.teacher-social a:hover {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    transform: translateY(-3px);
}

.teacher-info {
    padding: 25px 20px;
    text-align: center;
    position: relative;
}

.teacher-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    border-radius: 2px;
}

.teacher-name {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 5px;
    color: #333;
    transition: var(--transition-fast);
}

.teacher-card:hover .teacher-name {
    color: var(--gold-dark);
}

.teacher-designation {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    display: block;
}

.teacher-description {
    font-size: 14px;
    color: #777;
    line-height: 1.6;
    margin-bottom: 15px;
}

/* Experience Badge */
.experience-badge {
    position: absolute;
    top: 15px;
    right: 15px; /* Changed from left to right */
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    width: 50px; /* Reduced from 60px */
    height: 50px; /* Reduced from 60px */
    border-radius: 50%;
    font-size: 11px; /* Reduced from 12px */
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.experience-badge span {
    font-size: 16px; /* Reduced from 18px */
    font-weight: bold;
    line-height: 1;
}

.experience-badge small {
    font-size: 9px; /* Reduced from 10px */
    text-transform: uppercase;
    margin-top: 2px;
}

.teacher-card:hover .experience-badge {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Teacher Courses */
.teacher-courses {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 15px;
    gap: 5px;
}

.course-tag {
    display: inline-block;
    padding: 5px 12px;
    background-color: rgba(184, 134, 11, 0.1);
    color: var(--gold-dark);
    border-radius: 15px;
    font-size: 11px;
    font-weight: 600;
    transition: var(--transition-fast);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 8px;
    cursor: pointer;
}

.course-tag:hover {
    background-color: var(--gold-dark);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

/* Specific course tags */
.course-tag.arabic {
    background-color: rgba(0, 128, 0, 0.1);
    color: #006400;
}

.course-tag.arabic:hover {
    background-color: #006400;
    color: #fff;
}

.course-tag.quran {
    background-color: rgba(0, 0, 139, 0.1);
    color: #00008B;
}

.course-tag.quran:hover {
    background-color: #00008B;
    color: #fff;
}

.course-tag.tajweed {
    background-color: rgba(139, 0, 0, 0.1);
    color: #8B0000;
}

.course-tag.tajweed:hover {
    background-color: #8B0000;
    color: #fff;
}

.course-tag.islamic-studies {
    background-color: rgba(72, 61, 139, 0.1);
    color: #483D8B;
}

.course-tag.islamic-studies:hover {
    background-color: #483D8B;
    color: #fff;
}

.course-tag.qeraat {
    background-color: rgba(139, 69, 19, 0.1);
    color: #8B4513;
}

.course-tag.qeraat:hover {
    background-color: #8B4513;
    color: #fff;
}

/* Make course tags more visible */
.course-tag {
    margin-right: 4px;
    margin-bottom: 6px;
    display: inline-block;
    transition: all 0.3s ease;
}

.course-tag:hover {
    transform: translateY(-2px);
}

.teacher-link {
    display: inline-block;
    padding: 8px 20px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.teacher-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
    z-index: -1;
}

.teacher-link:hover::before {
    left: 100%;
    transition: 0.7s;
}

.teacher-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(26, 95, 141, 0.3);
}

/*======================================
    Teachers Features Section
========================================*/
.teachers-features {
    padding: 100px 0;
    background-color: #f9f9f9;
    position: relative;
}

.teachers-features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23B8860B' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 0.5;
    z-index: 0;
}

.teachers-features .container {
    position: relative;
    z-index: 1;
}

.feature-box {
    padding: 30px;
    background: #fff;
    border-radius: 10px;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.feature-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(to bottom, var(--gold-dark), var(--gold-light));
    transition: var(--transition-medium);
    z-index: -1;
}

.feature-box:hover::before {
    width: 100%;
    opacity: 0.1;
}

.feature-box:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.feature-icon {
    width: 70px;
    height: 70px;
    line-height: 70px;
    text-align: center;
    background: #03355c;
    border-radius: 50%;
    margin-bottom: 20px;
    font-size: 28px;
    position: relative;
    z-index: 1;
    transition: var(--transition-medium);
}

.feature-icon i {
    color: #ffffff;
}

.feature-box:hover .feature-icon {
    transform: rotateY(360deg);
}

.feature-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
    transition: var(--transition-fast);
}

.feature-box:hover .feature-title {
    color: var(--gold-dark);
}

.feature-text {
    font-size: 15px;
    color: #666;
    line-height: 1.7;
    margin-bottom: 15px;
}

.feature-link {
    display: inline-block;
    color: var(--gold-dark);
    font-weight: 600;
    font-size: 14px;
    transition: var(--transition-fast);
}

.feature-link i {
    margin-left: 5px;
    transition: var(--transition-fast);
}

.feature-link:hover {
    color: var(--gold-light);
}

.feature-link:hover i {
    transform: translateX(5px);
}

/*======================================
    Teachers CTA Section
========================================*/
.teachers-cta {
    padding: 100px 0;
    background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('../images/cta-bg.svg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    text-align: center;
    color: #fff;
    position: relative;
}

.teachers-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23B8860B' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 0.2;
    z-index: 0;
}

.teachers-cta .container {
    position: relative;
    z-index: 1;
}

.cta-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.cta-text {
    font-size: 18px;
    max-width: 800px;
    margin: 0 auto 30px;
    line-height: 1.8;
}

.cta-btn {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    border-radius: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
    z-index: 1;
    animation: pulse 2s infinite;
}

.cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-fast);
    z-index: -1;
}

.cta-btn:hover::before {
    left: 100%;
    transition: 0.7s;
}

.cta-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(184, 134, 11, 0.3);
}

/*======================================
    Teachers Testimonials Section
========================================*/
.teachers-testimonials {
    padding: 100px 0;
    background-color: #fff;
    position: relative;
}

.teachers-testimonials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23B8860B' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
    opacity: 0.5;
    z-index: 0;
}

.teachers-testimonials .container {
    position: relative;
    z-index: 1;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(26, 95, 141, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(26, 95, 141, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(26, 95, 141, 0);
    }
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.fade-in {
    animation: fadeIn 1s ease forwards;
}

.fade-in-up {
    animation: fadeInUp 1s ease forwards;
}

.fade-in-down {
    animation: fadeInDown 1s ease forwards;
}

.fade-in-left {
    animation: fadeInLeft 1s ease forwards;
}

.fade-in-right {
    animation: fadeInRight 1s ease forwards;
}

.pulse {
    animation: pulse 2s infinite;
}

.rotate {
    animation: rotate 3s linear infinite;
}

.float {
    animation: float 6s ease-in-out infinite;
}

.delay-1 {
    animation-delay: 0.2s;
}

.delay-2 {
    animation-delay: 0.4s;
}

.delay-3 {
    animation-delay: 0.6s;
}

.delay-4 {
    animation-delay: 0.8s;
}

.delay-5 {
    animation-delay: 1s;
}

/* Section Title Animation */
.section-title span {
    animation: fadeInDown 0.5s ease forwards;
}

.section-title h2 {
    animation: fadeInUp 0.7s ease forwards;
}

.section-title p {
    animation: fadeIn 0.9s ease forwards;
}

.section-title h2::after {
    animation: width-grow 1.5s ease forwards;
}

@keyframes width-grow {
    from {
        width: 0;
    }
    to {
        width: 70px;
    }
}

/* Responsive Styles */
@media only screen and (max-width: 1199px) {
    .teachers-hero h1 {
        font-size: 42px;
    }

    .teachers-hero p {
        font-size: 16px;
    }

    .section-title h2 {
        font-size: 32px;
    }
}

@media only screen and (max-width: 991px) {
    .teachers-hero {
        padding: 100px 0;
    }

    .teachers-hero h1 {
        font-size: 36px;
    }

    .teachers-stats,
    .teachers-grid,
    .teachers-features,
    .teachers-cta,
    .teachers-testimonials {
        padding: 80px 0;
    }

    .section-title {
        margin-bottom: 50px;
    }

    .section-title h2 {
        font-size: 28px;
    }

    .stats-item {
        margin-bottom: 30px;
    }

    .feature-box {
        margin-bottom: 30px;
    }
}

@media only screen and (max-width: 767px) {
    .teachers-hero {
        padding: 80px 0;
    }

    .teachers-hero h1 {
        font-size: 32px;
    }

    .teachers-hero p {
        font-size: 15px;
    }

    .teachers-stats,
    .teachers-grid,
    .teachers-features,
    .teachers-cta,
    .teachers-testimonials {
        padding: 60px 0;
    }

    .section-title {
        margin-bottom: 40px;
    }

    .section-title h2 {
        font-size: 26px;
    }

    .section-title p {
        font-size: 15px;
    }

    .cta-title {
        font-size: 28px;
    }

    .cta-text {
        font-size: 16px;
    }
}

@media only screen and (max-width: 575px) {
    .teachers-hero {
        padding: 60px 0;
    }

    .teachers-hero h1 {
        font-size: 28px;
    }

    .teachers-hero p {
        font-size: 14px;
    }

    .teachers-stats,
    .teachers-grid,
    .teachers-features,
    .teachers-cta,
    .teachers-testimonials {
        padding: 50px 0;
    }

    .section-title h2 {
        font-size: 24px;
    }

    .section-title p {
        font-size: 14px;
    }

    .cta-title {
        font-size: 24px;
    }

    .cta-text {
        font-size: 15px;
    }

    .cta-btn {
        padding: 10px 25px;
        font-size: 14px;
    }
}
