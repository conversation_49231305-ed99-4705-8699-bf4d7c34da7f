/* Modern Navigation Styles */
.header.navbar-area {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 999;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }
  
  /* Logo Styling */
  .navbar-brand {
    display: flex;
    align-items: center;
    padding: 15px 0;
  }
  
  .navbar-brand img {
    max-height: 50px;
    width: auto;
  }
  
  .default-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
  }
  
  .default-logo i {
    color: #B8860B;
    margin-right: 10px;
    font-size: 1.8rem;
  }
  
  /* Main Navigation */
  .main-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0;
    position: relative;
  }
  
  .navbar-nav {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
  }
  
  .navbar-nav > li {
    position: relative;
    margin: 0 15px;
  }
  
  .navbar-nav > li > a {
    display: flex;
    align-items: center;
    padding: 25px 0;
    color: #333;
    font-size: 15px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
  }
  
  .navbar-nav > li > a:hover,
  .navbar-nav > li.current-menu-item > a {
    color: #B8860B;
  }
  
  /* Dropdown indicator */
  .navbar-nav > li.menu-item-has-children > a:after {
    content: "\f107";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    margin-left: 5px;
    font-size: 12px;
  }
  
  /* Submenu Styling */
  .navbar-nav > li > .sub-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 220px;
    background: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 10px 0;
    border-radius: 4px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 99;
    border-bottom: 3px solid #B8860B;
  }
  
  .navbar-nav > li:hover > .sub-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
  
  .navbar-nav > li > .sub-menu > li {
    display: block;
    margin: 0;
    padding: 0;
  }
  
  .navbar-nav > li > .sub-menu > li > a {
    display: block;
    padding: 8px 20px;
    font-size: 14px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
  }
  
  .navbar-nav > li > .sub-menu > li > a:hover,
  .navbar-nav > li > .sub-menu > li.current-menu-item > a {
    color: #B8860B;
    background-color: rgba(184, 134, 11, 0.05);
  }
  
  /* Social Icons */
  .header-social {
    display: flex;
    margin-left: 30px;
  }
  
  .header-social ul {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
  }
  
  .header-social ul li {
    margin-left: 15px;
  }
  
  .header-social ul li a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    color: #555;
    background-color: #f5f5f5;
    transition: all 0.3s ease;
  }
  
  .header-social ul li a:hover {
    color: #fff;
    background-color: #B8860B;
  }
  
  /* Mobile Menu Button */
  .mobile-menu-btn {
    display: none;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 10px;
  }
  
  .toggler-icon {
    display: block;
    width: 25px;
    height: 2px;
    background-color: #333;
    margin: 5px 0;
    transition: all 0.3s ease;
  }
  
  /* Mobile Menu Header */
  .mobile-menu-header {
    display: none;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
  }
  
  .mobile-logo {
    display: flex;
    align-items: center;
  }
  
  .mobile-logo img {
    max-height: 40px;
  }
  
  .close-mobile-menu {
    background: transparent;
    border: none;
    font-size: 20px;
    color: #333;
    cursor: pointer;
  }
  
  /* Mobile Menu Overlay */
  .mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }
  
  /* Responsive Styles */
  @media (max-width: 991px) {
    .mobile-menu-btn {
      display: block;
    }
    
    .mobile-menu-header {
      display: flex;
    }
    
    .navbar-collapse {
      position: fixed;
      top: 0;
      left: -280px;
      width: 280px;
      height: 100%;
      background-color: #fff;
      z-index: 999;
      padding: 0;
      overflow-y: auto;
      transition: all 0.3s ease;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    }
    
    .navbar-collapse.active {
      left: 0;
    }
    
    .mobile-menu-overlay.active {
      opacity: 1;
      visibility: visible;
    }
    
    .navbar-nav {
      flex-direction: column;
      padding: 20px;
    }
    
    .navbar-nav > li {
      margin: 0;
      padding: 0;
      border-bottom: 1px solid #eee;
    }
    
    .navbar-nav > li > a {
      padding: 12px 0;
    }
    
    .navbar-nav > li.menu-item-has-children > a:after {
      content: "\f105";
      float: right;
    }
    
    .navbar-nav > li.menu-item-has-children.active > a:after {
      content: "\f107";
    }
    
    .navbar-nav > li > .sub-menu {
      position: static;
      width: 100%;
      opacity: 1;
      visibility: visible;
      transform: none;
      box-shadow: none;
      padding: 0 0 0 15px;
      display: none;
      border-bottom: none;
      background-color: #f9f9f9;
      border-radius: 0;
    }
    
    .navbar-nav > li.active > .sub-menu {
      display: block;
    }
    
    .header-social {
      margin: 20px 0 0 0;
      justify-content: center;
    }
    
    .header-social ul {
      justify-content: center;
    }
    
    .header-social ul li {
      margin: 0 8px;
    }
  }