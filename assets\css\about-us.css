/* About Us Page Styles */

/* Global Typography Improvements */
body {
    font-family: 'Poppins', sans-serif;
    color: #333;
    line-height: 1.7;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    color: #222;
    line-height: 1.3;
    margin-bottom: 15px;
}

p {
    margin-bottom: 20px;
    color: #555;
    font-size: 16px;
    line-height: 1.8;
}

a {
    color: #0a4b6c;
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: #1a5f8d;
}

/* About Us Section */
.about-us-section, .about-us, section.about-us {
    position: relative;
    padding: 100px 0;
    overflow: hidden;
    background: linear-gradient(135deg, #fff 0%, #f9f9f9 100%);
}

.about-us::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="1" fill="%2303355c" opacity="0.1"/></svg>');
    background-size: 20px 20px;
    opacity: 0.5;
    z-index: 0;
}

.about-us .container {
    position: relative;
    z-index: 1;
}

.about-left, .about-us .about-left {
    padding-right: 30px;
}

@media only screen and (max-width: 991px) {
    .about-left, .about-us .about-left {
        padding-right: 0;
        text-align: center;
    }

    .about-title, .about-us .about-title, .about-title.align-left {
        text-align: center;
    }

    .about-title h2::after, .about-us .about-title h2::after, .about-title.align-left h2::after {
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 100px !important;
    }

    .about-title .icon-box, .about-us .about-title .icon-box, .about-title.align-left .icon-box {
        margin: 0 auto 20px;
    }

    .about-features {
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }
}

.about-title, .about-us .about-title, .about-title.align-left {
    position: relative;
}

.about-title .icon-box, .about-us .about-title .icon-box, .about-title.align-left .icon-box {
    width: 70px;
    height: 70px;
    background: rgba(3, 53, 92, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.about-title .icon-box i, .about-us .about-title .icon-box i, .about-title.align-left .icon-box i {
    font-size: 30px;
    color: #03355c;
    transition: all 0.3s ease;
}

.about-title:hover .icon-box, .about-us .about-title:hover .icon-box, .about-title.align-left:hover .icon-box {
    background: linear-gradient(45deg, #03355c, #1a5f8d);
}

.about-title:hover .icon-box i, .about-us .about-title:hover .icon-box i, .about-title.align-left:hover .icon-box i {
    color: #fff;
    transform: rotateY(180deg);
}

.about-title span, .about-us .about-title span, .about-title.align-left span {
    display: inline-block;
    padding: 5px 15px;
    background-color: rgba(3, 53, 92, 0.1);
    color: #03355c;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
}

.about-title h2, .about-us .about-title h2, .about-title.align-left h2 {
    font-size: 36px;
    font-weight: 700;
    color: #222;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
    letter-spacing: -0.5px;
    text-shadow: 0 1px 1px rgba(0,0,0,0.1);
}

.about-title h2::after, .about-us .about-title h2::after, .about-title.align-left h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 70px;
    height: 4px;
    background: linear-gradient(90deg, #1a5f8d, #0c4a77, #03355c);
    border-radius: 2px;
    box-shadow: 0 2px 4px rgba(3, 53, 92, 0.2);
}

.about-title p, .about-us .about-title p, .about-title.align-left p {
    font-size: 16px;
    line-height: 1.8;
    color: #555;
    margin-bottom: 25px;
    font-weight: 400;
    letter-spacing: 0.2px;
}

.about-title .qote, .about-us .about-title .qote, .about-title.align-left .qote {
    font-style: italic;
    font-size: 18px;
    color: #03355c;
    margin: 25px 0;
    padding-left: 20px;
    border-left: 3px solid #03355c;
}

.about-title .qote i, .about-us .about-title .qote i, .about-title.align-left .qote i {
    margin-right: 10px;
    font-size: 20px;
}

.about-title .button {
    margin-top: 30px;
}

.about-title .button .btn {
    background: linear-gradient(90deg, #1a5f8d, #0c4a77, #03355c);
    color: #fff;
    border: none;
    padding: 12px 25px;
    border-radius: 30px;
    font-weight: 600;
    margin-right: 15px;
    transition: all 0.3s ease;
}

.about-title .button .btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(3, 53, 92, 0.2);
}

.about-title .button .btn i {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.about-title .button .btn:hover i {
    transform: translateX(5px);
}

.about-title .button .video {
    background: #fff;
    color: #03355c;
    border: 2px solid #03355c;
}

.about-title .button .video:hover {
    background: rgba(3, 53, 92, 0.1);
}

.about-title .button .video i {
    margin-left: 5px;
}

/* Page Icon */
.page-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(90deg, #1a5f8d, #0c4a77, #03355c);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 10px 20px rgba(3, 53, 92, 0.2);
}

.page-icon i {
    font-size: 36px;
    color: #fff;
}

/* Animated Elements */
.animated-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, #1a5f8d, #03355c);
    opacity: 0.1;
    z-index: 0;
}

.animated-circle.circle-1 {
    width: 200px;
    height: 200px;
    top: -100px;
    right: -50px;
    animation: float 8s infinite ease-in-out;
}

.animated-circle.circle-2 {
    width: 150px;
    height: 150px;
    bottom: -50px;
    left: -50px;
    animation: float 6s infinite ease-in-out;
}

.animated-square {
    position: absolute;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #1a5f8d, #03355c);
    opacity: 0.1;
    transform: rotate(45deg);
    z-index: 0;
}

.animated-square.square-1 {
    bottom: 50px;
    right: 10%;
    animation: rotate 15s infinite linear;
}

.animated-square.square-2 {
    top: 20%;
    left: 5%;
    width: 70px;
    height: 70px;
    animation: rotate 12s infinite linear reverse;
}

.animated-circle.circle-3 {
    width: 50px;
    height: 50px;
    top: 30%;
    right: 15%;
    background: linear-gradient(45deg, #03355c, #1a5f8d);
    opacity: 0.15;
}

.about-us-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="1" fill="%2303355c" opacity="0.1"/></svg>');
    background-size: 20px 20px;
    opacity: 0.5;
    z-index: 0;
}

.about-us-section .container {
    position: relative;
    z-index: 1;
}

.about-image {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    background-color: #fff;
    padding: 15px;
    border: 1px solid rgba(10, 75, 108, 0.2);
    transition: all 0.5s ease;
}

.about-image:hover {
    box-shadow: 0 20px 50px rgba(10, 75, 108, 0.2);
    transform: translateY(-10px);
}

.about-image img {
    width: 100%;
    border-radius: 10px;
    transition: all 0.5s ease;
}

.about-image:hover img {
    transform: scale(1.05);
}

.about-content {
    padding: 20px 0 20px 30px;
    position: relative;
}

.about-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100px;
    background: linear-gradient(to bottom, #03355c, #1a5f8d);
    border-radius: 5px;
}

.about-content .section-title {
    margin-bottom: 30px;
}

.about-content .section-title span {
    display: inline-block;
    padding: 5px 15px;
    background-color: rgba(3, 53, 92, 0.1);
    color: #03355c;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
}

.about-content .section-title h2 {
    margin-bottom: 20px;
    font-size: 36px;
    font-weight: 700;
    color: #333;
    position: relative;
    padding-bottom: 15px;
}

.about-content .section-title h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 70px;
    height: 3px;
    background: linear-gradient(90deg, #1a5f8d, #0c4a77, #03355c);
}

.about-content .section-title p {
    font-size: 16px;
    line-height: 1.8;
    color: #666;
}

.about-text p {
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.7;
    color: #666;
    position: relative;
    padding-left: 20px;
}

.about-text p::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 0;
    width: 8px;
    height: 8px;
    background: #03355c;
    border-radius: 50%;
}

/* About Features */
.about-features {
    margin: 30px 0;
}

.feature-item {
    display: flex;
    margin-bottom: 25px;
    background: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-left: 4px solid #03355c;
}

.feature-item:hover {
    transform: translateX(10px);
    box-shadow: 0 10px 25px rgba(3, 53, 92, 0.15);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: rgba(3, 53, 92, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    background: linear-gradient(45deg, #03355c, #1a5f8d);
}

.feature-icon i {
    font-size: 28px;
    color: #03355c;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon i {
    color: #fff;
    transform: rotateY(180deg);
}

.feature-content h5 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #222;
    transition: all 0.3s ease;
    letter-spacing: 0.2px;
    text-shadow: 0 1px 1px rgba(0,0,0,0.05);
}

.feature-item:hover .feature-content h5 {
    color: #03355c;
}

.feature-content p {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
    margin-bottom: 0;
    font-weight: 400;
    letter-spacing: 0.1px;
}

/* About Image Wrapper */
.about-img-wrapper, .about-right {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    background-color: #fff;
    padding: 15px;
    border: 1px solid rgba(3, 53, 92, 0.2);
    transition: all 0.5s ease;
}

.about-img-wrapper:hover, .about-right:hover {
    box-shadow: 0 20px 50px rgba(3, 53, 92, 0.2);
    transform: translateY(-10px);
}

.about-img-wrapper img, .about-right img {
    width: 100%;
    border-radius: 10px;
    transition: all 0.5s ease;
    position: relative;
    z-index: 1;
}

.img-shape-1, .img-shape-2 {
    position: absolute;
    border-radius: 10px;
    background: linear-gradient(45deg, #03355c, #1a5f8d);
    opacity: 0.1;
    z-index: 0;
}

.img-shape-1 {
    width: 200px;
    height: 200px;
    bottom: -30px;
    right: -30px;
    animation: float 8s infinite ease-in-out;
}

.img-shape-2 {
    width: 150px;
    height: 150px;
    top: -20px;
    left: -20px;
    animation: float 6s infinite ease-in-out reverse;
}

.about-img-wrapper:hover img, .about-right:hover img {
    transform: scale(1.05);
}

/* Floating Elements */
.floating-element {
    position: absolute;
    background: #fff;
    border-radius: 10px;
    padding: 15px;
    display: flex;
    align-items: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    z-index: 2;
    border-left: 3px solid #03355c;
}

.floating-element.element-1 {
    bottom: 30px;
    left: -20px;
    animation: float 4s infinite ease-in-out;
}

.floating-element.element-2 {
    top: 30px;
    right: -20px;
    animation: float 5s infinite ease-in-out;
}

.floating-element.element-3 {
    top: 50%;
    right: 30px;
    animation: float 6s infinite ease-in-out;
}

.floating-element:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(3, 53, 92, 0.2);
}

.floating-element .icon-box {
    width: 40px;
    height: 40px;
    background: rgba(3, 53, 92, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.floating-element:hover .icon-box {
    background: linear-gradient(45deg, #03355c, #1a5f8d);
}

.floating-element .icon-box i {
    font-size: 20px;
    color: #03355c;
    transition: all 0.3s ease;
}

.floating-element:hover .icon-box i {
    color: #fff;
    transform: rotateY(180deg);
}

.floating-element h5 {
    font-size: 14px;
    font-weight: 700;
    margin: 0;
    color: #333;
    transition: all 0.3s ease;
}

.floating-element:hover h5 {
    color: #03355c;
}

/* Icon Box */
.icon-box {
    width: 70px;
    height: 70px;
    background: rgba(3, 53, 92, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.icon-box i {
    font-size: 30px;
    color: #03355c;
    transition: all 0.3s ease;
}

.about-title:hover .icon-box {
    background: linear-gradient(45deg, #03355c, #1a5f8d);
}

.about-title:hover .icon-box i {
    color: #fff;
    transform: rotateY(180deg);
}

/* Our Journey Section */
.our-journey {
    position: relative;
    padding: 100px 0;
    background-color: #f9f9f9;
    overflow: hidden;
    background: linear-gradient(135deg, #f9f9f9 0%, #f5f5f5 100%);
}

.our-journey::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><path d="M0,0 L20,0 L20,20 L0,20 Z" fill="%2303355c" opacity="0.03"/></svg>');
    background-size: 40px 40px;
    opacity: 0.5;
    z-index: 0;
}

.our-journey .container {
    position: relative;
    z-index: 1;
}

.our-journey .section-title {
    margin-bottom: 50px;
    text-align: center;
    position: relative;
}

.journey-decoration {
    position: relative;
    height: 10px;
    margin: 15px auto;
    width: 100px;
}

.journey-dot {
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #025c61;
}

.journey-dot.dot-1 {
    left: 0;
    animation-delay: 0.2s;
}

.journey-dot.dot-2 {
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0.4s;
}

.journey-dot.dot-3 {
    right: 0;
    animation-delay: 0.6s;
}

.our-journey .section-title span {
    display: inline-block;
    padding: 5px 15px;
    background-color: rgba(3, 53, 92, 0.1);
    color: #03355c;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
}

.our-journey .section-title h2 {
    font-size: 36px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
    display: inline-block;
}

.our-journey .section-title h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background: linear-gradient(90deg, #1a5f8d, #0c4a77, #03355c);
}

.our-journey .section-title p {
    max-width: 700px;
    margin: 0 auto;
    font-size: 16px;
    line-height: 1.8;
    color: #666;
}

/* Timeline */
.timeline-wrapper {
    position: relative;
    max-width: 1200px;
    margin: 50px auto;
}

.timeline-line {
    position: absolute;
    width: 6px;
    background: linear-gradient(to bottom, #1a5f8d, #03355c);
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -3px;
    border-radius: 3px;
    box-shadow: 0 0 10px rgba(3, 53, 92, 0.3);
    z-index: 1;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    width: 50%;
    box-sizing: border-box;
    margin-bottom: 30px;
    z-index: 2;
}

.timeline-left {
    left: 0;
    text-align: right;
}

.timeline-right {
    left: 50%;
    text-align: left;
}

.timeline-dot {
    position: absolute;
    width: 40px;
    height: 40px;
    background-color: #fff;
    border: 4px solid #03355c;
    top: 15px;
    border-radius: 50%;
    z-index: 3;
    box-shadow: 0 0 0 4px rgba(3, 53, 92, 0.2);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.timeline-left .timeline-dot {
    right: -20px;
}

.timeline-right .timeline-dot {
    left: -20px;
}

.timeline-dot i {
    font-size: 18px;
    color: #03355c;
    transition: all 0.3s ease;
}

.timeline-item:hover .timeline-dot {
    background: linear-gradient(45deg, #03355c, #1a5f8d);
    transform: scale(1.2);
    box-shadow: 0 0 0 6px rgba(3, 53, 92, 0.3);
}

.timeline-item:hover .timeline-dot i {
    color: #fff;
    transform: rotateY(180deg);
}



.timeline-content {
    padding: 25px 30px;
    background-color: white;
    position: relative;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(3, 53, 92, 0.1);
}

.timeline-content:hover {
    box-shadow: 0 10px 30px rgba(3, 53, 92, 0.2);
    transform: translateY(-5px);
    border-color: rgba(3, 53, 92, 0.3);
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(26, 95, 141, 0.05) 0%, rgba(3, 53, 92, 0.05) 100%);
    border-radius: 10px;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: -1;
}

.timeline-content:hover::before {
    opacity: 1;
}

.timeline-left .timeline-content {
    border-right: 5px solid #03355c;
}

.timeline-right .timeline-content {
    border-left: 5px solid #03355c;
}

.timeline-content::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}

.timeline-left .timeline-content::after {
    border-left: 10px solid #03355c;
    right: -15px;
    top: 20px;
}

.timeline-right .timeline-content::after {
    border-right: 10px solid #03355c;
    left: -15px;
    top: 20px;
}

.timeline-year {
    display: inline-block;
    padding: 5px 15px;
    background: linear-gradient(45deg, #03355c, #1a5f8d);
    color: white;
    border-radius: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    box-shadow: 0 3px 10px rgba(3, 53, 92, 0.2);
    transition: all 0.3s ease;
}

.timeline-content:hover .timeline-year {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(3, 53, 92, 0.3);
}

.timeline-content h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 22px;
    font-weight: 700;
    position: relative;
    padding-bottom: 10px;
    transition: all 0.3s ease;
}

.timeline-content h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    width: 40px;
    height: 2px;
    background: #03355c;
    transition: all 0.3s ease;
}

.timeline-left .timeline-content h4::after {
    right: 0;
    left: auto;
}

.timeline-right .timeline-content h4::after {
    left: 0;
    right: auto;
}

.timeline-content:hover h4 {
    color: #03355c;
}

.timeline-content:hover h4::after {
    width: 60px;
}

.timeline-content p {
    margin: 0;
    color: #666;
    font-size: 15px;
    line-height: 1.7;
    transition: all 0.3s ease;
}

/* Counter Area */
.counter-area {
    background: #fff;
    border-radius: 15px;
    padding: 40px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 2;
}

.single-counter {
    text-align: center;
    padding: 20px;
    transition: all 0.3s ease;
}

.single-counter:hover {
    transform: translateY(-10px);
}

.single-counter .icon {
    width: 70px;
    height: 70px;
    background: rgba(3, 53, 92, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.single-counter:hover .icon {
    background: linear-gradient(45deg, #03355c, #1a5f8d);
}

.single-counter .icon i {
    font-size: 30px;
    color: #03355c;
    transition: all 0.3s ease;
}

.single-counter:hover .icon i {
    color: #fff;
    transform: rotateY(180deg);
}

.single-counter h3 {
    font-size: 36px;
    font-weight: 700;
    color: #03355c;
    margin-bottom: 10px;
}

.single-counter p {
    font-size: 16px;
    color: #666;
    margin: 0;
}

/* Our Story Section */
.our-story-section {
    position: relative;
    padding: 100px 0;
    background-color: #f9f9f9;
    overflow: hidden;
    background: linear-gradient(135deg, #f9f9f9 0%, #f5f5f5 100%);
}

.our-story-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><path d="M0,0 L20,0 L20,20 L0,20 Z" fill="%2303355c" opacity="0.03"/></svg>');
    background-size: 40px 40px;
    opacity: 0.5;
    z-index: 0;
}

.our-story-section .container {
    position: relative;
    z-index: 1;
}

.our-story-section .section-title {
    margin-bottom: 50px;
    text-align: center;
}

.our-story-section .section-title span {
    display: inline-block;
    padding: 5px 15px;
    background-color: rgba(3, 53, 92, 0.1);
    color: #03355c;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
}

.our-story-section .section-title h2 {
    font-size: 36px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
    display: inline-block;
}

.our-story-section .section-title h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background: linear-gradient(90deg, #1a5f8d, #0c4a77, #03355c);
}

.our-story-section .section-title p {
    max-width: 700px;
    margin: 0 auto;
    font-size: 16px;
    line-height: 1.8;
    color: #666;
}

.timeline {
    position: relative;
    max-width: 1200px;
    margin: 50px auto;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 6px;
    background: linear-gradient(to bottom, #1a5f8d, #03355c);
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -3px;
    border-radius: 3px;
    box-shadow: 0 0 10px rgba(3, 53, 92, 0.3);
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    width: 50%;
    box-sizing: border-box;
    margin-bottom: 30px;
}

.timeline-item.left {
    left: 0;
}

.timeline-item.right {
    left: 50%;
}

.timeline-dot {
    position: absolute;
    width: 25px;
    height: 25px;
    right: -12.5px;
    background-color: #fff;
    border: 4px solid #03355c;
    top: 15px;
    border-radius: 50%;
    z-index: 1;
    box-shadow: 0 0 0 4px rgba(3, 53, 92, 0.2);
    transition: all 0.3s ease;
}

.timeline-item:hover .timeline-dot {
    background-color: #1a5f8d;
    transform: scale(1.2);
    box-shadow: 0 0 0 6px rgba(3, 53, 92, 0.3);
}

.timeline-item.right .timeline-dot {
    left: -12.5px;
}

.timeline-content {
    padding: 25px 30px;
    background-color: white;
    position: relative;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(3, 53, 92, 0.1);
}

.timeline-content:hover {
    box-shadow: 0 10px 30px rgba(3, 53, 92, 0.2);
    transform: translateY(-5px);
    border-color: rgba(3, 53, 92, 0.3);
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(26, 95, 141, 0.05) 0%, rgba(3, 53, 92, 0.05) 100%);
    border-radius: 10px;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: -1;
}

.timeline-content:hover::before {
    opacity: 1;
}

.timeline-item.left .timeline-content {
    border-right: 5px solid #03355c;
}

.timeline-item.right .timeline-content {
    border-left: 5px solid #03355c;
}

.timeline-content::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}

.timeline-item.left .timeline-content::after {
    border-left: 10px solid #03355c;
    right: -15px;
    top: 15px;
}

.timeline-item.right .timeline-content::after {
    border-right: 10px solid #03355c;
    left: -15px;
    top: 15px;
}

.timeline-content .year {
    display: inline-block;
    padding: 5px 15px;
    background: linear-gradient(45deg, #03355c, #1a5f8d);
    color: white;
    border-radius: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    box-shadow: 0 3px 10px rgba(3, 53, 92, 0.2);
    transition: all 0.3s ease;
}

.timeline-content:hover .year {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(3, 53, 92, 0.3);
}

.timeline-content h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 22px;
    font-weight: 700;
    position: relative;
    padding-bottom: 10px;
    transition: all 0.3s ease;
}

.timeline-content h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: #03355c;
    transition: all 0.3s ease;
}

.timeline-content:hover h4 {
    color: #03355c;
}

.timeline-content:hover h4::after {
    width: 60px;
}

.timeline-content p {
    margin: 0;
    color: #666;
    font-size: 15px;
    line-height: 1.7;
    transition: all 0.3s ease;
}

/* Team Section */
.team-section {
    position: relative;
    padding: 100px 0;
    overflow: hidden;
    background: linear-gradient(135deg, #fff 0%, #f9f9f9 100%);
}

.team-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg"><circle cx="15" cy="15" r="1" fill="%2303355c" opacity="0.1"/></svg>');
    background-size: 30px 30px;
    opacity: 0.5;
    z-index: 0;
}

.team-section .container {
    position: relative;
    z-index: 1;
}

.team-section .section-title {
    margin-bottom: 50px;
    text-align: center;
}

.team-section .section-title span {
    display: inline-block;
    padding: 5px 15px;
    background-color: rgba(3, 53, 92, 0.1);
    color: #03355c;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
}

.team-section .section-title h2 {
    font-size: 36px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
    display: inline-block;
}

.team-section .section-title h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background: linear-gradient(90deg, #1a5f8d, #0c4a77, #03355c);
}

.team-section .section-title p {
    max-width: 700px;
    margin: 0 auto;
    font-size: 16px;
    line-height: 1.8;
    color: #666;
}

.single-team {
    position: relative;
    margin-bottom: 30px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background: #fff;
    border: 1px solid rgba(3, 53, 92, 0.1);
}

.single-team::before {
    content: '';
    position: absolute;
    top: -100%;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(26, 95, 141, 0.2), rgba(3, 53, 92, 0.2));
    z-index: 1;
    transition: all 0.5s ease;
    opacity: 0;
}

.single-team:hover {
    box-shadow: 0 15px 30px rgba(3, 53, 92, 0.2);
    transform: translateY(-10px);
    border-color: rgba(3, 53, 92, 0.3);
}

.single-team:hover::before {
    top: 0;
    opacity: 1;
}

/* Teacher Image Wrapper */
.teacher-img-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
}

.teacher-img-wrapper img {
    width: 100%;
    height: auto;
    transition: all 0.5s ease;
}

.single-team:hover .teacher-img-wrapper img {
    transform: scale(1.1);
}

/* Experience Badge */
.experience-badge {
    position: absolute;
    bottom: 15px;
    right: 15px;
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #03355c, #1a5f8d);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    z-index: 2;
}

.experience-badge span {
    font-size: 20px;
    font-weight: 700;
    line-height: 1;
}

.experience-badge small {
    font-size: 12px;
    opacity: 0.8;
}

.single-team:hover .experience-badge {
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 10px 25px rgba(3, 53, 92, 0.3);
}

/* Teacher Icon */
.teacher-icon {
    width: 50px;
    height: 50px;
    background: rgba(3, 53, 92, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.teacher-icon i {
    font-size: 24px;
    color: #03355c;
    transition: all 0.3s ease;
}

.single-team:hover .teacher-icon {
    background: linear-gradient(45deg, #03355c, #1a5f8d);
}

.single-team:hover .teacher-icon i {
    color: #fff;
    transform: rotateY(180deg);
}

/* Teacher Skills */
.teacher-skills {
    margin: 15px 0;
}

.skill-tag {
    display: inline-block;
    padding: 5px 10px;
    background: rgba(3, 53, 92, 0.1);
    color: #03355c;
    border-radius: 20px;
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.skill-tag i {
    margin-right: 5px;
    font-size: 10px;
}

.single-team:hover .skill-tag {
    background: rgba(3, 53, 92, 0.2);
    transform: translateY(-3px);
}

/* Social Icons */
.social {
    margin-top: 20px;
    padding: 0;
    list-style: none;
    display: flex;
}

.social li {
    margin-right: 10px;
}

.social li a {
    width: 35px;
    height: 35px;
    background: rgba(3, 53, 92, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #03355c;
    transition: all 0.3s ease;
}

.social li a:hover {
    transform: translateY(-5px);
}

.social li a.facebook:hover {
    background: #3b5998;
    color: #fff;
}

.social li a.twitter:hover {
    background: #1da1f2;
    color: #fff;
}

.social li a.linkedin:hover {
    background: #0077b5;
    color: #fff;
}

.social li a.youtube:hover {
    background: #ff0000;
    color: #fff;
}

.team-image {
    position: relative;
    overflow: hidden;
}

.team-image img {
    width: 100%;
    height: auto;
    transition: all 0.5s ease;
}

.single-team:hover .team-image img {
    transform: scale(1.1);
}

.team-social {
    position: absolute;
    bottom: -60px;
    left: 0;
    width: 100%;
    background-color: rgba(3, 53, 92, 0.9);
    padding: 15px 0;
    transition: all 0.3s ease;
}

.single-team:hover .team-social {
    bottom: 0;
}

.team-social ul {
    display: flex;
    justify-content: center;
    padding: 0;
    margin: 0;
    list-style: none;
}

.team-social ul li {
    margin: 0 10px;
}

.team-social ul li a {
    color: white;
    font-size: 18px;
    transition: all 0.3s ease;
}

.team-social ul li a:hover {
    color: #fff;
    transform: scale(1.2);
}

.team-info {
    padding: 25px 20px;
    text-align: center;
    background-color: #fff;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.single-team:hover .team-info {
    background-color: rgba(255, 255, 255, 0.95);
}

.team-info h4 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 5px;
    color: #333;
    position: relative;
    display: inline-block;
    padding-bottom: 10px;
    transition: all 0.3s ease;
}

.team-info h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background: #03355c;
    transition: all 0.3s ease;
}

.single-team:hover .team-info h4 {
    color: #03355c;
}

.single-team:hover .team-info h4::after {
    width: 50px;
}

.team-info span {
    display: block;
    font-size: 14px;
    color: #03355c;
    margin-bottom: 15px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.single-team:hover .team-info span {
    letter-spacing: 1px;
}

.team-info p {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    margin: 0;
    transition: all 0.3s ease;
    max-height: 0;
    opacity: 0;
    overflow: hidden;
}

.single-team:hover .team-info p {
    max-height: 100px;
    opacity: 1;
    margin-top: 10px;
}

/* Testimonials Section */
.testimonials-section, .testimonials {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg, #f9f9f9 0%, #f5f5f5 100%);
    overflow: hidden;
}

.testimonials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M0,0 L50,0 L50,50 L0,50 Z" fill="%2303355c" opacity="0.02"/></svg>');
    background-size: 100px 100px;
    opacity: 0.5;
    z-index: 0;
}

.testimonials-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M0,0 L50,0 L50,50 L0,50 Z" fill="%2303355c" opacity="0.02"/></svg>');
    background-size: 100px 100px;
    opacity: 0.5;
    z-index: 0;
}

.testimonials-section .container {
    position: relative;
    z-index: 1;
}

.testimonials-section .section-title {
    margin-bottom: 50px;
    text-align: center;
}

.testimonials-section .section-title span {
    display: inline-block;
    padding: 5px 15px;
    background-color: rgba(3, 53, 92, 0.1);
    color: #03355c;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
}

.testimonials-section .section-title h2 {
    font-size: 36px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
    display: inline-block;
}

.testimonials-section .section-title h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background: linear-gradient(90deg, #1a5f8d, #0c4a77, #03355c);
}

.testimonials-section .section-title p {
    max-width: 700px;
    margin: 0 auto;
    font-size: 16px;
    line-height: 1.8;
    color: #666;
}

.testimonial-slider {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 15px;
}

.single-testimonial {
    background-color: #fff;
    border-radius: 15px;
    padding: 35px 30px;
    margin: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(3, 53, 92, 0.1);
    overflow: hidden;
}

.single-testimonial:hover {
    box-shadow: 0 15px 30px rgba(3, 53, 92, 0.2);
    transform: translateY(-5px);
    border-color: rgba(3, 53, 92, 0.3);
}

.single-testimonial::before {
    content: '\201C';
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 120px;
    color: rgba(3, 53, 92, 0.1);
    font-family: Georgia, serif;
    line-height: 1;
    transition: all 0.3s ease;
}

.single-testimonial:hover::before {
    color: rgba(3, 53, 92, 0.2);
    transform: scale(1.1);
}

.single-testimonial::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, #1a5f8d, #03355c);
    transform: scaleX(0);
    transform-origin: left;
    transition: all 0.5s ease;
}

.single-testimonial:hover::after {
    transform: scaleX(1);
}

.quote-icon {
    text-align: center;
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
}

.quote-icon i {
    font-size: 36px;
    color: #03355c;
    background: rgba(255, 255, 255, 0.8);
    width: 70px;
    height: 70px;
    line-height: 70px;
    border-radius: 50%;
    display: inline-block;
    box-shadow: 0 5px 15px rgba(3, 53, 92, 0.1);
    transition: all 0.3s ease;
}

.single-testimonial:hover .quote-icon i {
    transform: rotateY(180deg);
    color: #fff;
    background: linear-gradient(45deg, #03355c, #1a5f8d);
}

.single-testimonial .content {
    margin-bottom: 25px;
    font-style: italic;
    position: relative;
    z-index: 1;
}

.single-testimonial .content p {
    font-size: 16px;
    line-height: 1.8;
    color: #666;
    margin: 0;
    transition: all 0.3s ease;
}

.single-testimonial:hover .content p {
    color: #333;
}

.single-testimonial .info {
    text-align: center;
    position: relative;
    padding-top: 20px;
    z-index: 1;
}

.single-testimonial .info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background: #03355c;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.single-testimonial:hover .info::before {
    width: 80px;
    opacity: 1;
}

.single-testimonial .info h4 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 5px;
    color: #333;
    transition: all 0.3s ease;
}

.single-testimonial:hover .info h4 {
    color: #03355c;
}

.single-testimonial .info span {
    font-size: 14px;
    color: #03355c;
    font-weight: 600;
    display: inline-block;
    padding: 3px 10px;
    background: rgba(3, 53, 92, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.single-testimonial:hover .info span {
    background: rgba(3, 53, 92, 0.2);
    transform: translateY(-3px);
}

/* Author Image */
.author-image {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px;
    border: 3px solid rgba(3, 53, 92, 0.2);
    transition: all 0.3s ease;
}

.author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.single-testimonial:hover .author-image {
    border-color: #03355c;
    transform: scale(1.1);
}

.single-testimonial:hover .author-image img {
    transform: scale(1.1);
}

.quote-mark {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 25px;
    height: 25px;
    background: #03355c;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.quote-mark i {
    font-size: 12px;
    color: #fff;
}

.single-testimonial:hover .quote-mark {
    background: linear-gradient(45deg, #03355c, #1a5f8d);
    transform: scale(1.2);
}

/* Author Info */
.author-info {
    text-align: center;
}

.rating {
    margin-top: 10px;
}

.rating i {
    color: #1a5f8d;
    font-size: 14px;
    margin: 0 1px;
    transition: all 0.3s ease;
}

.single-testimonial:hover .rating i {
    transform: rotateY(360deg);
    color: #03355c;
}

/* Client Logo Section */
.client-logo-section {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg, #fff 0%, #f9f9f9 100%);
    overflow: hidden;
}

.client-logo-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg"><circle cx="15" cy="15" r="1" fill="%230a4b6c" opacity="0.1"/></svg>');
    background-size: 30px 30px;
    opacity: 0.5;
    z-index: 0;
}

.client-logo-section .container {
    position: relative;
    z-index: 1;
}

.client-logo-section .section-icon {
    width: 70px;
    height: 70px;
    background: rgba(10, 75, 108, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.client-decoration {
    position: relative;
    height: 10px;
    margin: 15px auto 30px;
    width: 100px;
    text-align: center;
}

.client-dot {
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #03355c;
}

.client-dot.dot-1 {
    left: 0;
    animation-delay: 0.2s;
}

.client-dot.dot-2 {
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0.4s;
}

.client-dot.dot-3 {
    right: 0;
    animation-delay: 0.6s;
}

.client-logo-section .section-icon i {
    font-size: 30px;
    color: #03355c;
    transition: all 0.3s ease;
}

.client-logo {
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(10, 75, 108, 0.1);
}

.client-logo img {
    max-width: 100%;
    max-height: 60px;
    transition: all 0.3s ease;
    filter: grayscale(100%);
    opacity: 0.7;
}

.client-logo:hover {
    box-shadow: 0 15px 30px rgba(10, 75, 108, 0.15);
    transform: translateY(-10px);
    border-color: rgba(10, 75, 108, 0.3);
}

.client-logo:hover img {
    filter: grayscale(0%);
    opacity: 1;
    transform: scale(1.1);
}

.client-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 75, 108, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.client-overlay i {
    font-size: 24px;
    color: #03355c;
    transform: scale(0);
    transition: all 0.3s ease;
}

.client-logo:hover .client-overlay {
    opacity: 1;
}

.client-logo:hover .client-overlay i {
    transform: scale(1);
}

.partner-cta {
    background: linear-gradient(45deg, #03355c, #1a5f8d);
    padding: 30px;
    border-radius: 15px;
    color: #fff;
    box-shadow: 0 10px 30px rgba(3, 53, 92, 0.2);
    transition: all 0.3s ease;
}

.partner-cta:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(3, 53, 92, 0.3);
}

.partner-cta h4 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #fff;
}

.partner-cta p {
    font-size: 16px;
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.9);
}

.partner-cta .btn {
    background: #fff;
    color: #03355c;
    border: none;
    padding: 10px 25px;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.partner-cta .btn:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateX(10px);
}

.partner-cta .btn i {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.partner-cta .btn:hover i {
    transform: translateX(5px);
}

/* Call to Action */
.call-action {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg, #03355c 0%, #1a5f8d 100%);
    overflow: hidden;
    color: #fff;
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
}

.call-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="1" fill="white" opacity="0.1"/></svg>');
    background-size: 20px 20px;
    opacity: 0.5;
}

.call-content {
    position: relative;
    z-index: 1;
}

.cta-note {
    display: inline-block;
    padding: 5px 15px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    margin-bottom: 15px;
    font-size: 14px;
}

.call-content h2 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #fff;
}

.call-content p {
    font-size: 18px;
    line-height: 1.7;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.9);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.call-content .btn {
    background-color: #fff;
    color: #03355c;
    border: none;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 30px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.call-content .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(3, 53, 92, 0.1);
    transition: all 0.5s ease;
    z-index: -1;
}

.call-content .btn:hover {
    background-color: #fff;
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    color: #03355c;
}

.call-content .btn:hover::before {
    left: 0;
}

.call-content .btn::after {
    content: '\2192';
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    opacity: 0;
    transition: all 0.3s ease;
}

.call-content .btn:hover::after {
    opacity: 1;
    right: 15px;
}

/* Animations */
@keyframes float {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes rotate {
    0% {
        transform: rotate(45deg);
    }
    100% {
        transform: rotate(405deg);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(10, 75, 108, 0.4);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(10, 75, 108, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(10, 75, 108, 0);
    }
}

.float {
    animation: float 4s ease-in-out infinite;
}

.pulse {
    animation: pulse 2s infinite;
}

.gold-gradient-text {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .about-us {
        padding: 60px 0;
    }

    .about-left {
        padding-right: 0;
        margin-bottom: 40px;
    }

    .about-title h2 {
        font-size: 28px;
    }

    .floating-element.element-1 {
        bottom: 10px;
        left: 10px;
    }

    .floating-element.element-2 {
        top: 10px;
        right: 10px;
    }

    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 15px;
    }

    .timeline-item:nth-child(even) {
        left: 0;
    }

    .timeline-line {
        left: 30px;
        margin-left: 0;
    }

    .timeline-dot {
        left: 30px;
        right: auto;
    }

    .timeline-item:nth-child(even) .timeline-dot {
        left: 30px;
    }

    .timeline-item:nth-child(odd) .timeline-content,
    .timeline-item:nth-child(even) .timeline-content {
        border-left: 5px solid var(--gold-dark);
        border-right: none;
    }

    .timeline-item:nth-child(odd) .timeline-content::after,
    .timeline-item:nth-child(even) .timeline-content::after {
        display: none;
    }

    .single-team .row {
        flex-direction: column;
    }

    .single-team .col-lg-5,
    .single-team .col-lg-7 {
        width: 100%;
    }

    .teacher-img-wrapper {
        margin-bottom: 30px;
    }

    .client-logo {
        height: 80px;
    }
}

@media only screen and (max-width: 991px) {
    .about-us-section,
    .our-story-section,
    .team-section,
    .testimonials-section,
    .call-action {
        padding: 80px 0;
    }

    .about-content {
        padding: 20px 0;
        margin-top: 30px;
    }

    .about-left, .about-us .about-left {
        margin-bottom: 30px;
    }

    .feature-item {
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .timeline-line {
        left: 31px;
        margin-left: 0;
    }

    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
        text-align: left !important;
    }

    .timeline-left,
    .timeline-right {
        left: 0;
    }

    .timeline-dot {
        left: 18px !important;
        right: auto !important;
    }

    .timeline-left .timeline-content,
    .timeline-right .timeline-content {
        border-left: 5px solid var(--gold-dark) !important;
        border-right: none !important;
        text-align: left !important;
    }

    .timeline-left .timeline-content::after,
    .timeline-right .timeline-content::after {
        left: -15px !important;
        right: auto !important;
        border-right: 10px solid var(--gold-dark) !important;
        border-left: none !important;
    }

    .timeline-left .timeline-content h4::after,
    .timeline-right .timeline-content h4::after {
        left: 0 !important;
        right: auto !important;
    }

    .call-content h2 {
        font-size: 30px;
    }
}

@media only screen and (max-width: 767px) {
    .about-us {
        padding: 50px 0;
    }

    .about-title h2 {
        font-size: 24px;
    }

    .about-title p {
        font-size: 14px;
    }

    .about-title .qote {
        font-size: 16px;
        padding-left: 15px;
        text-align: center;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
        border-left: none;
        border-bottom: 2px solid var(--gold-dark);
        padding-bottom: 10px;
    }

    .about-title .qote i {
        display: block;
        margin: 0 auto 10px;
        font-size: 24px;
        color: var(--gold-dark);
    }

    .about-title .button {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .about-title .button .btn {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100%;
        max-width: 250px;
        text-align: center;
    }

    .img-shape-1 {
        width: 120px;
        height: 120px;
        bottom: -15px;
        right: -15px;
    }

    .img-shape-2 {
        width: 100px;
        height: 100px;
        top: -10px;
        left: -10px;
    }

    .feature-item {
        padding: 15px;
        text-align: left;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        margin-right: 15px;
    }

    .feature-icon i {
        font-size: 22px;
    }

    .feature-content h5 {
        font-size: 16px;
    }

    .feature-content p {
        font-size: 13px;
    }

    .floating-element {
        width: 120px;
        padding: 10px;
    }

    .floating-element .icon-box {
        width: 40px;
        height: 40px;
        margin-right: 10px;
    }

    .floating-element .icon-box i {
        font-size: 18px;
    }

    .floating-element h5 {
        font-size: 14px;
    }

    .timeline-item {
        padding-left: 60px;
    }

    .timeline-dot {
        width: 30px;
        height: 30px;
        left: 15px;
    }

    .timeline-dot i {
        font-size: 14px;
    }

    .timeline-content {
        padding: 15px;
    }

    .timeline-content h4 {
        font-size: 18px;
    }

    .timeline-content p {
        font-size: 13px;
    }

    .single-counter {
        padding: 10px;
    }

    .single-counter .icon {
        width: 50px;
        height: 50px;
        margin-bottom: 10px;
    }

    .single-counter .icon i {
        font-size: 22px;
    }

    .single-counter h3 {
        font-size: 24px;
    }

    .single-counter p {
        font-size: 13px;
    }

    .single-testimonial {
        padding: 20px 15px;
    }

    .author-image {
        width: 60px;
        height: 60px;
    }

    .quote-mark {
        width: 20px;
        height: 20px;
    }

    .single-team .row {
        flex-direction: column;
    }

    .single-team .col-lg-5,
    .single-team .col-lg-7 {
        width: 100%;
    }

    .teacher-img-wrapper {
        margin-bottom: 20px;
    }

    .experience-badge {
        width: 50px;
        height: 50px;
    }

    .experience-badge span {
        font-size: 16px;
    }

    .experience-badge small {
        font-size: 10px;
    }

    .client-logo {
        height: 70px;
        padding: 10px;
    }

    .client-logo img {
        max-height: 50px;
    }

    .partner-cta {
        padding: 20px;
    }

    .partner-cta h4 {
        font-size: 20px;
    }

    .partner-cta p {
        font-size: 14px;
    }

    .about-us-section,
    .our-story-section,
    .team-section,
    .testimonials-section,
    .call-action {
        padding: 60px 0;
    }

    .about-image, .about-img-wrapper, .about-right {
        margin-bottom: 40px;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .about-content .section-title h2 {
        font-size: 28px;
    }

    .about-text p {
        font-size: 15px;
    }

    .timeline {
        margin: 30px auto;
    }

    .timeline-content {
        padding: 15px 20px;
    }

    .timeline-content h4 {
        font-size: 18px;
    }

    .timeline-content p {
        font-size: 14px;
    }

    .single-team {
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }

    .single-testimonial {
        padding: 20px;
    }

    .call-content h2 {
        font-size: 26px;
    }

    .call-content p {
        font-size: 16px;
    }
}

@media only screen and (max-width: 575px) {
    .about-title h2 {
        font-size: 22px;
    }

    .about-title h2::after, .about-us .about-title h2::after, .about-title.align-left h2::after {
        width: 80px !important;
    }

    .about-title span {
        font-size: 12px;
        padding: 4px 12px;
    }

    .about-title .icon-box {
        width: 60px;
        height: 60px;
    }

    .about-title .icon-box i {
        font-size: 24px;
    }

    .feature-item {
        padding: 12px;
        margin-bottom: 15px;
    }

    .feature-icon {
        width: 40px;
        height: 40px;
        margin-right: 10px;
    }

    .feature-icon i {
        font-size: 18px;
    }

    .feature-content h5 {
        font-size: 15px;
        margin-bottom: 5px;
    }

    .feature-content p {
        font-size: 12px;
        line-height: 1.5;
    }

    .about-title .qote {
        font-size: 14px;
        margin: 15px auto;
    }

    .about-title .qote i {
        font-size: 20px;
    }

    .about-title .button .btn {
        padding: 10px 20px;
        font-size: 14px;
    }
    .timeline-item {
        padding-left: 60px;
        padding-right: 15px;
    }

    .timeline-content {
        padding: 10px 15px;
    }

    .timeline-content .year {
        padding: 3px 10px;
        font-size: 12px;
    }

    .timeline-content h4 {
        font-size: 16px;
    }

    .timeline-content p {
        font-size: 13px;
    }

    .call-content h2 {
        font-size: 22px;
    }

    .call-content p {
        font-size: 14px;
    }

    .call-content .btn {
        padding: 12px 25px;
        font-size: 14px;
    }
}
/* Custom Color Variables */
:root {
    --gold-light: #FFD700;
    --gold-medium: #DAA520;
    --gold-dark: #B8860B;
}

