/* Simple Mobile Menu CSS for Zajel Arabic Theme */

/* Site Colors */
:root {
    --primary-blue: #1a5f8d;
    --primary-blue-light: #2980b9;
    --primary-blue-dark: #03355c;
    --white: #ffffff;
    --dark-gray: #343a40;
    --light-gray: #f8f9fa;
    --border-color: #e0e0e0;
}

/* Hide mobile menu on desktop */
@media (min-width: 992px) {
    .mobile-menu-btn,
    .mobile-menu-overlay,
    .mobile-sidebar-menu {
        display: none !important;
    }
}

/* Show mobile menu on mobile/tablet */
@media (max-width: 991px) {
    /* Hide desktop navigation */
    .navbar-collapse.sub-menu-bar {
        display: none !important;
    }
    
    /* Mobile Menu Button */
    .mobile-menu-btn {
        display: flex !important;
        background: transparent;
        border: none;
        padding: 8px;
        cursor: pointer;
        flex-direction: column;
        gap: 4px;
        z-index: 1001;
    }

    .mobile-menu-btn .toggler-icon {
        width: 25px;
        height: 3px;
        background-color: var(--dark-gray);
        transition: all 0.3s ease;
        border-radius: 2px;
        display: block;
    }

    .mobile-menu-btn.active .toggler-icon:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }

    .mobile-menu-btn.active .toggler-icon:nth-child(2) {
        opacity: 0;
    }

    .mobile-menu-btn.active .toggler-icon:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }

    /* Mobile Menu Overlay */
    .mobile-menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .mobile-menu-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* Mobile Sidebar Menu */
    .mobile-sidebar-menu {
        position: fixed;
        top: 0;
        left: -350px;
        width: 350px;
        height: 100vh;
        background: var(--white);
        z-index: 1000;
        transition: left 0.3s ease;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        overflow-y: auto;
    }

    .mobile-sidebar-menu.active {
        left: 0;
    }

    /* Mobile Menu Header */
    .mobile-menu-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px;
        background: var(--primary-blue);
        color: var(--white);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .mobile-logo {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 18px;
        font-weight: 700;
    }

    .mobile-logo i {
        font-size: 24px;
        color: var(--white);
    }

    .mobile-menu-close {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: var(--white);
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .mobile-menu-close:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: rotate(90deg);
    }



    /* Mobile Menu Content */
    .mobile-menu-content {
        padding: 10px 0;
    }

    .mobile-nav-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .mobile-nav-item {
        border-bottom: 1px solid var(--border-color);
    }

    .mobile-nav-item:last-child {
        border-bottom: none;
    }

    .mobile-nav-link {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        color: var(--dark-gray);
        text-decoration: none;
        font-size: 15px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .mobile-nav-link:hover {
        background: var(--light-gray);
        color: var(--primary-blue);
    }

    /* Style for dropdown parent items */
    .mobile-nav-item.has-dropdown .mobile-nav-link {
        position: relative;
    }

    .mobile-nav-item.has-dropdown .mobile-nav-link::after {
        content: "▼";
        position: absolute;
        right: 20px;
        font-size: 12px;
        color: var(--primary-blue);
        transition: transform 0.3s ease;
    }

    .mobile-nav-item.has-dropdown.active .mobile-nav-link::after {
        transform: rotate(180deg);
    }

    .mobile-nav-link i {
        width: 20px;
        margin-right: 15px;
        color: #6c757d;
        font-size: 16px;
    }

    .mobile-nav-link:hover i {
        color: var(--primary-blue);
    }

    /* Dropdown Functionality */
    .mobile-nav-item.has-dropdown .mobile-nav-link {
        justify-content: flex-start;
    }

    .dropdown-icon {
        display: none !important;
    }

    .mobile-dropdown {
        list-style: none;
        padding: 0;
        margin: 0;
        background: var(--light-gray);
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .mobile-nav-item.has-dropdown.active .mobile-dropdown {
        max-height: 300px;
    }

    .mobile-dropdown li {
        border-bottom: 1px solid var(--border-color);
    }

    .mobile-dropdown li:last-child {
        border-bottom: none;
    }

    .mobile-dropdown a {
        display: block;
        padding: 12px 20px 12px 55px;
        color: var(--dark-gray);
        text-decoration: none;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .mobile-dropdown a:hover {
        background: var(--white);
        color: var(--primary-blue);
        padding-left: 60px;
    }

    /* Free Trial Button */
    .mobile-trial-section {
        padding: 20px;
        text-align: center;
    }

    .mobile-trial-btn {
        display: inline-flex;
        align-items: center;
        gap: 10px;
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-light));
        color: var(--white);
        text-decoration: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(26, 95, 141, 0.2);
    }

    .mobile-trial-btn:hover {
        background: linear-gradient(135deg, var(--primary-blue-dark), var(--primary-blue));
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(26, 95, 141, 0.3);
        color: var(--white);
    }

    /* Social Media Section */
    .mobile-social-section {
        padding: 20px;
        border-top: 1px solid var(--border-color);
    }

    .mobile-social-section h4 {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 15px;
        font-weight: 500;
    }

    .mobile-social-links {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .social-link {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        color: var(--white);
        transition: all 0.3s ease;
        font-size: 16px;
    }

    .social-link.facebook { background: #1877f2; }
    .social-link.instagram { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888); }
    .social-link.twitter { background: #1da1f2; }
    .social-link.tiktok { background: #000000; }
    .social-link.snapchat { background: #fffc00; color: var(--dark-gray); }
    .social-link.youtube { background: #ff0000; }

    .social-link:hover {
        transform: translateY(-2px) scale(1.1);
    }
}

/* Smaller screens adjustments */
@media (max-width: 575px) {
    .mobile-sidebar-menu {
        width: 320px;
        left: -320px;
    }
    
    .mobile-menu-header {
        padding: 15px;
    }
    
    .mobile-logo {
        font-size: 16px;
    }
    
    .mobile-logo i {
        font-size: 20px;
    }
    
    .departments-btn {
        padding: 12px 15px;
        font-size: 15px;
    }
    
    .mobile-nav-link {
        padding: 12px 15px;
        font-size: 14px;
    }
    
    .mobile-trial-btn {
        padding: 12px 25px;
        font-size: 15px;
    }
}
