/*
 * Teachers Slider CSS for Zajel Arabic Theme
 * Styles for the responsive teachers slider with pagination
 */

/* Slider Wrapper */
.teachers-slider-wrapper {
    position: relative;
    margin-bottom: 50px;
    overflow: hidden;
}

/* Navigation Buttons */
.teachers-slider-nav {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    z-index: 10;
    pointer-events: none;
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
}

.teachers-slider-prev,
.teachers-slider-next {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1a5f8d, #03355c);
    color: #fff;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    pointer-events: auto;
    opacity: 0.9;
}

.teachers-slider-prev:hover,
.teachers-slider-next:hover {
    transform: scale(1.1);
    opacity: 1;
    background: linear-gradient(135deg, #03355c, #1a5f8d);
}

.teachers-slider-prev.disabled,
.teachers-slider-next.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.teachers-slider-prev i,
.teachers-slider-next i {
    font-size: 16px;
}

/* Pagination Dots */
.teachers-slider-pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 8px;
}

.teachers-slider-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(3, 53, 92, 0.3);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0;
}

.teachers-slider-dot.active {
    width: 12px;
    height: 12px;
    background-color: #1a5f8d;
    transform: scale(1.1);
}

.teachers-slider-dot:hover {
    background-color: rgba(3, 53, 92, 0.6);
}

/* Animation for slide transitions */
.teachers-grid .col-lg-4 {
    transition: opacity 0.4s ease, transform 0.4s ease;
}

/* Active teacher card styling */
.teacher-card.active {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-bottom: 3px solid #1a5f8d;
    position: relative;
    z-index: 2;
}

/* Add a subtle highlight effect to the active card */
.teacher-card.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid rgba(26, 95, 141, 0.3);
    border-radius: 10px;
    pointer-events: none;
}

/* Responsive Styles */
@media (max-width: 991px) {
    .teachers-slider-nav {
        top: 30%;
    }

    .teachers-slider-wrapper {
        min-height: 450px; /* Ensure consistent height */
    }
}

@media (max-width: 767px) {
    .teachers-slider-nav {
        top: 25%;
    }

    .teachers-slider-prev,
    .teachers-slider-next {
        width: 35px;
        height: 35px;
    }

    .teachers-slider-pagination {
        margin-top: 15px;
    }

    /* Improve mobile layout */
    .teachers-grid .col-lg-4 {
        float: none;
        margin: 0 auto;
        width: 100%;
        max-width: 350px;
    }

    /* Ensure proper spacing between teachers */
    .teacher-card {
        margin-bottom: 20px;
    }

    /* Improve touch area for better swiping */
    .teachers-slider-row {
        touch-action: pan-y;
        -webkit-overflow-scrolling: touch;
    }
}

/* Add swipe hint animation for mobile */
@media (max-width: 767px) {
    .teachers-slider-wrapper::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 50px;
        height: 50px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231a5f8d' width='24px' height='24px'%3E%3Cpath d='M0 0h24v24H0V0z' fill='none'/%3E%3Cpath d='M14 7l-5 5 5 5V7z'/%3E%3C/svg%3E"), url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231a5f8d' width='24px' height='24px'%3E%3Cpath d='M0 0h24v24H0V0z' fill='none'/%3E%3Cpath d='M10 17l5-5-5-5v10z'/%3E%3C/svg%3E");
        background-position: left center, right center;
        background-repeat: no-repeat;
        background-size: 24px, 24px;
        opacity: 0;
        animation: swipeHint 2s ease-in-out 1s;
        pointer-events: none;
    }

    @keyframes swipeHint {
        0%, 100% { opacity: 0; }
        50% { opacity: 0.5; }
    }
}
