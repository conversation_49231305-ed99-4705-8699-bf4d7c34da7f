<?php
/**
 * The template for displaying comments
 *
 * This is the template that displays the area of the page that contains both the current comments
 * and the comment form.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Zajel_Arabic
 */

/*
 * If the current post is protected by a password and
 * the visitor has not entered the password we will
 * return early without loading the comments.
 */
if (post_password_required()) {
    return;
}
?>

<div id="comments" class="comments-area">

    <?php
    // You can start editing here -- including this comment!
    if (have_comments()) :
        $zajel_comment_count = get_comments_number();
    ?>
        <h2 class="comments-title">
            <?php
            if ('1' === $zajel_comment_count) {
                echo esc_html__('1 Comment', 'zajel');
            } else {
                printf(
                    esc_html__('%s Comments', 'zajel'),
                    number_format_i18n($zajel_comment_count)
                );
            }
            ?>
        </h2><!-- .comments-title -->

        <?php the_comments_navigation(); ?>

        <ol class="comment-list">
            <?php
            wp_list_comments(
                array(
                    'style'      => 'ol',
                    'short_ping' => true,
                    'avatar_size' => 60,
                )
            );
            ?>
        </ol><!-- .comment-list -->

        <?php
        the_comments_navigation();

        // If comments are closed and there are comments, let's leave a little note, shall we?
        if (!comments_open()) :
        ?>
            <p class="no-comments"><?php esc_html_e('Comments are closed.', 'zajel'); ?></p>
        <?php
        endif;

    endif; // Check for have_comments().

    comment_form(
        array(
            'title_reply' => esc_html__('Leave a Comment', 'zajel'),
            'title_reply_before' => '<h3 id="reply-title" class="comment-reply-title">',
            'title_reply_after'  => '</h3>',
            'class_submit' => 'btn',
            'comment_notes_before' => '<p class="comment-notes">' . esc_html__('Your email address will not be published. Required fields are marked *', 'zajel') . '</p>',
            'comment_field' => '<div class="comment-form-comment"><label for="comment">' . esc_html__('Comment', 'zajel') . '</label><textarea id="comment" name="comment" cols="45" rows="8" required="required"></textarea></div>',
        )
    );
    ?>

</div><!-- #comments -->
