/* Simple Header CSS for Zajel Arabic Theme */
:root {
    --gold-light: #FFD700;
    --gold-medium: #DAA520;
    --gold-dark: #B8860B;
}

/* Header Styles */
.header.navbar-area {
    background-color: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 10px 0;
}

/* Logo Styles */
.navbar-brand img {
    max-height: 60px;
    width: auto;
}

.default-logo {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--gold-dark);
}

.default-logo i {
    color: var(--gold-dark);
    margin-right: 10px;
    font-size: 2rem;
}

/* Navigation Menu */
.navbar-nav {
    margin-right: auto;
}

.navbar-nav .nav-item {
    margin-right: 20px;
}

.navbar-nav .nav-item a {
    color: #333;
    font-weight: 600;
    padding: 10px 0;
    position: relative;
}

.navbar-nav .nav-item a:hover,
.navbar-nav .nav-item a.active {
    color: var(--gold-dark);
}

/* Social Icons */
.header-social {
    margin-right: 20px;
}

.header-social ul {
    display: flex;
    padding: 0;
    margin: 0;
    list-style: none;
}

.header-social ul li {
    margin-right: 10px;
}

.header-social ul li a {
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    display: block;
    border-radius: 50%;
    color: #333;
    background-color: #f5f5f5;
    font-size: 16px;
    transition: all 0.3s ease;
}

.header-social ul li a:hover {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
}

/* Trial Class Button */
.trial-class-btn {
    padding: 10px 20px;
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    border-radius: 30px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    display: inline-block;
}

.trial-class-btn:hover {
    color: #fff;
    opacity: 0.9;
}

/* Mobile Menu Button */
.mobile-menu-btn {
    padding: 0;
    border: none;
    background: transparent;
}

.mobile-menu-btn:focus {
    outline: none;
    box-shadow: none;
}

.mobile-menu-btn .toggler-icon {
    width: 30px;
    height: 2px;
    background-color: #333;
    display: block;
    margin: 6px 0;
    position: relative;
    transition: all 0.3s ease;
}

/* Mobile Menu Styles */
@media only screen and (max-width: 991px) {
    .navbar-collapse {
        background-color: #fff;
        padding: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        position: fixed;
        top: 0;
        left: -280px;
        width: 280px;
        height: 100vh;
        z-index: 999;
        transition: all 0.3s ease;
        overflow-y: auto;
    }

    .navbar-collapse.show {
        left: 0;
    }

    /* Mobile Menu Overlay */
    .mobile-menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        z-index: 998;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .mobile-menu-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .navbar-nav {
        margin-bottom: 20px;
    }

    .navbar-nav .nav-item {
        margin-right: 0;
        margin-bottom: 10px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding-bottom: 10px;
    }

    .navbar-nav .nav-item:last-child {
        border-bottom: none;
    }

    .mobile-social {
        margin-top: 20px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        padding-top: 20px;
    }

    .mobile-social ul {
        display: flex;
        justify-content: center;
        padding: 0;
        margin: 0;
        list-style: none;
    }

    .mobile-social ul li {
        margin: 0 10px;
    }

    .mobile-social ul li a {
        width: 35px;
        height: 35px;
        line-height: 35px;
        text-align: center;
        display: block;
        border-radius: 50%;
        color: #333;
        background-color: #f5f5f5;
        font-size: 16px;
        transition: all 0.3s ease;
    }

    .mobile-social ul li a:hover {
        background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
        color: #fff;
    }

    .mobile-trial {
        text-align: center;
        margin-top: 20px;
    }

    .mobile-trial .trial-class-btn {
        width: 100%;
        display: block;
    }
}
