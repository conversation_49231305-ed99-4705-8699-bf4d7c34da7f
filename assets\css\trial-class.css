/* Trial Class Page Styles */
:root {
    --gold-light: #1a5f8d;
    --gold-medium: #0c4a77;
    --gold-dark: #03355c;
    --transition-fast: all 0.3s ease;
    --transition-medium: all 0.5s ease;
    --transition-slow: all 0.8s ease;
    --shadow-small: 0 5px 15px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 15px 60px rgba(0, 0, 0, 0.2);
}

/* Hero Section */
.trial-hero {
    background: linear-gradient(135deg, rgba(26, 95, 141, 0.1), rgba(3, 53, 92, 0.2)), url('../images/trial-bg.jpg');
    background-size: cover;
    background-position: center;
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.trial-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(26, 95, 141, 0.7), rgba(3, 53, 92, 0.7));
    opacity: 0.85;
    z-index: 1;
}

.trial-hero .container {
    position: relative;
    z-index: 2;
}

.trial-hero h1 {
    color: #fff;
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: fadeInDown 1s ease;
}

.trial-hero p {
    color: #fff;
    font-size: 18px;
    max-width: 600px;
    margin-bottom: 30px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 1s ease;
}

/* Trial Form Section */
.trial-form-section {
    padding: 80px 0;
    background-color: #f9f9f9;
    position: relative;
}

.trial-form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/pattern.png');
    background-size: 300px;
    opacity: 0.05;
    z-index: 0;
}

.trial-form-container {
    position: relative;
    z-index: 1;
}

.trial-form-box {
    background: #fff;
    border-radius: 15px;
    box-shadow: var(--shadow-medium);
    padding: 40px;
    transition: var(--transition-medium);
    animation: fadeIn 1s ease;
    border: 1px solid rgba(3, 53, 92, 0.1);
    position: relative;
    overflow: hidden;
}

.trial-form-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, var(--gold-light), var(--gold-dark));
}

.trial-form-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-large);
}

.trial-form-box h3 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 30px;
    color: #333;
    position: relative;
    padding-bottom: 15px;
}

.trial-form-box h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    border-radius: 2px;
}

.trial-form-box p {
    margin-bottom: 25px;
    color: #666;
}

/* Form Styling */
.trial-form .form-group {
    margin-bottom: 25px;
    position: relative;
}

.trial-form .form-control {
    height: 55px;
    padding: 10px 20px;
    font-size: 16px;
    border: 2px solid #eee;
    border-radius: 8px;
    transition: var(--transition-fast);
    background-color: #f9f9f9;
}

.trial-form .form-control:focus {
    border-color: var(--gold-medium);
    box-shadow: 0 0 0 3px rgba(3, 53, 92, 0.1);
    background-color: #fff;
}

.trial-form textarea.form-control {
    height: 150px;
    resize: none;
}

.trial-form .form-control::placeholder {
    color: #999;
    font-size: 14px;
}

.trial-form .form-label {
    font-weight: 600;
    color: #555;
    margin-bottom: 8px;
    display: block;
}

.trial-form .btn-submit {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
    color: #fff;
    border: none;
    height: 55px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    padding: 0 35px;
    cursor: pointer;
    transition: var(--transition-fast);
    width: 100%;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(3, 53, 92, 0.3);
}

.trial-form .btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(3, 53, 92, 0.4);
}

/* Trial Info Section */
.trial-info {
    background: #fff;
    border-radius: 15px;
    box-shadow: var(--shadow-medium);
    padding: 40px;
    transition: var(--transition-medium);
    animation: fadeIn 1s ease;
    border: 1px solid rgba(3, 53, 92, 0.1);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.trial-info::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, var(--gold-light), var(--gold-dark));
}

.trial-info:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-large);
}

.trial-info h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #333;
    position: relative;
    padding-bottom: 15px;
}

.trial-info h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    border-radius: 2px;
}

.trial-info p {
    margin-bottom: 25px;
    color: #666;
    line-height: 1.7;
}

.trial-benefits {
    list-style: none;
    padding: 0;
    margin: 0 0 30px;
}

.trial-benefits li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-left: 10px;
    position: relative;
    transition: var(--transition-fast);
}

.trial-benefits li:hover {
    transform: translateX(5px);
}

.trial-benefits li i {
    color: var(--gold-dark);
    font-size: 20px;
    margin-right: 15px;
    flex-shrink: 0;
}

.trial-benefits li span {
    color: #555;
    font-size: 16px;
    line-height: 1.6;
}

.trial-note {
    background: rgba(3, 53, 92, 0.05);
    border-left: 4px solid var(--gold-dark);
    padding: 20px;
    border-radius: 8px;
    margin-top: 30px;
}

.trial-note h4 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.trial-note h4 i {
    color: var(--gold-dark);
    font-size: 20px;
    margin-right: 10px;
}

.trial-note p {
    margin-bottom: 0;
    color: #666;
    font-size: 15px;
    line-height: 1.7;
}

/* Features Section */
.trial-features {
    padding: 80px 0;
    background-color: #fff;
}

.feature-row {
    margin-left: -15px;
    margin-right: -15px;
}

.feature-column {
    padding: 15px;
    margin-bottom: 15px;
}

.feature-card {
    background: #fff;
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: var(--shadow-small);
    transition: var(--transition-medium);
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(135deg, rgba(26, 95, 141, 0.05), rgba(3, 53, 92, 0.1));
    transition: var(--transition-medium);
    z-index: -1;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.feature-card:hover::before {
    height: 100%;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, rgba(3, 53, 92, 0.1), rgba(26, 95, 141, 0.2));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    position: relative;
    transition: var(--transition-medium);
}

.feature-icon::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    border: 2px dashed rgba(3, 53, 92, 0.3);
    animation: spin 20s linear infinite;
    opacity: 0;
    transition: var(--transition-medium);
}

.feature-card:hover .feature-icon::after {
    opacity: 1;
}

.feature-icon i {
    font-size: 36px;
    color: var(--gold-dark);
    transition: var(--transition-medium);
}

.feature-card:hover .feature-icon {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
}

.feature-card:hover .feature-icon i {
    color: #fff;
    transform: rotateY(360deg);
}

.feature-title {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
    transition: var(--transition-fast);
}

.feature-card:hover .feature-title {
    color: var(--gold-dark);
}

.feature-description {
    color: #666;
    margin-bottom: 0;
    line-height: 1.7;
}

/* FAQ Section */
.trial-faq {
    padding: 80px 0;
    background-color: #f9f9f9;
    position: relative;
}

.trial-faq::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/pattern.png');
    background-size: 300px;
    opacity: 0.05;
    z-index: 0;
}

.faq-container {
    position: relative;
    z-index: 1;
}

.faq-title {
    text-align: center;
    margin-bottom: 50px;
}

.faq-title h2 {
    font-size: 36px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.faq-title h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--gold-dark), var(--gold-light));
    border-radius: 2px;
}

.faq-title p {
    max-width: 700px;
    margin: 0 auto;
    color: #666;
}

.faq-item {
    background: #fff;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-small);
    overflow: hidden;
    transition: var(--transition-fast);
}

.faq-item:hover {
    box-shadow: var(--shadow-medium);
}

.faq-question {
    padding: 20px 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    transition: var(--transition-fast);
    position: relative;
}

.faq-question::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 5px;
    background: linear-gradient(to bottom, var(--gold-light), var(--gold-dark));
    opacity: 0;
    transition: var(--transition-fast);
}

.faq-item.active .faq-question::before,
.faq-question:hover::before {
    opacity: 1;
}

.faq-question h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    transition: var(--transition-fast);
}

.faq-item.active .faq-question h3,
.faq-question:hover h3 {
    color: var(--gold-dark);
    padding-left: 10px;
}

.faq-toggle {
    width: 24px;
    height: 24px;
    background: rgba(3, 53, 92, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.faq-toggle i {
    color: var(--gold-dark);
    font-size: 12px;
    transition: var(--transition-fast);
}

.faq-item.active .faq-toggle {
    background: linear-gradient(45deg, var(--gold-dark), var(--gold-light));
}

.faq-item.active .faq-toggle i {
    color: #fff;
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 30px;
    max-height: 0;
    overflow: hidden;
    transition: var(--transition-medium);
}

.faq-item.active .faq-answer {
    padding: 0 30px 20px;
    max-height: 1000px;
}

.faq-answer p {
    color: #666;
    margin: 0;
    line-height: 1.7;
}

/* CTA Section */
.trial-cta {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--gold-dark), var(--gold-light));
    position: relative;
    overflow: hidden;
}

.trial-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/pattern-light.png');
    background-size: 300px;
    opacity: 0.1;
    z-index: 0;
}

.cta-container {
    position: relative;
    z-index: 1;
    text-align: center;
}

.cta-title {
    font-size: 36px;
    font-weight: 700;
    color: #fff;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cta-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    max-width: 700px;
    margin: 0 auto 30px;
}

.cta-btn {
    background: #fff;
    color: var(--gold-dark);
    border: none;
    height: 55px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    padding: 0 35px;
    cursor: pointer;
    transition: var(--transition-fast);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    background: #f9f9f9;
}

.cta-btn i {
    margin-right: 10px;
    font-size: 20px;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .trial-hero h1 {
        font-size: 36px;
    }

    .trial-hero p {
        font-size: 16px;
    }

    .trial-form-box {
        padding: 30px;
        margin-bottom: 30px;
    }

    .trial-form-box h3 {
        font-size: 24px;
    }

    .feature-column {
        padding: 12px;
        margin-bottom: 12px;
    }

    .faq-title h2 {
        font-size: 30px;
    }

    .cta-title {
        font-size: 30px;
    }

    .cta-description {
        font-size: 16px;
    }
}

@media only screen and (max-width: 767px) {
    .trial-hero {
        padding: 80px 0;
    }

    .trial-hero h1 {
        font-size: 30px;
    }

    .trial-form-section,
    .trial-features,
    .trial-faq,
    .trial-cta {
        padding: 60px 0;
    }

    .trial-form-box h3 {
        font-size: 22px;
    }

    .feature-column {
        padding: 10px;
        margin-bottom: 10px;
    }

    .feature-card {
        padding: 30px 20px;
    }

    .feature-icon {
        width: 70px;
        height: 70px;
    }

    .feature-icon i {
        font-size: 30px;
    }

    .feature-title {
        font-size: 20px;
    }

    .faq-question h3 {
        font-size: 16px;
    }

    .cta-title {
        font-size: 26px;
    }

    .cta-btn {
        height: 50px;
        font-size: 14px;
        padding: 0 25px;
    }
}

@media only screen and (max-width: 575px) {
    .trial-hero h1 {
        font-size: 26px;
    }

    .trial-hero p {
        font-size: 14px;
    }

    .trial-form-box {
        padding: 25px;
    }

    .trial-form-box h3 {
        font-size: 20px;
    }

    .trial-form .form-control {
        height: 50px;
        font-size: 14px;
    }

    .trial-form .btn-submit {
        height: 50px;
        font-size: 14px;
    }

    .feature-row {
        margin-left: -8px;
        margin-right: -8px;
    }

    .feature-column {
        padding: 8px;
        margin-bottom: 8px;
    }

    .feature-card {
        padding: 25px 15px;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 15px;
    }

    .feature-icon i {
        font-size: 24px;
    }

    .feature-title {
        font-size: 18px;
        margin-bottom: 10px;
    }

    .feature-description {
        font-size: 14px;
    }

    .faq-question {
        padding: 15px 20px;
    }

    .faq-question h3 {
        font-size: 15px;
    }

    .faq-answer {
        padding: 0 20px;
    }

    .faq-item.active .faq-answer {
        padding: 0 20px 15px;
    }

    .cta-title {
        font-size: 22px;
    }

    .cta-description {
        font-size: 14px;
    }
}

/* Alert Messages */
.alert {
    padding: 15px 20px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 8px;
    position: relative;
    animation: slideInDown 0.5s ease;
    font-size: 14px;
    line-height: 1.5;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-success::before {
    content: "✓";
    font-weight: bold;
    margin-right: 10px;
    color: #28a745;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-danger::before {
    content: "⚠";
    font-weight: bold;
    margin-right: 10px;
    color: #dc3545;
}

@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
