<?php
/**
 * The template for displaying all single posts
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package Zajel_Arabic
 */

get_header();
?>

<!-- Start Breadcrumbs -->
<div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content">
                    <div class="page-icon wow zoomIn" data-wow-delay=".2s">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <h1 class="page-title wow fadeInUp" data-wow-delay=".4s"><?php the_title(); ?></h1>
                </div>
                <ul class="breadcrumb-nav wow fadeInUp" data-wow-delay=".6s">
                    <li><a href="<?php echo esc_url(home_url('/')); ?>"><i class="fas fa-home"></i> Home</a></li>
                    <li><i class="fas fa-angle-right"></i></li>
                    <li><a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>">Blog</a></li>
                    <li><i class="fas fa-angle-right"></i></li>
                    <li><?php the_title(); ?></li>
                </ul>
            </div>
        </div>

        <!-- Animated Elements -->
        <div class="animated-circle circle-1 wow fadeIn" data-wow-delay=".3s"></div>
        <div class="animated-circle circle-2 wow fadeIn" data-wow-delay=".5s"></div>
        <div class="animated-square square-1 wow fadeIn" data-wow-delay=".7s"></div>
        <div class="animated-square square-2 wow fadeIn" data-wow-delay=".9s"></div>
    </div>
</div>
<!-- End Breadcrumbs -->

<!-- Start Blog Single Area -->
<section class="blog-single-area section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-md-12 col-12">
                <div class="blog-single-main">
                    <?php
                    while (have_posts()) :
                        the_post();

                        get_template_part('template-parts/content', 'single');

                        // If comments are open or we have at least one comment, load up the comment template.
                        if (comments_open() || get_comments_number()) :
                            comments_template();
                        endif;

                    endwhile; // End of the loop.
                    ?>
                </div>
            </div>

            <div class="col-lg-4 col-md-12 col-12">
                <div class="blog-sidebar">
                    <?php get_sidebar(); ?>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Blog Single Area -->

<!-- Start Related Posts -->
<section class="related-posts section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title">
                    <h2 class="wow fadeInUp" data-wow-delay=".4s">Related Posts</h2>
                    <p class="wow fadeInUp" data-wow-delay=".6s">Explore more articles related to this topic</p>
                </div>
            </div>
        </div>

        <div class="row">
            <?php
            // Get the current post's categories
            $categories = get_the_category();

            if ($categories) {
                $category_ids = array();
                foreach ($categories as $category) {
                    $category_ids[] = $category->term_id;
                }

                $args = array(
                    'category__in'        => $category_ids,
                    'post__not_in'        => array(get_the_ID()),
                    'posts_per_page'      => 3,
                    'ignore_sticky_posts' => 1
                );

                $related_query = new WP_Query($args);

                if ($related_query->have_posts()) :
                    $delay = 0.2;
                    while ($related_query->have_posts()) : $related_query->the_post();
            ?>
                        <div class="col-lg-4 col-md-6 col-12">
                            <div class="single-related-post wow fadeInUp" data-wow-delay="<?php echo esc_attr($delay); ?>s">
                                <div class="post-thumbnail">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', array('class' => 'img-fluid', 'alt' => get_the_title())); ?>
                                        <?php else : ?>
                                            <img src="https://via.placeholder.com/370x230" alt="<?php the_title(); ?>" class="img-fluid">
                                        <?php endif; ?>
                                    </a>
                                </div>
                                <div class="post-info">
                                    <span class="category"><?php the_category(', '); ?></span>
                                    <h4 class="title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                                    <span class="date"><i class="fas fa-calendar-alt"></i> <?php echo get_the_date('F j, Y'); ?></span>
                                </div>
                            </div>
                        </div>
            <?php
                        $delay += 0.2;
                    endwhile;
                    wp_reset_postdata();
                else :
            ?>
                    <div class="col-12">
                        <p class="no-related-posts">No related posts found.</p>
                    </div>
            <?php
                endif;
            }
            ?>
        </div>
    </div>
</section>
<!-- End Related Posts -->

<?php
get_footer();
