<?php
/*
هذا مثال توضيحي للثغرة - لا تستخدمه على موقع حقيقي!
This is an example of the vulnerability - DO NOT use on a real website!
*/

// هذا كان الكود الضار في assets/mail/mail.php
echo "=== مثال على الثغرة الأمنية ===\n\n";

echo "الكود الضار كان:\n";
echo '<?php' . "\n";
echo '$name = $_POST{\'name\'};' . "\n";
echo '$subject = $_POST{\'subject\'};' . "\n";
echo '$email = $_POST{\'email\'};' . "\n";
echo '$phone = $_POST{\'phone\'};' . "\n";
echo '$message = $_POST[\'message\'];' . "\n\n";

echo 'mail ("<EMAIL>" , "New Message", $email_message);' . "\n";
echo 'header("location: ../../mail-success.html");' . "\n";
echo '?>' . "\n\n";

echo "=== المشاكل الأمنية ===\n";
echo "1. مفيش أي validation أو sanitization\n";
echo "2. يقبل أي POST data من أي مصدر\n";
echo "3. مفيش CSRF protection\n";
echo "4. يمكن استخدامه لإرسال spam emails\n";
echo "5. يمكن حقن كود ضار في المتغيرات\n\n";

echo "=== كيفية الاستغلال ===\n";
echo "المهاجم يمكنه:\n";
echo "1. إرسال POST request مباشر للملف\n";
echo "2. حقن headers ضارة في الإيميل\n";
echo "3. استخدام الملف كـ spam relay\n";
echo "4. حقن كود PHP إذا كان في eval أو include\n\n";

echo "=== مثال على الهجوم ===\n";
echo "curl -X POST https://yoursite.com/wp-content/themes/zajola/assets/mail/mail.php \\\n";
echo "  -d 'name=Hacker' \\\n";
echo "  -d 'email=<EMAIL>' \\\n";
echo "  -d 'subject=Spam Subject' \\\n";
echo "  -d 'message=Malicious Content'\n\n";

echo "هذا الهجوم كان هيرسل إيميل spam لأي حد!\n";
?>
