<?php
/**
 * The sidebar containing the main widget area
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package Zajel_Arabic
 */
?>

<aside id="secondary" class="widget-area">


    <!-- Categories Widget -->
    <div class="widget categories-widget">
        <h4 class="widget-title">Categories</h4>
        <ul class="categories-list">
            <?php
            $categories = get_categories(array(
                'orderby' => 'name',
                'order'   => 'ASC'
            ));

            foreach ($categories as $category) :
                $category_link = get_category_link($category->term_id);
            ?>
                <li>
                    <a href="<?php echo esc_url($category_link); ?>">
                        <i class="fas fa-folder"></i> <?php echo esc_html($category->name); ?>
                        <span>(<?php echo esc_html($category->count); ?>)</span>
                    </a>
                </li>
            <?php endforeach; ?>
        </ul>
    </div>

    <!-- Recent Posts Widget -->
    <div class="widget recent-posts-widget">
        <h4 class="widget-title">Recent Posts</h4>
        <div class="recent-posts">
            <?php
            $recent_posts = wp_get_recent_posts(array(
                'numberposts' => 4,
                'post_status' => 'publish'
            ));

            foreach ($recent_posts as $post) :
                $post_id = $post['ID'];
            ?>
                <div class="single-post">
                    <div class="post-img">
                        <a href="<?php echo esc_url(get_permalink($post_id)); ?>">
                            <?php if (has_post_thumbnail($post_id)) : ?>
                                <?php echo get_the_post_thumbnail($post_id, 'thumbnail', array('alt' => get_the_title($post_id))); ?>
                            <?php else : ?>
                                <img src="https://via.placeholder.com/80x80" alt="<?php echo esc_attr(get_the_title($post_id)); ?>">
                            <?php endif; ?>
                        </a>
                    </div>
                    <div class="post-info">
                        <h5>
                            <a href="<?php echo esc_url(get_permalink($post_id)); ?>">
                                <?php echo esc_html(get_the_title($post_id)); ?>
                            </a>
                        </h5>
                        <span><i class="fas fa-calendar-alt"></i> <?php echo esc_html(get_the_date('F j, Y', $post_id)); ?></span>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Tags Widget -->
    <div class="widget tags-widget">
        <h4 class="widget-title">Popular Tags</h4>
        <div class="tags">
            <?php
            $tags = get_tags(array(
                'orderby' => 'count',
                'order'   => 'DESC',
                'number'  => 10
            ));

            if ($tags) :
                foreach ($tags as $tag) :
                    $tag_link = get_tag_link($tag->term_id);
            ?>
                    <a href="<?php echo esc_url($tag_link); ?>"><?php echo esc_html($tag->name); ?></a>
            <?php
                endforeach;
            endif;
            ?>
        </div>
    </div>

    <!-- Custom Widgets -->
    <?php if (is_active_sidebar('sidebar-1')) : ?>
        <?php dynamic_sidebar('sidebar-1'); ?>
    <?php endif; ?>
</aside><!-- #secondary -->