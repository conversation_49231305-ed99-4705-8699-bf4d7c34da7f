<?php
// Enqueue single-course CSS
wp_enqueue_style('zajel-single-course', get_template_directory_uri() . '/assets/css/single-course.css');
get_header();
if (have_posts()) : while (have_posts()) : the_post();

// Schema Markup for Course
$schema = array(
    '@context' => 'https://schema.org',
    '@type' => 'Course',
    'name' => get_the_title(),
    'description' => get_the_excerpt() ? get_the_excerpt() : wp_trim_words(get_the_content(), 30),
    'provider' => array(
        '@type' => 'Organization',
        'name' => 'Zajel Arabic Institute',
        'sameAs' => home_url('/'),
        'logo' => get_template_directory_uri() . '/assets/images/logo/logo.png'
    ),
    'url' => get_permalink(),
    'image' => has_post_thumbnail() ? get_the_post_thumbnail_url(get_the_ID(), 'full') : get_template_directory_uri() . '/assets/images/default-course.svg',
    'inLanguage' => 'en',
    'offers' => array(
        '@type' => 'Offer',
        'price' => 'Contact for pricing',
        'priceCurrency' => 'USD',
        'availability' => 'https://schema.org/InStock',
        'validFrom' => get_the_date('c')
    ),
    'courseCode' => 'ZAJEL-' . get_the_ID(),
    'timeRequired' => 'P8W',
    'educationalCredentialAwarded' => 'Certificate of Completion'
);

// Add instructor if available
$instructor_id = get_post_meta(get_the_ID(), 'course_instructor', true);
if (!$instructor_id) {
    $instructor_id = get_post_meta(get_the_ID(), 'course_teacher_id', true);
}

if ($instructor_id) {
    $instructor_bio = get_post_meta($instructor_id, 'teacher_summary', true);
    if (empty($instructor_bio)) {
        $instructor_bio = get_the_excerpt($instructor_id);
    }
    $instructor_designation = get_post_meta($instructor_id, 'teacher_designation', true) ?: 'Arabic Teacher';
    $experience = get_post_meta($instructor_id, 'teacher_experience', true);

    $schema['instructor'] = array(
        '@type' => 'Person',
        'name' => get_the_title($instructor_id),
        'url' => get_permalink($instructor_id),
        'description' => $instructor_bio,
        'jobTitle' => $instructor_designation,
        'image' => has_post_thumbnail($instructor_id) ? get_the_post_thumbnail_url($instructor_id, 'full') : get_template_directory_uri() . '/assets/images/default-avatar.svg'
    );
}
?>
<script type="application/ld+json"><?php echo wp_json_encode($schema); ?></script>

<!-- Start Breadcrumbs -->
<div class="breadcrumbs overlay">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-12">
                <div class="breadcrumbs-content text-center">
                    <h1 class="page-title"><?php the_title(); ?></h1>
                    <p><?php echo has_excerpt() ? get_the_excerpt() : 'Discover this comprehensive course by Zajel Arabic Institute designed to enhance your Arabic language skills with expert guidance.'; ?></p>
                </div>
                <ul class="breadcrumb-nav">
                    <li><a href="<?php echo esc_url(home_url('/')); ?>"><i class="lni lni-home"></i> Home</a></li>
                    <li><a href="<?php echo esc_url(home_url('/courses/')); ?>"><i class="lni lni-graduation"></i> Courses</a></li>
                    <li><i class="lni lni-book"></i> <?php the_title(); ?></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- End Breadcrumbs -->

<!-- Course Details Section Start -->
<div class="course-details section">
    <div class="container">
        <div class="row">
            <!-- Course Details Wrapper Start -->
            <div class="col-lg-8 col-12">
                <div class="course-featured-image">
                    <?php if (has_post_thumbnail()) : ?>
                        <?php the_post_thumbnail('large', ['class' => 'img-fluid rounded', 'alt' => get_the_title()]); ?>
                    <?php else : ?>
                        <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/images/default-course.svg" class="img-fluid rounded" alt="<?php the_title(); ?>">
                    <?php endif; ?>
                </div>

                <ul class="nav nav-tabs" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="overview-tab" data-bs-toggle="tab"
                            data-bs-target="#overview" type="button" role="tab" aria-controls="overview"
                            aria-selected="true"><i class="lni lni-book"></i> Overview</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="curriculum-tab" data-bs-toggle="tab"
                            data-bs-target="#curriculum" type="button" role="tab" aria-controls="curriculum"
                            aria-selected="false"><i class="lni lni-graduation"></i> Curriculum</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="instructor-tab" data-bs-toggle="tab"
                            data-bs-target="#instructor" type="button" role="tab" aria-controls="instructor"
                            aria-selected="false"><i class="lni lni-teacher"></i> Instructor</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews"
                            type="button" role="tab" aria-controls="reviews" aria-selected="false"><i class="lni lni-star-filled"></i> Reviews</button>
                    </li>
                </ul>
                <div class="tab-content" id="myTabContent">
                    <!-- Overview Tab -->
                    <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                        <div class="course-overview">
                            <h3 class="title">About This Course</h3>
                            <div class="course-content">
                                <?php the_content(); ?>
                            </div>

                            <?php
                            // Get course highlights/features if available (ACF field)
                            if (function_exists('get_field') && get_field('course_highlights')) : ?>
                            <div class="course-highlights">
                                <h4>Course Highlights</h4>
                                <ul class="highlights-list">
                                    <?php foreach (get_field('course_highlights') as $highlight) : ?>
                                        <li><i class="lni lni-checkmark"></i> <?php echo esc_html($highlight['highlight']); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <?php endif; ?>

                            <?php
                            // Display video if available
                            $video_url = get_post_meta(get_the_ID(), 'course_video_url', true);
                            if ($video_url) : ?>
                                <div class="overview-course-video">
                                    <h4>Course Preview</h4>
                                    <div class="video-container">
                                        <iframe title="<?php the_title(); ?> Video" src="<?php echo esc_url($video_url); ?>" frameborder="0" allowfullscreen></iframe>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="bottom-content">
                                <div class="row align-items-center">
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <div class="button">
                                            <a href="<?php echo esc_url(home_url('/contact/')); ?>" class="btn btn-primary"><i class="lni lni-graduation"></i> Enroll Now</a>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <ul class="share">
                                            <li><span>Share:</span></li>
                                            <li><a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" target="_blank" aria-label="Share on Facebook"><i class="lni lni-facebook-original"></i></a></li>
                                            <li><a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" target="_blank" aria-label="Share on Twitter"><i class="lni lni-twitter-original"></i></a></li>
                                            <li><a href="https://www.linkedin.com/shareArticle?mini=true&url=<?php echo urlencode(get_permalink()); ?>&title=<?php echo urlencode(get_the_title()); ?>" target="_blank" aria-label="Share on LinkedIn"><i class="lni lni-linkedin-original"></i></a></li>
                                            <li><a href="mailto:?subject=<?php echo urlencode(get_the_title()); ?>&body=Check out this course: <?php echo urlencode(get_permalink()); ?>" aria-label="Share via Email"><i class="lni lni-envelope"></i></a></li>
                                            <li><a href="https://api.whatsapp.com/send?text=<?php echo urlencode(get_the_title() . ': ' . get_permalink()); ?>" target="_blank" aria-label="Share on WhatsApp"><i class="lni lni-whatsapp"></i></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Curriculum Tab -->
                    <div class="tab-pane fade" id="curriculum" role="tabpanel" aria-labelledby="curriculum-tab">
                        <div class="course-curriculum">
                            <h3 class="title">Course Curriculum</h3>
                            <div class="curriculum-description">
                                <?php
                                // Get curriculum description from custom field
                                $curriculum_description = get_post_meta(get_the_ID(), 'course_curriculum_description', true);
                                if (!empty($curriculum_description)) {
                                    echo wpautop(wp_kses_post($curriculum_description));
                                } else {
                                    echo '<p>Explore the comprehensive curriculum designed to help you master this subject. Each section builds upon the previous one to ensure a solid understanding.</p>';
                                }
                                ?>
                            </div>

                            <ul class="curriculum-sections">
                                <?php
                                // Using ACF repeater field 'curriculum_sections'
                                if (function_exists('have_rows') && have_rows('curriculum_sections')) :
                                    $section_count = 0;
                                    while (have_rows('curriculum_sections')) : the_row();
                                        $section_count++;
                                        $section_title = get_sub_field('section_title');
                                        $section_desc = get_sub_field('section_description');
                                        ?>
                                        <li class="single-curriculum-section">
                                            <div class="section-header">
                                                <div class="section-left">
                                                    <span class="section-number"><?php echo esc_html($section_count); ?></span>
                                                    <h5 class="title"><?php echo esc_html($section_title); ?></h5>
                                                    <?php if ($section_desc) : ?>
                                                        <p class="section-desc"><?php echo esc_html($section_desc); ?></p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <ul class="section-content">
                                                <?php
                                                if (have_rows('lessons')) :
                                                    $lesson_count = 0;
                                                    while (have_rows('lessons')) : the_row();
                                                        $lesson_count++;
                                                        $lesson_name = get_sub_field('lesson_name');
                                                        $lesson_type = get_sub_field('lesson_type'); // e.g., 'video', 'quiz', 'meeting'
                                                        $duration = get_sub_field('duration');
                                                        $questions = get_sub_field('questions');
                                                        $locked = get_sub_field('locked') ? '<i class="lni lni-lock"></i>' : '';

                                                        // Set icon based on lesson type
                                                        $icon = '<i class="lni lni-text-format"></i>'; // Default icon
                                                        if ($lesson_type == 'video') {
                                                            $icon = '<i class="lni lni-video"></i>';
                                                        } elseif ($lesson_type == 'quiz') {
                                                            $icon = '<i class="lni lni-question-circle"></i>';
                                                        } elseif ($lesson_type == 'meeting') {
                                                            $icon = '<i class="lni lni-users"></i>';
                                                        }
                                                        ?>
                                                        <li class="course-item">
                                                            <a class="section-item-link <?php echo esc_attr($lesson_type); ?>" href="javascript:void(0)">
                                                                <span class="lesson-number"><?php echo esc_html($section_count . '.' . $lesson_count); ?></span>
                                                                <span class="item-name"><?php echo esc_html($lesson_name); ?></span>
                                                                <div class="course-item-meta">
                                                                    <?php if ($duration) : ?>
                                                                        <span class="item-meta duration"><i class="lni lni-timer"></i> <?php echo esc_html($duration); ?></span>
                                                                    <?php endif; ?>
                                                                    <?php if ($questions && $lesson_type == 'quiz') : ?>
                                                                        <span class="item-meta count-questions"><i class="lni lni-question-circle"></i> <?php echo esc_html($questions); ?> questions</span>
                                                                    <?php endif; ?>
                                                                    <span class="item-meta item-meta-icon">
                                                                        <?php echo $icon; ?>
                                                                        <?php echo $locked; ?>
                                                                    </span>
                                                                </div>
                                                            </a>
                                                        </li>
                                                    <?php endwhile; ?>
                                                <?php else : ?>
                                                    <li class="course-item empty-lessons">
                                                        <p>No lessons available in this section yet.</p>
                                                    </li>
                                                <?php endif; ?>
                                            </ul>
                                        </li>
                                    <?php endwhile; ?>
                                <?php else :
                                    // Check if curriculum description exists
                                    $curriculum_description = get_post_meta(get_the_ID(), 'course_curriculum_description', true);
                                    if (empty($curriculum_description)) :
                                    ?>
                                    <div class="no-curriculum-message">
                                        <i class="lni lni-book"></i>
                                        <p>No curriculum available for this course yet. Please check back later.</p>
                                    </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </ul>

                            <div class="bottom-content">
                                <div class="row align-items-center">
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <div class="button">
                                            <a href="<?php echo esc_url(home_url('/contact/')); ?>" class="btn btn-primary"><i class="lni lni-graduation"></i> Enroll Now</a>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <ul class="share">
                                            <li><span>Share:</span></li>
                                            <li><a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" target="_blank" aria-label="Share on Facebook"><i class="lni lni-facebook-original"></i></a></li>
                                            <li><a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" target="_blank" aria-label="Share on Twitter"><i class="lni lni-twitter-original"></i></a></li>
                                            <li><a href="https://www.linkedin.com/shareArticle?mini=true&url=<?php echo urlencode(get_permalink()); ?>&title=<?php echo urlencode(get_the_title()); ?>" target="_blank" aria-label="Share on LinkedIn"><i class="lni lni-linkedin-original"></i></a></li>
                                            <li><a href="mailto:?subject=<?php echo urlencode(get_the_title()); ?>&body=Check out this course: <?php echo urlencode(get_permalink()); ?>" aria-label="Share via Email"><i class="lni lni-envelope"></i></a></li>
                                            <li><a href="https://api.whatsapp.com/send?text=<?php echo urlencode(get_the_title() . ': ' . get_permalink()); ?>" target="_blank" aria-label="Share on WhatsApp"><i class="lni lni-whatsapp"></i></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Instructor Tab -->
                    <div class="tab-pane fade" id="instructor" role="tabpanel" aria-labelledby="instructor-tab">
                        <div class="course-instructor">
                            <h3 class="title">Meet Your Instructor</h3>

                            <?php
                            // Get instructor ID - check both course_instructor and course_teacher_id fields
                            $instructor_id = get_post_meta(get_the_ID(), 'course_instructor', true);
                            if (!$instructor_id) {
                                $instructor_id = get_post_meta(get_the_ID(), 'course_teacher_id', true);
                            }

                            if ($instructor_id) :
                                $instructor = get_post($instructor_id);
                                $instructor_title = get_the_title($instructor_id);
                                $instructor_bio = get_post_meta($instructor_id, 'teacher_summary', true);
                                if (empty($instructor_bio)) {
                                    $instructor_bio = get_the_excerpt($instructor_id);
                                }
                                $instructor_designation = get_post_meta($instructor_id, 'teacher_designation', true) ?: 'Arabic Teacher';
                                $experience = get_post_meta($instructor_id, 'teacher_experience', true);
                                $courses = get_post_meta($instructor_id, 'teacher_courses', true);
                                ?>
                                <div class="instructor-profile">
                                    <div class="instructor-profile-inner">
                                        <div class="profile-image-col">
                                            <div class="profile-image">
                                                <?php if (has_post_thumbnail($instructor_id)) : ?>
                                                    <?php echo get_the_post_thumbnail($instructor_id, 'medium', ['class' => 'img-fluid rounded', 'alt' => esc_attr($instructor_title)]); ?>
                                                <?php else : ?>
                                                    <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/images/default-teacher.png" class="img-fluid rounded" alt="<?php echo esc_attr($instructor_title); ?>">
                                                <?php endif; ?>

                                                <?php if ($experience) : ?>
                                                <div class="experience-badge">
                                                    <span><?php echo esc_html($experience); ?></span>
                                                    <small>years</small>
                                                </div>
                                                <?php endif; ?>
                                            </div>

                                            <?php
                                            // Check if ACF is active and social links field exists
                                            if (function_exists('get_field') && get_field('social_links', $instructor_id)) :
                                                $social_links = get_field('social_links', $instructor_id);
                                            ?>
                                            <ul class="instructor-social-networks">
                                                <?php foreach ($social_links as $social) :
                                                    $icon = $social['platform'] === 'facebook' ? 'lni-facebook-original' :
                                                            ($social['platform'] === 'twitter' ? 'lni-twitter-original' :
                                                            ($social['platform'] === 'linkedin' ? 'lni-linkedin-original' : 'lni-instagram'));
                                                ?>
                                                <li class="item"><a href="<?php echo esc_url($social['url']); ?>" target="_blank" class="social-link"><i class="lni <?php echo esc_attr($icon); ?>"></i></a></li>
                                                <?php endforeach; ?>
                                            </ul>
                                            <?php else : // Default social links if ACF field doesn't exist ?>
                                            <ul class="instructor-social-networks">
                                                <li class="item"><a href="#" class="social-link"><i class="lni lni-facebook-original"></i></a></li>
                                                <li class="item"><a href="#" class="social-link"><i class="lni lni-twitter-original"></i></a></li>
                                                <li class="item"><a href="#" class="social-link"><i class="lni lni-linkedin-original"></i></a></li>
                                            </ul>
                                            <?php endif; ?>
                                        </div>

                                        <div class="profile-info-col">
                                            <h4 class="instructor-name"><a href="<?php echo esc_url(get_permalink($instructor_id)); ?>"><?php echo esc_html($instructor_title); ?></a></h4>
                                            <p class="instructor-designation"><i class="lni lni-graduation"></i> <?php echo esc_html($instructor_designation); ?></p>

                                            <?php if ($instructor_bio) : ?>
                                            <div class="instructor-bio">
                                                <?php echo wpautop(esc_html($instructor_bio)); ?>
                                            </div>
                                            <?php endif; ?>

                                            <?php if ($courses) : ?>
                                            <div class="instructor-expertise">
                                                <h5><i class="lni lni-certificate"></i> Expertise</h5>
                                                <div class="expertise-tags">
                                                    <?php
                                                    $course_tags = explode(',', $courses);
                                                    foreach ($course_tags as $tag) :
                                                        $tag = trim($tag);
                                                        if (!empty($tag)) :
                                                    ?>
                                                        <span class="expertise-tag"><i class="lni lni-checkmark"></i> <?php echo esc_html($tag); ?></span>
                                                    <?php
                                                        endif;
                                                    endforeach;
                                                    ?>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <div class="instructor-cta">
                                                <a href="<?php echo esc_url(get_permalink($instructor_id)); ?>" class="btn btn-primary"><i class="lni lni-user"></i> View Full Profile</a>
                                                <a href="<?php echo esc_url(home_url('/trial-class/')); ?>" class="btn btn-outline"><i class="lni lni-calendar"></i> Book a Class</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php else : ?>
                                <div class="no-instructor-message">
                                    <i class="lni lni-user"></i>
                                    <p>No instructor has been assigned to this course yet. Our qualified teachers will guide you through this course material.</p>
                                </div>
                            <?php endif; ?>

                            <div class="bottom-content">
                                <div class="row align-items-center">
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <div class="button">
                                            <a href="<?php echo esc_url(home_url('/contact/')); ?>" class="btn btn-primary"><i class="lni lni-graduation"></i> Enroll Now</a>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <ul class="share">
                                            <li><span>Share:</span></li>
                                            <li><a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" target="_blank" aria-label="Share on Facebook"><i class="lni lni-facebook-original"></i></a></li>
                                            <li><a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" target="_blank" aria-label="Share on Twitter"><i class="lni lni-twitter-original"></i></a></li>
                                            <li><a href="https://www.linkedin.com/shareArticle?mini=true&url=<?php echo urlencode(get_permalink()); ?>&title=<?php echo urlencode(get_the_title()); ?>" target="_blank" aria-label="Share on LinkedIn"><i class="lni lni-linkedin-original"></i></a></li>
                                            <li><a href="mailto:?subject=<?php echo urlencode(get_the_title()); ?>&body=Check out this course: <?php echo urlencode(get_permalink()); ?>" aria-label="Share via Email"><i class="lni lni-envelope"></i></a></li>
                                            <li><a href="https://api.whatsapp.com/send?text=<?php echo urlencode(get_the_title() . ': ' . get_permalink()); ?>" target="_blank" aria-label="Share on WhatsApp"><i class="lni lni-whatsapp"></i></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Reviews Tab -->
                    <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                        <div class="course-reviews">
                            <h3 class="title">Student Reviews</h3>

                            <div class="course-rating">
                                <div class="rating-summary">
                                    <div class="average-rating">
                                        <?php
                                        // Get average rating if available
                                        $average_rating = 0;
                                        $rating_count = 0;

                                        if (function_exists('get_field')) {
                                            $average_rating = get_field('course_rating', get_the_ID());
                                            $rating_count = get_field('rating_count', get_the_ID());
                                        }

                                        // If no custom field, calculate from comments
                                        if (!$average_rating) {
                                            $comments = get_comments(array(
                                                'post_id' => get_the_ID(),
                                                'status' => 'approve'
                                            ));

                                            $total = 0;
                                            $count = 0;

                                            foreach ($comments as $comment) {
                                                $rating = get_comment_meta($comment->comment_ID, 'rating', true);
                                                if ($rating) {
                                                    $total += $rating;
                                                    $count++;
                                                }
                                            }

                                            if ($count > 0) {
                                                $average_rating = round($total / $count, 1);
                                                $rating_count = $count;
                                            }
                                        }

                                        // Default values if no ratings
                                        if (!$average_rating) {
                                            $average_rating = 5.0;
                                            $rating_count = 0;
                                        }
                                        ?>

                                        <div class="rating-number">
                                            <span class="average"><?php echo esc_html(number_format($average_rating, 1)); ?></span>
                                            <span class="max">/5</span>
                                        </div>

                                        <div class="rating-stars">
                                            <?php
                                            // Display stars based on rating
                                            $full_stars = floor($average_rating);
                                            $half_star = ($average_rating - $full_stars) >= 0.5;
                                            $empty_stars = 5 - $full_stars - ($half_star ? 1 : 0);

                                            // Full stars
                                            for ($i = 0; $i < $full_stars; $i++) {
                                                echo '<i class="lni lni-star-filled"></i>';
                                            }

                                            // Half star
                                            if ($half_star) {
                                                echo '<i class="lni lni-star-half"></i>';
                                            }

                                            // Empty stars
                                            for ($i = 0; $i < $empty_stars; $i++) {
                                                echo '<i class="lni lni-star"></i>';
                                            }
                                            ?>
                                        </div>

                                        <div class="rating-count">
                                            <span><?php echo esc_html($rating_count); ?> <?php echo $rating_count == 1 ? 'Review' : 'Reviews'; ?></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="course-rating-content">
                                    <!-- Comments -->
                                    <div class="post-comments">
                                        <?php
                                        if (comments_open() || get_comments_number()) :
                                            comments_template();
                                        else :
                                        ?>
                                            <div class="no-reviews-message">
                                                <i class="lni lni-comments"></i>
                                                <p>Reviews are currently closed for this course.</p>
                                            </div>
                                        <?php
                                        endif;
                                        ?>
                                    </div>
                                </div>
                            </div>

                            <div class="review-cta">
                                <p>Have you taken this course? Share your experience with other students.</p>
                                <a href="#respond" class="btn btn-outline"><i class="lni lni-pencil"></i> Write a Review</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Course Details Wrapper -->
            <!-- Start Course Sidebar -->
            <div class="col-lg-4 col-12">
                <div class="course-sidebar">
                    <!-- Course Info Widget -->
                    <div class="sidebar-widget course-info-widget">
                        <div class="course-info-header">
                            <h3 class="sidebar-widget-title"><?php echo esc_html(get_post_meta(get_the_ID(), 'course_info_title', true) ?: 'Course Information'); ?></h3>
                        </div>
                        <div class="sidebar-widget-content">
                            <ul class="course-info-list">
                                <?php
                                // Get course details
                                $duration = get_post_meta(get_the_ID(), 'course_duration', true) ?: 'Varies';
                                $level = get_post_meta(get_the_ID(), 'course_level', true) ?: 'All Levels';
                                $students = get_post_meta(get_the_ID(), 'course_students', true) ?: '0';
                                $language = get_post_meta(get_the_ID(), 'course_language', true) ?: 'Arabic';
                                $certificate = get_post_meta(get_the_ID(), 'course_certificate', true) ?: 'Yes';
                                ?>
                                <li><i class="lni lni-timer"></i> <strong>Duration:</strong> <?php echo esc_html($duration); ?></li>
                                <li><i class="lni lni-graduation"></i> <strong>Level:</strong> <?php echo esc_html($level); ?></li>
                                <li><i class="lni lni-users"></i> <strong>Students:</strong> <?php echo esc_html($students); ?></li>
                                <li><i class="lni lni-bubble"></i> <strong>Language:</strong> <?php echo esc_html($language); ?></li>
                                <li><i class="lni lni-certificate"></i> <strong>Certificate:</strong> <?php echo esc_html($certificate); ?></li>
                            </ul>

                            <div class="course-action">
                                <a href="<?php echo esc_url(home_url('/contact/')); ?>" class="btn btn-primary btn-block"><i class="lni lni-graduation"></i> Enroll Now</a>
                                <a href="<?php echo esc_url(home_url('/trial-class/')); ?>" class="btn btn-outline btn-block"><i class="lni lni-calendar"></i> Book a Trial Class</a>
                            </div>
                        </div>
                    </div>



                    <!-- Recent Courses Widget -->
                    <div class="sidebar-widget other-course-widget">
                        <h3 class="sidebar-widget-title">Related Courses</h3>
                        <div class="sidebar-widget-content">
                            <ul class="sidebar-widget-course">
                                <?php
                                // Get course categories
                                $categories = get_the_category();
                                $category_ids = array();

                                if ($categories) {
                                    foreach ($categories as $category) {
                                        $category_ids[] = $category->term_id;
                                    }
                                }

                                // Query related courses
                                $args = array(
                                    'post_type' => 'course',
                                    'posts_per_page' => 3,
                                    'post__not_in' => array(get_the_ID()),
                                    'orderby' => 'rand',
                                );

                                // Add category parameter if we have categories
                                if (!empty($category_ids)) {
                                    $args['category__in'] = $category_ids;
                                }

                                $related_courses = new WP_Query($args);

                                if ($related_courses->have_posts()) :
                                    while ($related_courses->have_posts()) : $related_courses->the_post();
                                        ?>
                                        <li class="single-course">
                                            <div class="thumbnail">
                                                <a href="<?php echo esc_url(str_replace('/courses/', '/course/', get_permalink())); ?>" class="image">
                                                    <?php if (has_post_thumbnail()) : ?>
                                                        <?php the_post_thumbnail('thumbnail', ['class' => 'img-fluid', 'alt' => get_the_title()]); ?>
                                                    <?php else : ?>
                                                        <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/images/default-course.svg" class="img-fluid" alt="<?php the_title(); ?>">
                                                    <?php endif; ?>
                                                </a>
                                            </div>
                                            <div class="info">
                                                <h6 class="title"><a href="<?php echo esc_url(str_replace('/courses/', '/course/', get_permalink())); ?>"><?php the_title(); ?></a></h6>
                                                <div class="meta">
                                                    <?php
                                                    // Get instructor if available
                                                    $instructor_id = get_post_meta(get_the_ID(), 'course_instructor', true);
                                                    if (!$instructor_id) {
                                                        $instructor_id = get_post_meta(get_the_ID(), 'course_teacher_id', true);
                                                    }

                                                    if ($instructor_id) :
                                                        $instructor_name = get_the_title($instructor_id);
                                                    ?>
                                                    <span class="instructor"><i class="lni lni-user"></i> <?php echo esc_html($instructor_name); ?></span>
                                                    <?php endif; ?>

                                                    <?php
                                                    // Get course duration if available
                                                    $duration = get_post_meta(get_the_ID(), 'course_duration', true);
                                                    if ($duration) : ?>
                                                    <span class="duration"><i class="lni lni-timer"></i> <?php echo esc_html($duration); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endwhile;
                                    wp_reset_postdata();
                                else : ?>
                                    <div class="no-courses-message">
                                        <p>No related courses available at the moment.</p>
                                    </div>
                                <?php endif; ?>
                            </ul>
                            <div class="view-all-courses">
                                <a href="<?php echo esc_url(get_post_type_archive_link('course')); ?>" class="btn btn-outline btn-sm">View All Courses <i class="lni lni-arrow-right"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Course Sidebar -->
        </div>
    </div>
</div>
<!-- Course Details Section End -->

<?php
endwhile;
endif;
get_footer();
?>