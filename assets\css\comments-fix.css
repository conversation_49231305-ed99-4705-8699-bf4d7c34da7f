/**
 * Comments Styling for Zajel Arabic Theme
 */

/* Comments Area */
.comments-area {
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    margin-top: 40px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.comments-title {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(26, 95, 141, 0.1);
    position: relative;
    color: #333;
}

.comments-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #03355c, #1a5f8d);
}

.comment-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.comment {
    margin-bottom: 30px;
}

.comment:last-child {
    margin-bottom: 0;
}

.comment-body {
    position: relative;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.comment-meta {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.comment-author {
    display: flex;
    align-items: center;
    gap: 10px;
}

.comment-author .avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.comment-author .fn {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    text-decoration: none;
    font-style: normal;
}

.comment-metadata {
    font-size: 13px;
    color: #888;
}

.comment-metadata a {
    color: #888;
    text-decoration: none;
    transition: all 0.3s ease;
}

.comment-metadata a:hover {
    color: #1a5f8d;
}

.comment-content {
    color: #666;
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 15px;
}

.comment-content p:last-child {
    margin-bottom: 0;
}

.reply {
    text-align: right;
}

.reply a {
    display: inline-flex;
    align-items: center;
    color: #fff;
    background: linear-gradient(135deg, #03355c, #1a5f8d);
    padding: 5px 15px;
    border-radius: 30px;
    font-size: 13px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.reply a:hover {
    background: linear-gradient(135deg, #1a5f8d, #03355c);
    transform: translateY(-2px);
}

/* Comment Form */
.comment-respond {
    margin-top: 40px;
    border-left: 4px solid #1a5f8d;
    padding-left: 20px;
}

.comment-reply-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(26, 95, 141, 0.1);
    position: relative;
    color: #333;
}

.comment-reply-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #03355c, #1a5f8d);
}

.comment-notes {
    margin-bottom: 20px;
    color: #888;
    font-size: 14px;
}

.comment-form-comment,
.comment-form-author,
.comment-form-email,
.comment-form-url {
    margin-bottom: 20px;
}

.comment-form-comment label,
.comment-form-author label,
.comment-form-email label,
.comment-form-url label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.comment-form input[type="text"],
.comment-form input[type="email"],
.comment-form input[type="url"],
.comment-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    font-size: 15px;
    color: #333;
    transition: all 0.3s ease;
}

.comment-form input[type="text"]:focus,
.comment-form input[type="email"]:focus,
.comment-form input[type="url"]:focus,
.comment-form textarea:focus {
    border-color: #1a5f8d;
    outline: none;
    box-shadow: 0 0 0 3px rgba(26, 95, 141, 0.1);
}

.comment-form textarea {
    height: 150px;
    resize: vertical;
}

.comment-form-cookies-consent {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.comment-form-cookies-consent label {
    margin-bottom: 0;
    font-weight: normal;
    color: #666;
    font-size: 14px;
}

.form-submit {
    text-align: left;
}

.form-submit .btn,
.form-submit input[type="submit"] {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: linear-gradient(135deg, #03355c, #1a5f8d);
    padding: 12px 25px;
    border-radius: 30px;
    font-size: 15px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.form-submit .btn:hover,
.form-submit input[type="submit"]:hover {
    background: linear-gradient(135deg, #1a5f8d, #03355c);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(26, 95, 141, 0.2);
}

/* Comment Navigation */
.comment-navigation {
    display: flex;
    justify-content: space-between;
    margin: 30px 0;
}

.comment-navigation .nav-previous,
.comment-navigation .nav-next {
    max-width: 48%;
}

.comment-navigation a {
    display: inline-flex;
    align-items: center;
    color: #333;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.comment-navigation a:hover {
    color: #1a5f8d;
}

.comment-navigation .nav-previous a:before {
    content: '←';
    margin-right: 8px;
}

.comment-navigation .nav-next a:after {
    content: '→';
    margin-left: 8px;
}

/* No Comments Message */
.no-comments {
    background-color: #f9f9f9;
    padding: 15px 20px;
    border-radius: 5px;
    color: #666;
    font-style: italic;
    margin-top: 20px;
}

/* Comment Count Display */
.post-comments-count {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #03355c, #1a5f8d);
    color: #fff;
    padding: 5px 15px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 500;
    margin-top: 30px;
    margin-bottom: 10px;
}

.post-comments-count i {
    margin-right: 5px;
}

/* Responsive Styles */
@media only screen and (max-width: 767px) {
    .comments-area {
        padding: 20px;
    }

    .comment-author .avatar {
        width: 40px;
        height: 40px;
    }

    .comment-form input[type="text"],
    .comment-form input[type="email"],
    .comment-form input[type="url"],
    .comment-form textarea {
        padding: 10px;
    }

    .form-submit .btn,
    .form-submit input[type="submit"] {
        padding: 10px 20px;
        font-size: 14px;
    }
}
